namespace CYSF.Models.Const;

/// <summary>
/// 缓存键常量
/// </summary>
public static class CacheKeys
{
    public static string Enum(string enumName) => $"Enum:{enumName}";

    public static string Token(int userId, string token) =>  $"Token:{userId}:{token}";

    public static string RefreshToken(int userId, string refreshToken) =>  $"RefreshToken:{userId}:{refreshToken}";

    public static string Tenant(int tenantId) =>  $"Tenant:{tenantId}";

    public static string TenantUser(int userId) =>  $"TenantUser:{userId}";

    public static string Role(int roleId) =>  $"TenantRole:{roleId}";

    /// <summary>
    ///     缓存30天
    /// </summary>
    public const int MonthExpired = 2592000;

    /// <summary>
    ///     缓存7天
    /// </summary>
    public const int WeekExpired = 604800;

    /// <summary>
    ///     缓存1天
    /// </summary>
    public static int DayExpired = 86400;

    /// <summary>
    ///     缓存1小时
    /// </summary>
    public static int HourExpired = 3600;

    /// <summary>
    ///     永不过期
    /// </summary>
    public static readonly int NeverExpired = -1;
}