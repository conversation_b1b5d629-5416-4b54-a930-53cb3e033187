using System;
using CYSF.Models.Entities;

namespace CYSF.Models.Response;

/// <summary>
/// 租户响应模型
/// </summary>
public class TenantDto : Tenant
{
    /// <summary>
    /// 是否已过期
    /// </summary>
    public bool IsExpired => ExpireTime.HasValue && ExpireTime.Value < DateTime.Now;

    /// <summary>
    /// 剩余天数
    /// </summary>
    public int? RemainingDays
    {
        get
        {
            if (!ExpireTime.HasValue)
                return null;

            var days = (ExpireTime.Value - DateTime.Now).Days;
            return days > 0 ? days : 0;
        }
    }

    /// <summary>
    /// 状态描述
    /// </summary>
    public string StatusDescription
    {
        get
        {
            if (IsExpired)
                return "已过期";

            if (RemainingDays.HasValue && RemainingDays.Value <= 30)
                return $"即将过期（{RemainingDays}天）";

            return "正常";
        }
    }
}
