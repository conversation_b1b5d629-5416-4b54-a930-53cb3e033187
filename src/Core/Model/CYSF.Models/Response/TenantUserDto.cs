using System;
using CYSF.Models.Entities;
using CYSF.Models.Enum;

namespace CYSF.Models.Response;

/// <summary>
/// 租户用户响应模型
/// </summary>
public class TenantUserDto : TenantUser
{
    /// <summary>
    /// 最后登录时间描述
    /// </summary>
    public string LastLoginTimeDescription
    {
        get
        {
            if (!LastLoginTime.HasValue)
                return "从未登录";

            var timeSpan = DateTime.Now.Subtract(LastLoginTime.Value);

            if (timeSpan.TotalMinutes < 1)
                return "刚刚";
            if (timeSpan.TotalMinutes < 60)
                return $"{(int)timeSpan.TotalMinutes}分钟前";
            if (timeSpan.TotalHours < 24)
                return $"{(int)timeSpan.TotalHours}小时前";
            if (timeSpan.TotalDays < 30)
                return $"{(int)timeSpan.TotalDays}天前";

            return LastLoginTime.Value.ToString("yyyy-MM-dd");
        }
    }

    /// <summary>
    /// 账户创建天数
    /// </summary>
    public int AccountAgeDays
    {
        get
        {
            return (int)DateTime.Now.Subtract(CreateTime).TotalDays;
        }
    }

    /// <summary>
    /// 角色名称（需要在应用层填充）
    /// </summary>
    public string RoleName { get; set; } = string.Empty;

    /// <summary>
    /// 租户名称（需要在应用层填充）
    /// </summary>
    public string TenantName { get; set; } = string.Empty;

    /// <summary>
    /// 创建者姓名（需要在应用层填充）
    /// </summary>
    public string CreatorName { get; set; } = string.Empty;

    /// <summary>
    /// 更新者姓名（需要在应用层填充）
    /// </summary>
    public string UpdatorName { get; set; } = string.Empty;
}
