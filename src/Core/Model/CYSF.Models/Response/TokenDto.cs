using System;

namespace CYSF.Models.Response;

[Serializable]
public class TokenDto
{
    /// <summary>
    ///     用户Id
    /// </summary>
    public int UserId { get; set; }
    
    /// <summary>
    ///     角色Id
    /// </summary>
    public int RoleId { get; set; }
    
    /// <summary>
    ///     租户Id
    /// </summary>
    public int TenantId { get; set; }

    /// <summary>
    ///     租户名称
    /// </summary>
    public string TenantName { get; set; }
    
    /// <summary>
    ///     是否为租户
    /// </summary>
    public bool IsTenant => TenantId > 0;

    /// <summary>
    ///     姓名
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     Token
    /// </summary>
    public string Token { get; set; }

    /// <summary>
    ///     RefreshToken
    /// </summary>
    public string RefreshToken { get; set; }

    /// <summary>
    ///     Token过期时间(秒)
    /// </summary>
    public int TokenExpires { get; set; }

    /// <summary>
    ///     刷新Token过期时间(秒)
    /// </summary>
    public int RefreshTokenExpires { get; set; }
}