using System.Collections.Generic;

namespace CYSF.Models.Response;

public class ListRes<T>
{
    public ListRes(int total, List<T> data, object extendData)
    {
        Total = total;
        Data = data;
        ExtendData = extendData;
    }

    public ListRes(int total, List<T> data)
    {
        Total = total;
        Data = data;
    }

    public ListRes()
    {
    }

    /// <summary>
    ///     总记录数
    /// </summary>
    public int Total { get; set; }

    /// <summary>
    ///     数据集
    /// </summary>
    public List<T> Data { get; set; }

    /// <summary>
    ///     扩展数据
    /// </summary>
    public object ExtendData { get; set; }
}