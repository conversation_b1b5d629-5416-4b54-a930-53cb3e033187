namespace CYSF.Models.Response;

public class UserInfoDto
{
    /// <summary>
    /// 租户Id, 平台管理员为0
    /// </summary>
    public int TenantId { get; set; }
    /// <summary>
    /// 租户名称
    /// </summary>
    public string TenantName { get; set; }
    /// <summary>
    /// 姓名
    /// </summary>
    public string Name { get; set; }
    /// <summary>
    /// 角色Id, 平台管理员为0
    /// </summary>
    public int RoleId { get; set; }
    /// <summary>
    /// 角色名称
    /// </summary>
    public string RoleName { get; set; }
    /// <summary>
    /// 最后登录Ip
    /// </summary>
    public string LastLoginIp { get; set; }
    /// <summary>
    /// 最后登录时间
    /// </summary>
    public DateTime? LastLoginTime { get; set; }
}