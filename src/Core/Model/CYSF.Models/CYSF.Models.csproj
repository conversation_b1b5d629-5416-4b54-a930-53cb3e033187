<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
        <LangVersion>latest</LangVersion>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
        <DocumentationFile>CYSF.Models.xml</DocumentationFile>
        <NoWarn>1701;1702;1591</NoWarn>
        <OutputPath></OutputPath>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
        <DocumentationFile>CYSF.Models.xml</DocumentationFile>
    </PropertyGroup>
    <ItemGroup>
      <PackageReference Include="FluentValidation" Version="11.4.0" />
      <PackageReference Include="SqlSugarCore" Version="5.1.4.196" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\Infrastructure\CYSF.Core\CYSF.Core.csproj" />
    </ItemGroup>

</Project>
