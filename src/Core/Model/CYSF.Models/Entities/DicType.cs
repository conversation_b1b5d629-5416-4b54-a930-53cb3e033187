using System;
using SqlSugar;
using CYSF.Models.Enum;

namespace CYSF.Models.Entities
{
    /// <summary>
    /// 字典类型表
    /// </summary>
    [Serializable]
    [SugarTable("t_dic_type", TableDescription = "字典类型表")]
    [SugarIndex("IX_DicType_Name", nameof(Name), OrderByType.Asc, true)] // 字典类型名称唯一
    public class DicType
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true, ColumnDescription = "主键ID")]
        public int Id { get; set; }

        /// <summary>
        /// 字典类型名称，全局唯一
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false, ColumnDescription = "字典类型名称，全局唯一")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 字典类型编码
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = true, ColumnDescription = "字典类型编码")]
        public string? Code { get; set; }

        /// <summary>
        /// 字典类型描述
        /// </summary>
        [SugarColumn(Length = 200, IsNullable = true, ColumnDescription = "字典类型描述")]
        public string? Description { get; set; }

        /// <summary>
        /// 排序号
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "0", ColumnDescription = "排序号")]
        public int Sort { get; set; } = 0;

        /// <summary>
        /// 是否系统类型
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "0", ColumnDescription = "是否系统类型")]
        public bool IsSystem { get; set; } = false;

        /// <summary>
        /// 创建者ID
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "0", ColumnDescription = "创建者ID")]
        public int CreatorId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnDescription = "创建时间")]
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新者ID
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "0", ColumnDescription = "更新者ID")]
        public int UpdatorId { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnDescription = "更新时间")]
        public DateTime UpdateTime { get; set; } = DateTime.Now;
    }
}
