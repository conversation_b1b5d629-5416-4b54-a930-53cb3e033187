using System;
using SqlSugar;
using CYSF.Models.Enum;

namespace CYSF.Models.Entities
{
    /// <summary>
    /// 字典类型表
    /// 数据表: t_dic_type
    /// </summary>
    [Serializable]
    [SugarTable("t_dic_type", TableName = "t_dic_type", TableDescription = "字典类型表")]
    public class DicType
    {
                /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }


        /// <summary>
        /// 字典名称
        /// 长度:50
        /// </summary>
        public string Name { get; set; } = string.Empty;


    }
}
