using System;
using SqlSugar;
using CYSF.Models.Enum;

namespace CYSF.Models.Entities
{
    /// <summary>
    /// 用户表
    /// 数据表: t_user
    /// </summary>
    [Serializable]
    [SugarTable("t_user", TableName = "t_user", TableDescription = "用户表")]
    public class TenantUser
    {
                /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }


        /// <summary>
        /// 租户Id
        /// </summary>
        public int TenantId { get; set; }


        /// <summary>
        /// 用户名
        /// 长度:20
        /// </summary>
        public string UserName { get; set; } = string.Empty;


        /// <summary>
        /// 登录密码
        /// 长度:100
        /// </summary>
        public string Password { get; set; } = string.Empty;


        /// <summary>
        /// 登录密码加盐
        /// 长度:100
        /// </summary>
        public string PasswordSalt { get; set; } = string.Empty;


        /// <summary>
        /// 角色Id
        /// </summary>
        public int RoleId { get; set; }


        /// <summary>
        /// 姓名
        /// 长度:50
        /// </summary>
        public string Name { get; set; } = string.Empty;


        /// <summary>
        /// 手机号码
        /// 长度:20
        /// </summary>
        public string Mobile { get; set; } = string.Empty;


        /// <summary>
        /// 邮箱
        /// 长度:100
        /// </summary>
        public string Email { get; set; } = string.Empty;


        /// <summary>
        /// 用户状态枚举
        /// </summary>
        public UserState UserState { get; set; }


        /// <summary>
        /// 上次登录Ip
        /// 长度:50
        /// </summary>
        public string LastLoginIp { get; set; } = string.Empty;


        /// <summary>
        /// 上次登录时间
        /// </summary>
        public DateTime? LastLoginTime { get; set; }


        /// <summary>
        /// 创建者Id
        /// </summary>
        public int CreatorId { get; set; }


        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }


        /// <summary>
        /// 更新者Id
        /// </summary>
        public int UpdatorId { get; set; }


        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }


    }
}
