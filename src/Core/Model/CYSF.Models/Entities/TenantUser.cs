using System;
using SqlSugar;
using CYSF.Models.Enum;

namespace CYSF.Models.Entities
{
    /// <summary>
    /// 租户用户表
    /// </summary>
    [Serializable]
    [SugarTable("t_user", TableDescription = "租户用户表")]
    [SugarIndex("IX_UserName", nameof(UserName), OrderByType.Asc, true)] // 租户内用户名唯一
    [SugarIndex("IX_User_Mobile", nameof(Mobile), OrderByType.Asc)] // 手机号索引
    [SugarIndex("IX_User_Email", nameof(Email), OrderByType.Asc)] // 邮箱索引
    public class TenantUser
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true, ColumnDescription = "主键ID")]
        public int Id { get; set; }

        /// <summary>
        /// 租户ID
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "0", ColumnDescription = "租户ID")]
        public int TenantId { get; set; }

        /// <summary>
        /// 用户名，租户内唯一
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false, DefaultValue = "", ColumnDescription = "用户名，租户内唯一")]
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 登录密码（加密后）
        /// </summary>
        [SugarColumn(Length = 200, IsNullable = false,DefaultValue = "", ColumnDescription = "登录密码（加密后）")]
        public string Password { get; set; } = string.Empty;

        /// <summary>
        /// 密码加盐值
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false,DefaultValue = "", ColumnDescription = "密码加盐值")]
        public string PasswordSalt { get; set; } = string.Empty;

        /// <summary>
        /// 角色ID
        /// </summary>
        [SugarColumn(IsNullable = false,DefaultValue = "0", ColumnDescription = "角色ID")]
        public int RoleId { get; set; }

        /// <summary>
        /// 真实姓名
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false,DefaultValue = "", ColumnDescription = "真实姓名")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 手机号码
        /// </summary>
        [SugarColumn(Length = 20, IsNullable = false,DefaultValue = "", ColumnDescription = "手机号码")]
        public string Mobile { get; set; } = string.Empty;

        /// <summary>
        /// 邮箱地址
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false, DefaultValue = "", ColumnDescription = "邮箱地址")]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// 用户状态
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "1", ColumnDescription = "用户状态")]
        public UserState UserState { get; set; } = UserState.Normal;

        /// <summary>
        /// 最后登录IP
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false,DefaultValue = "", ColumnDescription = "最后登录IP")]
        public string LastLoginIp { get; set; } = string.Empty;

        /// <summary>
        /// 最后登录时间
        /// </summary>
        [SugarColumn(IsNullable = true, ColumnDescription = "最后登录时间")]
        public DateTime? LastLoginTime { get; set; }

        /// <summary>
        /// 创建者ID
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "0", ColumnDescription = "创建者ID")]
        public int CreatorId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnDescription = "创建时间")]
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新者ID
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "0", ColumnDescription = "更新者ID")]
        public int UpdatorId { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnDescription = "更新时间")]
        public DateTime UpdateTime { get; set; } = DateTime.Now;
    }
}
