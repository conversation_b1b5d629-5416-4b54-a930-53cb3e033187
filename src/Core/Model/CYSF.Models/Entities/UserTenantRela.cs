using System;
using SqlSugar;
using CYSF.Models.Enum;

namespace CYSF.Models.Entities
{
    /// <summary>
    /// 平台用户租户关联表
    /// </summary>
    [Serializable]
    [SugarTable("r_user_tenant", TableDescription = "平台用户租户关联表")]
    [SugarIndex("IX_UserTenant_UserId_TenantId", nameof(UserId) + "," + nameof(TenantId), OrderByType.Asc, true)] // 用户租户关联唯一
    public class UserTenantRela
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true, ColumnDescription = "主键ID")]
        public int Id { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnDescription = "用户ID")]
        public int UserId { get; set; }

        /// <summary>
        /// 租户ID
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnDescription = "租户ID")]
        public int TenantId { get; set; }

        /// <summary>
        /// 关联状态
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "1", ColumnDescription = "关联状态")]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 创建者ID
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "0", ColumnDescription = "创建者ID")]
        public int CreatorId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnDescription = "创建时间")]
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新者ID
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "0", ColumnDescription = "更新者ID")]
        public int UpdatorId { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnDescription = "更新时间")]
        public DateTime UpdateTime { get; set; } = DateTime.Now;
    }
}
