using System;
using SqlSugar;
using CYSF.Models.Enum;

namespace CYSF.Models.Entities
{
    /// <summary>
    /// 平台用户租户关联表
    /// 数据表: r_user_tenant
    /// </summary>
    [Serializable]
    [SugarTable("r_user_tenant", TableName = "r_user_tenant", TableDescription = "平台用户租户关联表")]
    public class UserTenantRela
    {
                /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }


        /// <summary>
        /// 用户Id
        /// </summary>
        public int UserId { get; set; }


        /// <summary>
        /// 租户Id
        /// </summary>
        public int TenantId { get; set; }


    }
}
