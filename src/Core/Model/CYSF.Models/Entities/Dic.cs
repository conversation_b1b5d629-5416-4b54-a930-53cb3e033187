using System;
using SqlSugar;
using CYSF.Models.Enum;

namespace CYSF.Models.Entities
{
    /// <summary>
    /// 字典数据表
    /// </summary>
    [Serializable]
    [SugarTable("t_dic", TableDescription = "字典数据表")]
    [SugarIndex("IX_DicType_Key", nameof(DicType), OrderByType.Asc, nameof(Key), OrderByType.Asc)] // 字典类型内键值唯一
    [SugarIndex("IX_Dic_Sequence", nameof(Sequence), OrderByType.Asc)] // 排序索引
    public class Dic
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true, ColumnDescription = "主键ID")]
        public int Id { get; set; }

        /// <summary>
        /// 字典类型枚举
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "", ColumnDescription = "字典类型枚举")]
        public DicType DicType { get; set; }

        /// <summary>
        /// 字典键，类型内唯一
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false,DefaultValue = "", ColumnDescription = "字典键，类型内唯一")]
        public string Key { get; set; } = string.Empty;

        /// <summary>
        /// 字典值
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = false, DefaultValue = "", ColumnDescription = "字典值")]
        public string Value { get; set; } = string.Empty;

        /// <summary>
        /// 排序号
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "0", ColumnDescription = "排序号")]
        public int Sequence { get; set; } = 0;

        /// <summary>
        /// 字典描述
        /// </summary>
        [SugarColumn(Length = 200, IsNullable = false,DefaultValue = "", ColumnDescription = "字典描述")]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 是否只读
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "0", ColumnDescription = "是否只读")]
        public bool IsReadonly { get; set; } = false;

        /// <summary>
        /// 是否系统字典
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "0", ColumnDescription = "是否系统字典")]
        public bool IsSystem { get; set; } = false;

        /// <summary>
        /// 创建者ID
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "0", ColumnDescription = "创建者ID")]
        public int CreatorId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnDescription = "创建时间")]
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新者ID
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "0", ColumnDescription = "更新者ID")]
        public int UpdatorId { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnDescription = "更新时间")]
        public DateTime UpdateTime { get; set; }
    }
}
