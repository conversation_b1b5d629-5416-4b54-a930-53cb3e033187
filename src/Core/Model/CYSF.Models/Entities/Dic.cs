using System;
using SqlSugar;
using CYSF.Models.Enum;

namespace CYSF.Models.Entities
{
    /// <summary>
    /// 字典表
    /// 数据表: t_dic
    /// </summary>
    [Serializable]
    [SugarTable("t_dic", TableName = "t_dic", TableDescription = "字典表")]
    public class Dic
    {
                /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }


        /// <summary>
        /// 字典类型Id
        /// </summary>
        public int DicTypeId { get; set; }


        /// <summary>
        /// 键
        /// 长度:50
        /// </summary>
        public string Key { get; set; } = string.Empty;


        /// <summary>
        /// 值
        /// 长度:100
        /// </summary>
        public string Value { get; set; } = string.Empty;


        /// <summary>
        /// 顺序
        /// </summary>
        public int Sequence { get; set; }


        /// <summary>
        /// 描述
        /// 长度:200
        /// </summary>
        public string Description { get; set; } = string.Empty;


        /// <summary>
        /// 是否只读
        /// </summary>
        public bool IsReadonly { get; set; }


        /// <summary>
        /// 系统枚举标识
        /// </summary>
        public bool IsSystem { get; set; }


        /// <summary>
        /// 创建者Id
        /// </summary>
        public int CreatorId { get; set; }


        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }


        /// <summary>
        /// 更新者Id
        /// </summary>
        public int UpdatorId { get; set; }


        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }


    }
}
