using System;
using SqlSugar;
using CYSF.Models.Enum;

namespace CYSF.Models.Entities
{
    /// <summary>
    /// 租户角色表
    /// </summary>
    [Serializable]
    [SugarTable("t_role", TableDescription = "租户角色表")]
    [SugarIndex("IX_TenantId", nameof(TenantId), OrderByType.Asc)]
    [SugarIndex("IX_Role_TenantId_Name", nameof(TenantId) + "," + nameof(Name), OrderByType.Asc, true)] // 租户内角色名唯一
    public class TenantRole
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true, ColumnDescription = "主键ID")]
        public int Id { get; set; }

        /// <summary>
        /// 租户ID
        /// </summary>
        [SugarColumn(IsNullable = false,DefaultValue= "0", ColumnDescription = "租户ID")]
        public int TenantId { get; set; }

        /// <summary>
        /// 角色名称，租户内唯一
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false,DefaultValue = "", ColumnDescription = "角色名称，租户内唯一")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 角色描述
        /// </summary>
        [SugarColumn(Length = 200, IsNullable = false,DefaultValue = "", ColumnDescription = "角色描述")]
        public string Description { get; set; }= string.Empty;

        /// <summary>
        /// 是否系统角色
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "0", ColumnDescription = "是否系统角色")]
        public bool IsSystem { get; set; } = false;

        /// <summary>
        /// 排序号
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "0", ColumnDescription = "排序号")]
        public int Sort { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "1", ColumnDescription = "是否启用")]
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 创建者ID
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "0", ColumnDescription = "创建者ID")]
        public int CreatorId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = true, ColumnDescription = "创建时间")]
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新者ID
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "0", ColumnDescription = "更新者ID")]
        public int UpdatorId { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = true, ColumnDescription = "更新时间")]
        public DateTime UpdateTime { get; set; }
    }
}
