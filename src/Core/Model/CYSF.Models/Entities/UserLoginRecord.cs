using System;
using SqlSugar;
using CYSF.Models.Enum;

namespace CYSF.Models.Entities
{
    /// <summary>
    /// 用户登录记录表
    /// </summary>
    [Serializable]
    [SugarTable("rec_user_login", TableDescription = "用户登录记录表")]
    [SugarIndex("IX_LoginRecord_TenantId_UserId", nameof(TenantId) + "," + nameof(UserId), OrderByType.Desc)] // 按租户用户查询索引
    [SugarIndex("IX_LoginRecord_LoginTime", nameof(LoginTime), OrderByType.Desc)] // 按登录时间查询索引
    public class UserLoginRecord
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true, ColumnDescription = "主键ID")]
        public int Id { get; set; }

        /// <summary>
        /// 租户ID
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnDescription = "租户ID")]
        public int TenantId { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnDescription = "用户ID")]
        public int UserId { get; set; }

        /// <summary>
        /// 登录IP地址
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = true, ColumnDescription = "登录IP地址")]
        public string? LoginIp { get; set; }

        /// <summary>
        /// 登录描述（浏览器、设备等信息）
        /// </summary>
        [SugarColumn(Length = 500, IsNullable = true, ColumnDescription = "登录描述（浏览器、设备等信息）")]
        public string? Description { get; set; }

        /// <summary>
        /// 登录时间
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnDescription = "登录时间")]
        public DateTime LoginTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 登录结果
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "1", ColumnDescription = "登录结果")]
        public bool IsSuccess { get; set; } = true;

        /// <summary>
        /// 失败原因
        /// </summary>
        [SugarColumn(Length = 200, IsNullable = true, ColumnDescription = "失败原因")]
        public string? FailureReason { get; set; }
    }
}
