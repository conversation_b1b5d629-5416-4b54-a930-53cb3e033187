using System;
using SqlSugar;
using CYSF.Models.Enum;

namespace CYSF.Models.Entities
{
    /// <summary>
    /// 用户登录记录表
    /// 数据表: rec_user_login
    /// </summary>
    [Serializable]
    [SugarTable("rec_user_login", TableName = "rec_user_login", TableDescription = "用户登录记录表")]
    public class UserLoginRecord
    {
                /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }


        /// <summary>
        /// 租户Id
        /// </summary>
        public int TenantId { get; set; }


        /// <summary>
        /// 用户Id
        /// </summary>
        public int UserId { get; set; }


        /// <summary>
        /// 登录Ip
        /// 长度:50
        /// </summary>
        public string LoginIp { get; set; } = string.Empty;


        /// <summary>
        /// 描述(浏览器版本、前端版本等)
        /// 长度:200
        /// </summary>
        public string Description { get; set; } = string.Empty;


        /// <summary>
        /// 登录/记录时间
        /// </summary>
        public DateTime? RecordTime { get; set; }


    }
}
