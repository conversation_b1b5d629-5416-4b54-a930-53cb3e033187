using System;
using SqlSugar;
using CYSF.Models.Enum;

namespace CYSF.Models.Entities
{
    /// <summary>
    /// 租户表
    /// 数据表: t_tenant
    /// </summary>
    [Serializable]
    [SugarTable("t_tenant", TableName = "t_tenant", TableDescription = "租户表")]
    public class Tenant
    {
                /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }


        /// <summary>
        /// 编号
        /// 长度:50
        /// </summary>
        public string Code { get; set; } = string.Empty;


        /// <summary>
        /// 简称
        /// 长度:200
        /// </summary>
        public string ShortName { get; set; } = string.Empty;


        /// <summary>
        /// 公司名称
        /// 长度:200
        /// </summary>
        public string CompanyName { get; set; } = string.Empty;


        /// <summary>
        /// 联系人姓名
        /// 长度:50
        /// </summary>
        public string ContactName { get; set; } = string.Empty;


        /// <summary>
        /// 联系人电话
        /// 长度:50
        /// </summary>
        public string ContactPhone { get; set; } = string.Empty;


        /// <summary>
        /// 租户等级(字典)
        /// 长度:20
        /// </summary>
        public string Level { get; set; } = string.Empty;


        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime? ExpireTime { get; set; }


        /// <summary>
        /// 软删除标识
        /// </summary>
        public bool IsDeleted { get; set; }


        /// <summary>
        /// 创建者Id
        /// </summary>
        public int CreatorId { get; set; }


        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }


        /// <summary>
        /// 更新者Id
        /// </summary>
        public int UpdatorId { get; set; }


        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }


    }
}
