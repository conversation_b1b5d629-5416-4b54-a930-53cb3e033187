using System;
using SqlSugar;
using CYSF.Models.Enum;

namespace CYSF.Models.Entities
{
    /// <summary>
    /// 租户表
    /// </summary>
    [Serializable]
    [SugarTable("t_tenant", TableDescription = "租户表")]
    [SugarIndex("IX_Tenant_Code", nameof(Code), OrderByType.Asc, true)] // 租户编码唯一索引
    public class Tenant
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true, ColumnDescription = "主键ID")]
        public int Id { get; set; }

        /// <summary>
        /// 租户编码，唯一标识
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = false, ColumnDescription = "租户编码，唯一标识")]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 租户名称
        /// </summary>
        [SugarColumn(Length = 200, IsNullable = false, ColumnDescription = "租户名称")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 租户简称
        /// </summary>
        [SugarColumn(Length = 100, IsNullable = true, ColumnDescription = "租户简称")]
        public string? ShortName { get; set; }

        /// <summary>
        /// 公司名称
        /// </summary>
        [SugarColumn(Length = 200, IsNullable = true, ColumnDescription = "公司名称")]
        public string? CompanyName { get; set; }

        /// <summary>
        /// 联系人姓名
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = true, ColumnDescription = "联系人姓名")]
        public string? ContactName { get; set; }

        /// <summary>
        /// 联系人电话
        /// </summary>
        [SugarColumn(Length = 50, IsNullable = true, ColumnDescription = "联系人电话")]
        public string? ContactPhone { get; set; }

        /// <summary>
        /// 租户等级(字典值)
        /// </summary>
        [SugarColumn(Length = 20, IsNullable = false, DefaultValue = "STANDARD", ColumnDescription = "租户等级")]
        public string Level { get; set; } = "STANDARD";

        /// <summary>
        /// 租户状态
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "1", ColumnDescription = "租户状态")]
        public TenantStatus Status { get; set; } = TenantStatus.Normal;

        /// <summary>
        /// 过期时间
        /// </summary>
        [SugarColumn(IsNullable = true, ColumnDescription = "过期时间")]
        public DateTime? ExpireTime { get; set; }

        /// <summary>
        /// 软删除标识
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "0", ColumnDescription = "软删除标识")]
        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// 创建者ID
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "0", ColumnDescription = "创建者ID")]
        public int CreatorId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnDescription = "创建时间")]
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新者ID
        /// </summary>
        [SugarColumn(IsNullable = false, DefaultValue = "0", ColumnDescription = "更新者ID")]
        public int UpdatorId { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [SugarColumn(IsNullable = false, ColumnDescription = "更新时间")]
        public DateTime UpdateTime { get; set; } = DateTime.Now;
    }
}
