using System;
using SqlSugar;
using CYSF.Models.Enum;

namespace CYSF.Models.Entities
{
    /// <summary>
    /// 租户角色表
    /// 数据表: t_role
    /// </summary>
    [Serializable]
    [SugarTable("t_role", TableName = "t_role", TableDescription = "租户角色表")]
    public class Role
    {
                /// <summary>
        /// 主键
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }


        /// <summary>
        /// 租户Id
        /// </summary>
        public int TenantId { get; set; }


        /// <summary>
        /// 角色名称
        /// 长度:50
        /// </summary>
        public string Name { get; set; } = string.Empty;


        /// <summary>
        /// 描述
        /// 长度:200
        /// </summary>
        public string Description { get; set; } = string.Empty;


        /// <summary>
        /// 创建者Id
        /// </summary>
        public int CreatorId { get; set; }


        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreateTime { get; set; }


        /// <summary>
        /// 更新者Id
        /// </summary>
        public int UpdatorId { get; set; }


        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }


    }
}
