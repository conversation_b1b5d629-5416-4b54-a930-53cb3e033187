using FluentValidation;
using CYSF.Models.Validators.Common;

namespace CYSF.Models.Request.Role;

/// <summary>
/// 创建角色请求模型
/// </summary>
public class CreateTenantRoleReq
{
    /// <summary>
    /// 租户ID（平台管理员创建时需要指定，租户管理员创建时自动设置）
    /// </summary>
    public int TenantId { get; set; }

    /// <summary>
    /// 角色名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 角色描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;
}

/// <summary>
/// 创建角色请求验证器
/// </summary>
public class CreateTenantRoleReqValidator : BaseValidator<CreateTenantRoleReq>
{
    public CreateTenantRoleReqValidator()
    {
        // 角色名称验证
        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("角色名称不能为空")
            .Length(2, 50)
            .WithMessage("角色名称长度必须在2-50个字符之间");

        // 角色描述验证
        RuleFor(x => x.Description)
            .MaximumLength(200)
            .WithMessage("角色描述长度不能超过200个字符")
            .When(x => !string.IsNullOrEmpty(x.Description));

        // 排序验证
        RuleFor(x => x.Sort)
            .GreaterThanOrEqualTo(0)
            .WithMessage("排序值不能小于0");
    }
}
