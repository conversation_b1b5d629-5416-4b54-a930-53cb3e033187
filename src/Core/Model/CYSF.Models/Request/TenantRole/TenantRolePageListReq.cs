namespace CYSF.Models.Request.TenantRole;

/// <summary>
/// TenantRole分页查询请求
/// </summary>
public class TenantRolePageListReq
{
    /// <summary>
    /// ID
    /// </summary>
    public int? Id { get; set; }

    /// <summary>
    /// 创建时间范围
    /// </summary>
    public List<DateTime>? CreateTime { get; set; }

    /// <summary>
    /// 更新时间范围
    /// </summary>
    public List<DateTime>? UpdateTime { get; set; }

    /// <summary>
    /// TenantId
    /// </summary>
    public int? TenantId { get; set; }

    /// <summary>
    /// Name
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Sort
    /// </summary>
    public int? Sort { get; set; }

    /// <summary>
    /// UpdatorId
    /// </summary>
    public int? UpdatorId { get; set; }
}
