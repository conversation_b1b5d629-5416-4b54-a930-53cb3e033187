using System;
using System.Collections.Generic;

namespace CYSF.Models.Request.Role;

/// <summary>
/// 角色列表查询请求模型
/// </summary>
public class RolePageListReq
{
    /// <summary>
    /// 角色名称筛选
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 租户ID筛选（平台管理员可以指定，租户管理员自动过滤）
    /// </summary>
    public int? TenantId { get; set; }

    /// <summary>
    /// 是否启用筛选
    /// </summary>
    public bool? IsEnabled { get; set; }

    /// <summary>
    /// 创建时间范围（第一个元素为开始时间，第二个元素为结束时间）
    /// </summary>
    public List<DateTime> CreateTime { get; set; } = new List<DateTime>();

    /// <summary>
    /// 更新时间范围（第一个元素为开始时间，第二个元素为结束时间）
    /// </summary>
    public List<DateTime> UpdateTime { get; set; } = new List<DateTime>();
}
