using CYSF.Models.Validators.Common;
using FluentValidation;

namespace CYSF.Models.Request.TenantRole;

/// <summary>
/// 更新角色请求模型
/// </summary>
public class UpdateTenantRoleReq
{
    /// <summary>
    /// 角色ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 角色名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 角色描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;
}

/// <summary>
/// 更新角色请求验证器
/// </summary>
public class UpdateRoleReqValidator : BaseValidator<UpdateTenantRoleReq>
{
    public UpdateRoleReqValidator()
    {
        // 角色ID验证
        RuleFor(x => x.Id)
            .GreaterThan(0)
            .WithMessage("角色ID必须大于0");

        // 角色名称验证
        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("角色名称不能为空")
            .Length(2, 50)
            .WithMessage("角色名称长度必须在2-50个字符之间");

        // 角色描述验证
        RuleFor(x => x.Description)
            .MaximumLength(200)
            .WithMessage("角色描述长度不能超过200个字符")
            .When(x => !string.IsNullOrEmpty(x.Description));

        // 排序验证
        RuleFor(x => x.Sort)
            .GreaterThanOrEqualTo(0)
            .WithMessage("排序值不能小于0");
    }
}
