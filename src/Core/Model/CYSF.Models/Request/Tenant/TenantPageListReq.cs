namespace CYSF.Models.Request.Tenant;

/// <summary>
/// Tenant分页查询请求
/// </summary>
public class TenantPageListReq
{
    /// <summary>
    /// ID
    /// </summary>
    public int? Id { get; set; }

    /// <summary>
    /// 创建时间范围
    /// </summary>
    public List<DateTime>? CreateTime { get; set; }

    /// <summary>
    /// 更新时间范围
    /// </summary>
    public List<DateTime>? UpdateTime { get; set; }

    /// <summary>
    /// Code
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// Name
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// ShortName
    /// </summary>
    public string? ShortName { get; set; }

    /// <summary>
    /// CompanyName
    /// </summary>
    public string? CompanyName { get; set; }

    /// <summary>
    /// ContactName
    /// </summary>
    public string? ContactName { get; set; }

    /// <summary>
    /// ContactPhone
    /// </summary>
    public string? ContactPhone { get; set; }

    /// <summary>
    /// DicLevel
    /// </summary>
    public string? DicLevel { get; set; }

    /// <summary>
    /// ExpireTime范围
    /// </summary>
    public List<DateTime>? ExpireTime { get; set; }

    /// <summary>
    /// UpdatorId
    /// </summary>
    public int? UpdatorId { get; set; }
}
