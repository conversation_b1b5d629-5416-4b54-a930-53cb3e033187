using System;
using FluentValidation;
using CYSF.Core.Extensions;
using CYSF.Models.Validators.Common;
using CYSF.Models.Enum;

namespace CYSF.Models.Request.Tenant;

/// <summary>
/// 更新租户请求模型
/// </summary>
public class UpdateTenantReq
{
    /// <summary>
    /// 租户ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 租户编号
    /// </summary>
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// 租户简称
    /// </summary>
    public string ShortName { get; set; } = string.Empty;

    /// <summary>
    /// 公司名称
    /// </summary>
    public string CompanyName { get; set; } = string.Empty;

    /// <summary>
    /// 联系人姓名
    /// </summary>
    public string ContactName { get; set; } = string.Empty;

    /// <summary>
    /// 联系人电话
    /// </summary>
    public string ContactPhone { get; set; } = string.Empty;

    /// <summary>
    /// 租户等级
    /// </summary>
    public Level Level { get; set; }

    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime? ExpireTime { get; set; }
}

/// <summary>
/// 更新租户请求验证器 - 继承基础验证器，自动支持快速失败模式
/// </summary>
public class UpdateTenantReqValidator : BaseValidator<UpdateTenantReq>
{
    public UpdateTenantReqValidator()
    {
        // 租户ID验证
        RuleFor(x => x.Id)
            .GreaterThan(0)
            .WithMessage("租户ID必须大于0");

        // 租户编号验证
        RuleFor(x => x.Code)
            .NotEmpty()
            .WithMessage("租户编号不能为空")
            .Length(2, 50)
            .WithMessage("租户编号长度必须在2-50个字符之间")
            .Must(BeValidCode)
            .WithMessage("租户编号只能包含字母、数字和下划线，且必须以字母开头");

        // 租户简称验证
        RuleFor(x => x.ShortName)
            .NotEmpty()
            .WithMessage("租户简称不能为空")
            .Length(2, 200)
            .WithMessage("租户简称长度必须在2-200个字符之间");

        // 公司名称验证
        RuleFor(x => x.CompanyName)
            .NotEmpty()
            .WithMessage("公司名称不能为空")
            .Length(2, 200)
            .WithMessage("公司名称长度必须在2-200个字符之间");

        // 联系人姓名验证
        RuleFor(x => x.ContactName)
            .NotEmpty()
            .WithMessage("联系人姓名不能为空")
            .Length(2, 50)
            .WithMessage("联系人姓名长度必须在2-50个字符之间");

        // 联系人电话验证
        RuleFor(x => x.ContactPhone)
            .NotEmpty()
            .WithMessage("联系人电话不能为空")
            .Must(BeValidPhone)
            .WithMessage("请输入有效的联系人电话");

        // 租户等级验证
        RuleFor(x => x.Level)
            .IsInEnum()
            .WithMessage("请选择有效的租户等级");

        // 过期时间验证
        RuleFor(x => x.ExpireTime)
            .Must(BeValidExpireTime)
            .WithMessage("过期时间必须大于当前时间")
            .When(x => x.ExpireTime.HasValue);
    }

    /// <summary>
    /// 验证租户编号格式
    /// </summary>
    /// <param name="code">租户编号</param>
    /// <returns>是否有效</returns>
    private bool BeValidCode(string code)
    {
        if (string.IsNullOrWhiteSpace(code))
            return false;

        // 不能包含空格
        if (code.Contains(" "))
            return false;

        // 必须以字母开头，只能包含字母、数字和下划线
        return code.IsMatch(@"^[a-zA-Z][a-zA-Z0-9_]*$");
    }

    /// <summary>
    /// 验证联系人电话格式
    /// </summary>
    /// <param name="phone">电话号码</param>
    /// <returns>是否有效</returns>
    private bool BeValidPhone(string phone)
    {
        if (string.IsNullOrWhiteSpace(phone))
            return false;

        // 支持手机号和固定电话
        return phone.IsMatch(RegExpressions.mobile) || 
               phone.IsMatch(@"^0\d{2,3}-?\d{7,8}$"); // 固定电话格式
    }

    /// <summary>
    /// 验证过期时间
    /// </summary>
    /// <param name="expireTime">过期时间</param>
    /// <returns>是否有效</returns>
    private bool BeValidExpireTime(DateTime? expireTime)
    {
        if (!expireTime.HasValue)
            return true;

        return expireTime.Value > DateTime.Now;
    }
}
