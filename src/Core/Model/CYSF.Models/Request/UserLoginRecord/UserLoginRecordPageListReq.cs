namespace CYSF.Models.Request.UserLoginRecord;

/// <summary>
/// UserLoginRecord分页查询请求
/// </summary>
public class UserLoginRecordPageListReq
{
    /// <summary>
    /// ID
    /// </summary>
    public int? Id { get; set; }

    /// <summary>
    /// 创建时间范围
    /// </summary>
    public List<DateTime>? CreateTime { get; set; }

    /// <summary>
    /// 更新时间范围
    /// </summary>
    public List<DateTime>? UpdateTime { get; set; }

    /// <summary>
    /// TenantId
    /// </summary>
    public int? TenantId { get; set; }

    /// <summary>
    /// UserId
    /// </summary>
    public int? UserId { get; set; }

    /// <summary>
    /// LoginIp
    /// </summary>
    public string? LoginIp { get; set; }

    /// <summary>
    /// Description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// RecordTime范围
    /// </summary>
    public List<DateTime>? RecordTime { get; set; }
}
