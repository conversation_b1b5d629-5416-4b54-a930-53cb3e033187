using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using CYSF.Core.Extensions;

namespace CYSF.Models.Request
{
    /// <summary>
    ///     分页请求参数实体
    /// </summary>
    public class PageListReq<T>
    {
        /// <summary>
        ///     分页参数
        /// </summary>
        public Pagination Pagination { get; set; }

        /// <summary>
        ///     排序参数
        /// </summary>
        public List<PageSort> Sorts { get; set; } = new List<PageSort>();

        public bool IsSortEmpty
        {
            get { return Sorts == null || Sorts.Count == 0 || !Sorts.Any(r => r.SortField.IsNotNullOrEmpty()); }
        }

        /// <summary>
        ///     筛选实体
        /// </summary>
        public T Filters { get; set; }
    }

    /// <summary>
    ///     无分页列表请求参数实体
    /// </summary>
    /// <typeparam name="T">请求参数实体</typeparam>
    public class ListReq<T>
    {
        /// <summary>
        ///     排序参数
        /// </summary>
        public List<PageSort> Sorts { get; set; }

        /// <summary>
        ///     筛选实体
        /// </summary>
        public T Filters { get; set; }
    }

    /// <summary>
    ///     分页请求参数实体
    /// </summary>
    public class Pagination
    {
        /// <summary>
        /// </summary>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        public Pagination(int pageIndex, int pageSize)
        {
            PageIndex = pageIndex;
            PageSize = pageSize;
        }

        /// <summary>
        /// </summary>
        public Pagination()
        {
        }

        /// <summary>
        ///     页索引
        /// </summary>
        [DefaultValue(1)]
        public int PageIndex { get; set; } = 1;

        /// <summary>
        ///     页记录数
        /// </summary>
        [DefaultValue(10)]
        public int PageSize { get; set; } = 10;
    }

    /// <summary>
    ///     排序请求参数实体
    /// </summary>
    public class PageSort
    {
        public PageSort()
        {
        }

        public PageSort(string sortField, bool orderDesc)
        {
            SortField = sortField;
            OrderDesc = orderDesc;
        }

        /// <summary>
        ///     排序字段
        /// </summary>
        [DefaultValue("")]
        public string SortField { get; set; }

        /// <summary>
        ///     倒序标识
        /// </summary>
        public bool OrderDesc { get; set; }
    }
}