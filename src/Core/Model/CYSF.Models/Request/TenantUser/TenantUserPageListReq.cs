using System;
using System.Collections.Generic;
using CYSF.Models.Enum;

namespace CYSF.Models.Request.TenantUser;

/// <summary>
/// 租户用户列表查询请求模型
/// </summary>
public class TenantUserPageListReq
{
    /// <summary>
    /// 用户名筛选
    /// </summary>
    public string UserName { get; set; } = string.Empty;

    /// <summary>
    /// 姓名筛选
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 手机号筛选
    /// </summary>
    public string Mobile { get; set; } = string.Empty;

    /// <summary>
    /// 邮箱筛选
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// 租户ID筛选
    /// </summary>
    public int? TenantId { get; set; }

    /// <summary>
    /// 用户状态筛选
    /// </summary>
    public UserState? UserState { get; set; }

    /// <summary>
    /// 角色ID筛选
    /// </summary>
    public int? RoleId { get; set; }

    /// <summary>
    /// 创建时间范围（第一个元素为开始时间，第二个元素为结束时间）
    /// </summary>
    public List<DateTime> CreateTime { get; set; }

    /// <summary>
    /// 最后登录时间范围（第一个元素为开始时间，第二个元素为结束时间）
    /// </summary>
    public List<DateTime> LastLoginTime { get; set; }
}
