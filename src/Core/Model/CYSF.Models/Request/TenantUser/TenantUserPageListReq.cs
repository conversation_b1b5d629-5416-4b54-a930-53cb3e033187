namespace CYSF.Models.Request.TenantUser;

/// <summary>
/// TenantUser分页查询请求
/// </summary>
public class TenantUserPageListReq
{
    /// <summary>
    /// ID
    /// </summary>
    public int? Id { get; set; }

    /// <summary>
    /// 创建时间范围
    /// </summary>
    public List<DateTime>? CreateTime { get; set; }

    /// <summary>
    /// 更新时间范围
    /// </summary>
    public List<DateTime>? UpdateTime { get; set; }

    /// <summary>
    /// TenantId
    /// </summary>
    public int? TenantId { get; set; }

    /// <summary>
    /// UserName
    /// </summary>
    public string? UserName { get; set; }

    /// <summary>
    /// Password
    /// </summary>
    public string? Password { get; set; }

    /// <summary>
    /// PasswordSalt
    /// </summary>
    public string? PasswordSalt { get; set; }

    /// <summary>
    /// RoleId
    /// </summary>
    public int? RoleId { get; set; }

    /// <summary>
    /// Name
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Mobile
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// Email
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// LastLoginIp
    /// </summary>
    public string? LastLoginIp { get; set; }

    /// <summary>
    /// LastLoginTime范围
    /// </summary>
    public List<DateTime>? LastLoginTime { get; set; }

    /// <summary>
    /// UpdatorId
    /// </summary>
    public int? UpdatorId { get; set; }
}
