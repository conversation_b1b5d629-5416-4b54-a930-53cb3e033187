using FluentValidation;
using CYSF.Models.Validators.Common;
using CYSF.Models.Enum;
using System.Text.RegularExpressions;

namespace CYSF.Models.Request.TenantUser;

/// <summary>
/// 创建租户用户请求模型
/// </summary>
public class CreateTenantUserReq
{
    /// <summary>
    /// 租户ID
    /// </summary>
    public int TenantId { get; set; }

    /// <summary>
    /// 用户名
    /// </summary>
    public string UserName { get; set; } = string.Empty;

    /// <summary>
    /// 姓名
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 手机号码
    /// </summary>
    public string Mobile { get; set; } = string.Empty;

    /// <summary>
    /// 邮箱
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// 密码
    /// </summary>
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// 确认密码
    /// </summary>
    public string ConfirmPassword { get; set; } = string.Empty;

    /// <summary>
    /// 用户状态
    /// </summary>
    public UserState UserState { get; set; } = UserState.Normal;

    /// <summary>
    /// 角色ID
    /// </summary>
    public int RoleId { get; set; }
}

/// <summary>
/// 创建租户用户请求验证器 - 继承基础验证器，自动支持快速失败模式
/// </summary>
public class CreateTenantUserReqValidator : BaseValidator<CreateTenantUserReq>
{
    public CreateTenantUserReqValidator()
    {
        // 租户ID验证
        RuleFor(x => x.TenantId)
            .GreaterThan(0)
            .WithMessage("租户ID必须大于0");

        // 用户名验证
        RuleFor(x => x.UserName)
            .NotEmpty()
            .WithMessage("用户名不能为空")
            .Length(3, 20)
            .WithMessage("用户名长度必须在3-20个字符之间")
            .Must(BeValidUserName)
            .WithMessage("用户名只能包含字母、数字和下划线，且必须以字母开头");

        // 姓名验证
        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("姓名不能为空")
            .Length(2, 50)
            .WithMessage("姓名长度必须在2-50个字符之间");

        // 手机号验证
        RuleFor(x => x.Mobile)
            .NotEmpty()
            .WithMessage("手机号不能为空")
            .Must(BeValidMobile)
            .WithMessage("请输入有效的手机号");

        // 邮箱验证
        RuleFor(x => x.Email)
            .EmailAddress()
            .WithMessage("请输入有效的邮箱地址")
            .When(x => !string.IsNullOrEmpty(x.Email));

        // 密码验证
        RuleFor(x => x.Password)
            .NotEmpty()
            .WithMessage("密码不能为空")
            .MinimumLength(6)
            .WithMessage("密码长度不能少于6位")
            .MaximumLength(20)
            .WithMessage("密码长度不能超过20位")
            .Must(BeValidPassword)
            .WithMessage("密码必须包含字母和数字");

        // 确认密码验证
        RuleFor(x => x.ConfirmPassword)
            .NotEmpty()
            .WithMessage("确认密码不能为空")
            .Equal(x => x.Password)
            .WithMessage("确认密码与密码不一致");

        // 用户状态验证
        RuleFor(x => x.UserState)
            .IsInEnum()
            .WithMessage("请选择有效的用户状态");

        // 角色ID验证
        RuleFor(x => x.RoleId)
            .GreaterThan(0)
            .WithMessage("角色ID必须大于0");
    }

    /// <summary>
    /// 验证用户名格式是否有效
    /// </summary>
    /// <param name="userName">用户名</param>
    /// <returns>是否有效</returns>
    private static bool BeValidUserName(string userName)
    {
        if (string.IsNullOrWhiteSpace(userName))
            return false;

        // 用户名只能包含字母、数字和下划线，且必须以字母开头
        return Regex.IsMatch(userName, @"^[a-zA-Z][a-zA-Z0-9_]*$");
    }

    /// <summary>
    /// 验证手机号格式是否有效
    /// </summary>
    /// <param name="mobile">手机号</param>
    /// <returns>是否有效</returns>
    private static bool BeValidMobile(string mobile)
    {
        if (string.IsNullOrWhiteSpace(mobile))
            return false;

        // 中国大陆手机号验证
        return Regex.IsMatch(mobile, @"^1[3-9]\d{9}$");
    }

    /// <summary>
    /// 验证密码格式是否有效
    /// </summary>
    /// <param name="password">密码</param>
    /// <returns>是否有效</returns>
    private static bool BeValidPassword(string password)
    {
        if (string.IsNullOrWhiteSpace(password))
            return false;

        // 密码必须包含字母和数字
        return Regex.IsMatch(password, @"^(?=.*[a-zA-Z])(?=.*\d).+$");
    }
}
