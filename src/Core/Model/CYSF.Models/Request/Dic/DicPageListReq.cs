namespace CYSF.Models.Request.Dic;

/// <summary>
/// Dic分页查询请求
/// </summary>
public class DicPageListReq
{
    /// <summary>
    /// ID
    /// </summary>
    public int? Id { get; set; }

    /// <summary>
    /// 创建时间范围
    /// </summary>
    public List<DateTime>? CreateTime { get; set; }

    /// <summary>
    /// 更新时间范围
    /// </summary>
    public List<DateTime>? UpdateTime { get; set; }

    /// <summary>
    /// Key
    /// </summary>
    public string? Key { get; set; }

    /// <summary>
    /// Value
    /// </summary>
    public string? Value { get; set; }

    /// <summary>
    /// Sequence
    /// </summary>
    public int? Sequence { get; set; }

    /// <summary>
    /// Description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// UpdatorId
    /// </summary>
    public int? UpdatorId { get; set; }
}
