using FluentValidation;

namespace CYSF.Models.Validators.Common;

/// <summary>
/// 基础验证器 - 统一配置快速失败模式
/// </summary>
/// <typeparam name="T">要验证的模型类型</typeparam>
public abstract class BaseValidator<T> : AbstractValidator<T>
{
    protected BaseValidator()
    {
        // 统一配置快速失败模式
        // ClassLevelCascadeMode: 控制不同属性之间的验证行为
        // RuleLevelCascadeMode: 控制同一属性内不同规则之间的验证行为
        ClassLevelCascadeMode = CascadeMode.Stop;
        RuleLevelCascadeMode = CascadeMode.Stop;
    }
}
