<?xml version="1.0"?>
<doc>
    <assembly>
        <name>CYSF.Models</name>
    </assembly>
    <members>
        <member name="T:CYSF.Models.Const.CacheKeys">
            <summary>
            缓存键常量
            </summary>
        </member>
        <member name="F:CYSF.Models.Const.CacheKeys.MonthExpired">
            <summary>
                缓存30天
            </summary>
        </member>
        <member name="F:CYSF.Models.Const.CacheKeys.WeekExpired">
            <summary>
                缓存7天
            </summary>
        </member>
        <member name="F:CYSF.Models.Const.CacheKeys.DayExpired">
            <summary>
                缓存1天
            </summary>
        </member>
        <member name="F:CYSF.Models.Const.CacheKeys.HourExpired">
            <summary>
                缓存1小时
            </summary>
        </member>
        <member name="F:CYSF.Models.Const.CacheKeys.NeverExpired">
            <summary>
                永不过期
            </summary>
        </member>
        <member name="T:CYSF.Models.Entities.Dic">
            <summary>
            字典数据表
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.Dic.Id">
            <summary>
            主键ID
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.Dic.DicType">
            <summary>
            字典类型枚举
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.Dic.Key">
            <summary>
            字典键，类型内唯一
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.Dic.Value">
            <summary>
            字典值
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.Dic.Sequence">
            <summary>
            排序号
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.Dic.Description">
            <summary>
            字典描述
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.Dic.IsReadonly">
            <summary>
            是否只读
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.Dic.IsSystem">
            <summary>
            是否系统字典
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.Dic.CreatorId">
            <summary>
            创建者ID
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.Dic.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.Dic.UpdatorId">
            <summary>
            更新者ID
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.Dic.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="T:CYSF.Models.Entities.Tenant">
            <summary>
            租户表
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.Tenant.Id">
            <summary>
            主键ID
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.Tenant.Code">
            <summary>
            租户编码，唯一标识
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.Tenant.Name">
            <summary>
            租户名称
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.Tenant.ShortName">
            <summary>
            租户简称
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.Tenant.CompanyName">
            <summary>
            公司名称
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.Tenant.ContactName">
            <summary>
            联系人姓名
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.Tenant.ContactPhone">
            <summary>
            联系人电话
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.Tenant.DicLevel">
            <summary>
            租户等级(字典键)
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.Tenant.Status">
            <summary>
            租户状态
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.Tenant.ExpireTime">
            <summary>
            过期时间
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.Tenant.IsDeleted">
            <summary>
            软删除标识
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.Tenant.CreatorId">
            <summary>
            创建者ID
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.Tenant.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.Tenant.UpdatorId">
            <summary>
            更新者ID
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.Tenant.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="T:CYSF.Models.Entities.TenantRole">
            <summary>
            租户角色表
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.TenantRole.Id">
            <summary>
            主键ID
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.TenantRole.TenantId">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.TenantRole.Name">
            <summary>
            角色名称，租户内唯一
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.TenantRole.Description">
            <summary>
            角色描述
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.TenantRole.IsSystem">
            <summary>
            是否系统角色
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.TenantRole.Sort">
            <summary>
            排序号
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.TenantRole.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.TenantRole.CreatorId">
            <summary>
            创建者ID
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.TenantRole.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.TenantRole.UpdatorId">
            <summary>
            更新者ID
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.TenantRole.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="T:CYSF.Models.Entities.TenantUser">
            <summary>
            租户用户表
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.TenantUser.Id">
            <summary>
            主键ID
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.TenantUser.TenantId">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.TenantUser.UserName">
            <summary>
            用户名，租户内唯一
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.TenantUser.Password">
            <summary>
            登录密码（加密后）
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.TenantUser.PasswordSalt">
            <summary>
            密码加盐值
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.TenantUser.RoleId">
            <summary>
            角色ID
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.TenantUser.Name">
            <summary>
            真实姓名
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.TenantUser.Mobile">
            <summary>
            手机号码
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.TenantUser.Email">
            <summary>
            邮箱地址
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.TenantUser.UserState">
            <summary>
            用户状态
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.TenantUser.LastLoginIp">
            <summary>
            最后登录IP
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.TenantUser.LastLoginTime">
            <summary>
            最后登录时间
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.TenantUser.CreatorId">
            <summary>
            创建者ID
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.TenantUser.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.TenantUser.UpdatorId">
            <summary>
            更新者ID
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.TenantUser.UpdateTime">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="T:CYSF.Models.Entities.UserLoginRecord">
            <summary>
            用户登录记录表
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.UserLoginRecord.Id">
            <summary>
            主键ID
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.UserLoginRecord.TenantId">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.UserLoginRecord.UserId">
            <summary>
            用户ID
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.UserLoginRecord.LoginIp">
            <summary>
            登录IP地址
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.UserLoginRecord.Description">
            <summary>
            登录描述（浏览器、设备等信息）
            </summary>
        </member>
        <member name="P:CYSF.Models.Entities.UserLoginRecord.RecordTime">
            <summary>
            记录时间
            </summary>
        </member>
        <member name="F:CYSF.Models.Enum.DicType.TenantLevel">
            <summary>
            租户等级
            </summary>
        </member>
        <member name="T:CYSF.Models.Enum.Level">
            <summary>
            租户等级
            </summary>
        </member>
        <member name="T:CYSF.Models.Enum.TenantStatus">
            <summary>
            租户状态枚举
            </summary>
        </member>
        <member name="F:CYSF.Models.Enum.TenantStatus.Normal">
            <summary>
            正常
            </summary>
        </member>
        <member name="F:CYSF.Models.Enum.TenantStatus.Disabled">
            <summary>
            禁用
            </summary>
        </member>
        <member name="F:CYSF.Models.Enum.TenantStatus.Expired">
            <summary>
            过期
            </summary>
        </member>
        <member name="F:CYSF.Models.Enum.TenantStatus.Locked">
            <summary>
            锁定
            </summary>
        </member>
        <member name="T:CYSF.Models.Enum.UserState">
            <summary>
            用户状态
            </summary>
        </member>
        <member name="T:CYSF.Models.Request.Auth.DelTokenReq">
            <summary>
            删除Token请求模型
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Auth.DelTokenReq.Token">
            <summary>
            Token
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Auth.DelTokenReq.RefreshToken">
            <summary>
            刷新Token
            </summary>
        </member>
        <member name="T:CYSF.Models.Request.Auth.DelTokenReqValidator">
            <summary>
            删除Token请求验证器 - 继承基础验证器，自动支持快速失败模式
            </summary>
        </member>
        <member name="T:CYSF.Models.Request.Auth.LoginReq">
            <summary>
            登录请求模型
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Auth.LoginReq.Identifier">
            <summary>
            用户名/手机号码/邮箱
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Auth.LoginReq.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="T:CYSF.Models.Request.Auth.LoginReqValidator">
            <summary>
            登录请求验证器 - 继承基础验证器，自动支持快速失败模式
            </summary>
        </member>
        <member name="M:CYSF.Models.Request.Auth.LoginReqValidator.BeValidIdentifier(System.String)">
            <summary>
            验证标识符是否有效（用户名、手机号码或邮箱）
            </summary>
            <param name="identifier">标识符</param>
            <returns>是否有效</returns>
        </member>
        <member name="M:CYSF.Models.Request.Auth.LoginReqValidator.BeValidPassword(System.String)">
            <summary>
            验证密码格式是否有效
            </summary>
            <param name="password">密码</param>
            <returns>是否有效</returns>
        </member>
        <member name="T:CYSF.Models.Request.Auth.RefreshTokenReq">
            <summary>
            刷新Token请求模型
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Auth.RefreshTokenReq.UserId">
            <summary>
            用户ID
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Auth.RefreshTokenReq.RefreshToken">
            <summary>
            刷新Token
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Auth.RefreshTokenReq.Token">
            <summary>
            当前Token
            </summary>
        </member>
        <member name="T:CYSF.Models.Request.Auth.RefreshTokenReqValidator">
            <summary>
            刷新Token请求验证器 - 继承基础验证器，自动支持快速失败模式
            </summary>
        </member>
        <member name="T:CYSF.Models.Request.PageListReq`1">
            <summary>
                分页请求参数实体
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.PageListReq`1.Pagination">
            <summary>
                分页参数
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.PageListReq`1.Sorts">
            <summary>
                排序参数
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.PageListReq`1.Filters">
            <summary>
                筛选实体
            </summary>
        </member>
        <member name="T:CYSF.Models.Request.ListReq`1">
            <summary>
                无分页列表请求参数实体
            </summary>
            <typeparam name="T">请求参数实体</typeparam>
        </member>
        <member name="P:CYSF.Models.Request.ListReq`1.Sorts">
            <summary>
                排序参数
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.ListReq`1.Filters">
            <summary>
                筛选实体
            </summary>
        </member>
        <member name="T:CYSF.Models.Request.Pagination">
            <summary>
                分页请求参数实体
            </summary>
        </member>
        <member name="M:CYSF.Models.Request.Pagination.#ctor(System.Int32,System.Int32)">
            <summary>
            </summary>
            <param name="pageIndex"></param>
            <param name="pageSize"></param>
        </member>
        <member name="M:CYSF.Models.Request.Pagination.#ctor">
            <summary>
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Pagination.PageIndex">
            <summary>
                页索引
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Pagination.PageSize">
            <summary>
                页记录数
            </summary>
        </member>
        <member name="T:CYSF.Models.Request.PageSort">
            <summary>
                排序请求参数实体
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.PageSort.SortField">
            <summary>
                排序字段
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.PageSort.OrderDesc">
            <summary>
                倒序标识
            </summary>
        </member>
        <member name="T:CYSF.Models.Request.Role.CreateRoleReq">
            <summary>
            创建角色请求模型
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Role.CreateRoleReq.TenantId">
            <summary>
            租户ID（平台管理员创建时需要指定，租户管理员创建时自动设置）
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Role.CreateRoleReq.Name">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Role.CreateRoleReq.Description">
            <summary>
            角色描述
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Role.CreateRoleReq.Sort">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Role.CreateRoleReq.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="T:CYSF.Models.Request.Role.CreateRoleReqValidator">
            <summary>
            创建角色请求验证器
            </summary>
        </member>
        <member name="T:CYSF.Models.Request.Role.RolePageListReq">
            <summary>
            角色列表查询请求模型
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Role.RolePageListReq.Name">
            <summary>
            角色名称筛选
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Role.RolePageListReq.TenantId">
            <summary>
            租户ID筛选（平台管理员可以指定，租户管理员自动过滤）
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Role.RolePageListReq.IsEnabled">
            <summary>
            是否启用筛选
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Role.RolePageListReq.CreateTime">
            <summary>
            创建时间范围（第一个元素为开始时间，第二个元素为结束时间）
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Role.RolePageListReq.UpdateTime">
            <summary>
            更新时间范围（第一个元素为开始时间，第二个元素为结束时间）
            </summary>
        </member>
        <member name="T:CYSF.Models.Request.Role.UpdateRoleReq">
            <summary>
            更新角色请求模型
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Role.UpdateRoleReq.Id">
            <summary>
            角色ID
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Role.UpdateRoleReq.Name">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Role.UpdateRoleReq.Description">
            <summary>
            角色描述
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Role.UpdateRoleReq.Sort">
            <summary>
            排序
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Role.UpdateRoleReq.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="T:CYSF.Models.Request.Role.UpdateRoleReqValidator">
            <summary>
            更新角色请求验证器
            </summary>
        </member>
        <member name="T:CYSF.Models.Request.TenantUser.CreateTenantUserReq">
            <summary>
            创建租户用户请求模型
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.TenantUser.CreateTenantUserReq.TenantId">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.TenantUser.CreateTenantUserReq.UserName">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.TenantUser.CreateTenantUserReq.Name">
            <summary>
            姓名
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.TenantUser.CreateTenantUserReq.Mobile">
            <summary>
            手机号码
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.TenantUser.CreateTenantUserReq.Email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.TenantUser.CreateTenantUserReq.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.TenantUser.CreateTenantUserReq.ConfirmPassword">
            <summary>
            确认密码
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.TenantUser.CreateTenantUserReq.UserState">
            <summary>
            用户状态
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.TenantUser.CreateTenantUserReq.RoleId">
            <summary>
            角色ID
            </summary>
        </member>
        <member name="T:CYSF.Models.Request.TenantUser.CreateTenantUserReqValidator">
            <summary>
            创建租户用户请求验证器 - 继承基础验证器，自动支持快速失败模式
            </summary>
        </member>
        <member name="M:CYSF.Models.Request.TenantUser.CreateTenantUserReqValidator.BeValidUserName(System.String)">
            <summary>
            验证用户名格式是否有效
            </summary>
            <param name="userName">用户名</param>
            <returns>是否有效</returns>
        </member>
        <member name="M:CYSF.Models.Request.TenantUser.CreateTenantUserReqValidator.BeValidMobile(System.String)">
            <summary>
            验证手机号格式是否有效
            </summary>
            <param name="mobile">手机号</param>
            <returns>是否有效</returns>
        </member>
        <member name="M:CYSF.Models.Request.TenantUser.CreateTenantUserReqValidator.BeValidPassword(System.String)">
            <summary>
            验证密码格式是否有效
            </summary>
            <param name="password">密码</param>
            <returns>是否有效</returns>
        </member>
        <member name="T:CYSF.Models.Request.TenantUser.ResetPasswordReq">
            <summary>
            重置密码请求模型
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.TenantUser.ResetPasswordReq.UserId">
            <summary>
            用户ID
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.TenantUser.ResetPasswordReq.NewPassword">
            <summary>
            新密码
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.TenantUser.ResetPasswordReq.ConfirmNewPassword">
            <summary>
            确认新密码
            </summary>
        </member>
        <member name="T:CYSF.Models.Request.TenantUser.ResetPasswordReqValidator">
            <summary>
            重置密码请求验证器 - 继承基础验证器，自动支持快速失败模式
            </summary>
        </member>
        <member name="M:CYSF.Models.Request.TenantUser.ResetPasswordReqValidator.BeValidPassword(System.String)">
            <summary>
            验证密码格式是否有效
            </summary>
            <param name="password">密码</param>
            <returns>是否有效</returns>
        </member>
        <member name="T:CYSF.Models.Request.TenantUser.TenantUserPageListReq">
            <summary>
            租户用户列表查询请求模型
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.TenantUser.TenantUserPageListReq.UserName">
            <summary>
            用户名筛选
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.TenantUser.TenantUserPageListReq.Name">
            <summary>
            姓名筛选
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.TenantUser.TenantUserPageListReq.Mobile">
            <summary>
            手机号筛选
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.TenantUser.TenantUserPageListReq.Email">
            <summary>
            邮箱筛选
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.TenantUser.TenantUserPageListReq.TenantId">
            <summary>
            租户ID筛选
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.TenantUser.TenantUserPageListReq.UserState">
            <summary>
            用户状态筛选
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.TenantUser.TenantUserPageListReq.RoleId">
            <summary>
            角色ID筛选
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.TenantUser.TenantUserPageListReq.CreateTime">
            <summary>
            创建时间范围（第一个元素为开始时间，第二个元素为结束时间）
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.TenantUser.TenantUserPageListReq.LastLoginTime">
            <summary>
            最后登录时间范围（第一个元素为开始时间，第二个元素为结束时间）
            </summary>
        </member>
        <member name="T:CYSF.Models.Request.TenantUser.UpdateTenantUserReq">
            <summary>
            更新租户用户请求模型
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.TenantUser.UpdateTenantUserReq.Id">
            <summary>
            用户ID
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.TenantUser.UpdateTenantUserReq.UserName">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.TenantUser.UpdateTenantUserReq.Name">
            <summary>
            姓名
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.TenantUser.UpdateTenantUserReq.Mobile">
            <summary>
            手机号码
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.TenantUser.UpdateTenantUserReq.Email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.TenantUser.UpdateTenantUserReq.UserState">
            <summary>
            用户状态
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.TenantUser.UpdateTenantUserReq.RoleId">
            <summary>
            角色ID
            </summary>
        </member>
        <member name="T:CYSF.Models.Request.TenantUser.UpdateTenantUserReqValidator">
            <summary>
            更新租户用户请求验证器 - 继承基础验证器，自动支持快速失败模式
            </summary>
        </member>
        <member name="M:CYSF.Models.Request.TenantUser.UpdateTenantUserReqValidator.BeValidUserName(System.String)">
            <summary>
            验证用户名格式是否有效
            </summary>
            <param name="userName">用户名</param>
            <returns>是否有效</returns>
        </member>
        <member name="M:CYSF.Models.Request.TenantUser.UpdateTenantUserReqValidator.BeValidMobile(System.String)">
            <summary>
            验证手机号格式是否有效
            </summary>
            <param name="mobile">手机号</param>
            <returns>是否有效</returns>
        </member>
        <member name="T:CYSF.Models.Request.Tenant.CreateTenantReq">
            <summary>
            创建租户请求模型
            </summary>
        </member>
        <member name="T:CYSF.Models.Request.Tenant.CreateTenantReqValidator">
            <summary>
            创建租户请求验证器 - 继承基础验证器，自动支持快速失败模式
            </summary>
        </member>
        <member name="M:CYSF.Models.Request.Tenant.CreateTenantReqValidator.BeValidCode(System.String)">
            <summary>
            验证租户编号格式
            </summary>
            <param name="code">租户编号</param>
            <returns>是否有效</returns>
        </member>
        <member name="M:CYSF.Models.Request.Tenant.CreateTenantReqValidator.BeValidPhone(System.String)">
            <summary>
            验证联系人电话格式
            </summary>
            <param name="phone">电话号码</param>
            <returns>是否有效</returns>
        </member>
        <member name="M:CYSF.Models.Request.Tenant.CreateTenantReqValidator.BeValidExpireTime(System.Nullable{System.DateTime})">
            <summary>
            验证过期时间
            </summary>
            <param name="expireTime">过期时间</param>
            <returns>是否有效</returns>
        </member>
        <member name="T:CYSF.Models.Request.Tenant.TenantPageListReq">
            <summary>
            租户列表查询请求模型
            </summary>
        </member>
        <member name="T:CYSF.Models.Request.Tenant.UpdateTenantReq">
            <summary>
            更新租户请求模型
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Tenant.UpdateTenantReq.Id">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Tenant.UpdateTenantReq.Code">
            <summary>
            租户编号
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Tenant.UpdateTenantReq.ShortName">
            <summary>
            租户简称
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Tenant.UpdateTenantReq.CompanyName">
            <summary>
            公司名称
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Tenant.UpdateTenantReq.ContactName">
            <summary>
            联系人姓名
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Tenant.UpdateTenantReq.ContactPhone">
            <summary>
            联系人电话
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Tenant.UpdateTenantReq.Level">
            <summary>
            租户等级
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.Tenant.UpdateTenantReq.ExpireTime">
            <summary>
            过期时间
            </summary>
        </member>
        <member name="T:CYSF.Models.Request.Tenant.UpdateTenantReqValidator">
            <summary>
            更新租户请求验证器 - 继承基础验证器，自动支持快速失败模式
            </summary>
        </member>
        <member name="M:CYSF.Models.Request.Tenant.UpdateTenantReqValidator.BeValidCode(System.String)">
            <summary>
            验证租户编号格式
            </summary>
            <param name="code">租户编号</param>
            <returns>是否有效</returns>
        </member>
        <member name="M:CYSF.Models.Request.Tenant.UpdateTenantReqValidator.BeValidPhone(System.String)">
            <summary>
            验证联系人电话格式
            </summary>
            <param name="phone">电话号码</param>
            <returns>是否有效</returns>
        </member>
        <member name="M:CYSF.Models.Request.Tenant.UpdateTenantReqValidator.BeValidExpireTime(System.Nullable{System.DateTime})">
            <summary>
            验证过期时间
            </summary>
            <param name="expireTime">过期时间</param>
            <returns>是否有效</returns>
        </member>
        <member name="T:CYSF.Models.Request.UserLoginRecord.CreateUserLoginRecordReq">
            <summary>
            创建用户登录记录请求模型
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.UserLoginRecord.CreateUserLoginRecordReq.TenantId">
            <summary>
            租户ID
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.UserLoginRecord.CreateUserLoginRecordReq.UserId">
            <summary>
            用户ID
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.UserLoginRecord.CreateUserLoginRecordReq.LoginIp">
            <summary>
            登录IP
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.UserLoginRecord.CreateUserLoginRecordReq.Description">
            <summary>
            描述(浏览器版本、前端版本等)
            </summary>
        </member>
        <member name="T:CYSF.Models.Request.UserLoginRecord.CreateUserLoginRecordReqValidator">
            <summary>
            创建用户登录记录请求验证器
            </summary>
        </member>
        <member name="T:CYSF.Models.Request.UserLoginRecord.UserLoginRecordPageListReq">
            <summary>
            用户登录记录列表查询请求模型
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.UserLoginRecord.UserLoginRecordPageListReq.TenantId">
            <summary>
            租户ID筛选（租户管理员自动过滤）
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.UserLoginRecord.UserLoginRecordPageListReq.UserId">
            <summary>
            用户ID筛选
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.UserLoginRecord.UserLoginRecordPageListReq.LoginIp">
            <summary>
            登录IP筛选
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.UserLoginRecord.UserLoginRecordPageListReq.Description">
            <summary>
            描述筛选
            </summary>
        </member>
        <member name="P:CYSF.Models.Request.UserLoginRecord.UserLoginRecordPageListReq.RecordTime">
            <summary>
            登录时间范围（第一个元素为开始时间，第二个元素为结束时间）
            </summary>
        </member>
        <member name="P:CYSF.Models.Response.ListRes`1.Total">
            <summary>
                总记录数
            </summary>
        </member>
        <member name="P:CYSF.Models.Response.ListRes`1.Data">
            <summary>
                数据集
            </summary>
        </member>
        <member name="P:CYSF.Models.Response.ListRes`1.ExtendData">
            <summary>
                扩展数据
            </summary>
        </member>
        <member name="T:CYSF.Models.Response.TenantDto">
            <summary>
            租户响应模型
            </summary>
        </member>
        <member name="P:CYSF.Models.Response.TenantDto.IsExpired">
            <summary>
            是否已过期
            </summary>
        </member>
        <member name="P:CYSF.Models.Response.TenantDto.RemainingDays">
            <summary>
            剩余天数
            </summary>
        </member>
        <member name="P:CYSF.Models.Response.TenantDto.StatusDescription">
            <summary>
            状态描述
            </summary>
        </member>
        <member name="T:CYSF.Models.Response.TenantUserDto">
            <summary>
            租户用户响应模型
            </summary>
        </member>
        <member name="P:CYSF.Models.Response.TenantUserDto.LastLoginTimeDescription">
            <summary>
            最后登录时间描述
            </summary>
        </member>
        <member name="P:CYSF.Models.Response.TenantUserDto.AccountAgeDays">
            <summary>
            账户创建天数
            </summary>
        </member>
        <member name="P:CYSF.Models.Response.TenantUserDto.RoleName">
            <summary>
            角色名称（需要在应用层填充）
            </summary>
        </member>
        <member name="P:CYSF.Models.Response.TenantUserDto.TenantName">
            <summary>
            租户名称（需要在应用层填充）
            </summary>
        </member>
        <member name="P:CYSF.Models.Response.TenantUserDto.CreatorName">
            <summary>
            创建者姓名（需要在应用层填充）
            </summary>
        </member>
        <member name="P:CYSF.Models.Response.TenantUserDto.UpdatorName">
            <summary>
            更新者姓名（需要在应用层填充）
            </summary>
        </member>
        <member name="P:CYSF.Models.Response.TokenDto.UserId">
            <summary>
                用户Id
            </summary>
        </member>
        <member name="P:CYSF.Models.Response.TokenDto.RoleId">
            <summary>
                角色Id
            </summary>
        </member>
        <member name="P:CYSF.Models.Response.TokenDto.TenantId">
            <summary>
                租户Id
            </summary>
        </member>
        <member name="P:CYSF.Models.Response.TokenDto.TenantName">
            <summary>
                租户名称
            </summary>
        </member>
        <member name="P:CYSF.Models.Response.TokenDto.IsTenant">
            <summary>
                是否为租户
            </summary>
        </member>
        <member name="P:CYSF.Models.Response.TokenDto.Name">
            <summary>
                姓名
            </summary>
        </member>
        <member name="P:CYSF.Models.Response.TokenDto.Token">
            <summary>
                Token
            </summary>
        </member>
        <member name="P:CYSF.Models.Response.TokenDto.RefreshToken">
            <summary>
                RefreshToken
            </summary>
        </member>
        <member name="P:CYSF.Models.Response.TokenDto.TokenExpires">
            <summary>
                Token过期时间(秒)
            </summary>
        </member>
        <member name="P:CYSF.Models.Response.TokenDto.RefreshTokenExpires">
            <summary>
                刷新Token过期时间(秒)
            </summary>
        </member>
        <member name="P:CYSF.Models.Response.UserInfoDto.TenantId">
            <summary>
            租户Id, 平台管理员为0
            </summary>
        </member>
        <member name="P:CYSF.Models.Response.UserInfoDto.TenantName">
            <summary>
            租户名称
            </summary>
        </member>
        <member name="P:CYSF.Models.Response.UserInfoDto.Name">
            <summary>
            姓名
            </summary>
        </member>
        <member name="P:CYSF.Models.Response.UserInfoDto.RoleId">
            <summary>
            角色Id, 平台管理员为0
            </summary>
        </member>
        <member name="P:CYSF.Models.Response.UserInfoDto.RoleName">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="P:CYSF.Models.Response.UserInfoDto.LastLoginIp">
            <summary>
            最后登录Ip
            </summary>
        </member>
        <member name="P:CYSF.Models.Response.UserInfoDto.LastLoginTime">
            <summary>
            最后登录时间
            </summary>
        </member>
        <member name="T:CYSF.Models.Validators.Common.BaseValidator`1">
            <summary>
            基础验证器 - 统一配置快速失败模式
            </summary>
            <typeparam name="T">要验证的模型类型</typeparam>
        </member>
        <member name="T:CYSF.Models.Validators.Extensions.ValidationExtensions">
            <summary>
            FluentValidation 扩展方法
            </summary>
        </member>
        <member name="M:CYSF.Models.Validators.Extensions.ValidationExtensions.MustBeValidMobile``1(FluentValidation.IRuleBuilder{``0,System.String})">
            <summary>
            验证手机号码格式
            </summary>
            <typeparam name="T"></typeparam>
            <param name="ruleBuilder"></param>
            <returns></returns>
        </member>
        <member name="M:CYSF.Models.Validators.Extensions.ValidationExtensions.MustBeValidEmail``1(FluentValidation.IRuleBuilder{``0,System.String})">
            <summary>
            验证邮箱格式
            </summary>
            <typeparam name="T"></typeparam>
            <param name="ruleBuilder"></param>
            <returns></returns>
        </member>
        <member name="M:CYSF.Models.Validators.Extensions.ValidationExtensions.MustBeValidUsername``1(FluentValidation.IRuleBuilder{``0,System.String})">
            <summary>
            验证用户名格式
            </summary>
            <typeparam name="T"></typeparam>
            <param name="ruleBuilder"></param>
            <returns></returns>
        </member>
        <member name="M:CYSF.Models.Validators.Extensions.ValidationExtensions.MustBeValidIdNumber``1(FluentValidation.IRuleBuilder{``0,System.String})">
            <summary>
            验证身份证号码格式
            </summary>
            <typeparam name="T"></typeparam>
            <param name="ruleBuilder"></param>
            <returns></returns>
        </member>
        <member name="M:CYSF.Models.Validators.Extensions.ValidationExtensions.MustBeStrongPassword``1(FluentValidation.IRuleBuilder{``0,System.String})">
            <summary>
            验证强密码格式
            </summary>
            <typeparam name="T"></typeparam>
            <param name="ruleBuilder"></param>
            <returns></returns>
        </member>
        <member name="M:CYSF.Models.Validators.Extensions.ValidationExtensions.MustNotContainSpaces``1(FluentValidation.IRuleBuilder{``0,System.String})">
            <summary>
            验证不能包含空格
            </summary>
            <typeparam name="T"></typeparam>
            <param name="ruleBuilder"></param>
            <returns></returns>
        </member>
        <member name="M:CYSF.Models.Validators.Extensions.ValidationExtensions.MustBeValidPrice``1(FluentValidation.IRuleBuilder{``0,System.String})">
            <summary>
            验证价格格式
            </summary>
            <typeparam name="T"></typeparam>
            <param name="ruleBuilder"></param>
            <returns></returns>
        </member>
        <member name="M:CYSF.Models.Validators.Extensions.ValidationExtensions.MustBeNumber``1(FluentValidation.IRuleBuilder{``0,System.String})">
            <summary>
            验证数字格式
            </summary>
            <typeparam name="T"></typeparam>
            <param name="ruleBuilder"></param>
            <returns></returns>
        </member>
        <member name="M:CYSF.Models.Validators.Extensions.ValidationExtensions.MustBeValidIP``1(FluentValidation.IRuleBuilder{``0,System.String})">
            <summary>
            验证IP地址格式
            </summary>
            <typeparam name="T"></typeparam>
            <param name="ruleBuilder"></param>
            <returns></returns>
        </member>
    </members>
</doc>
