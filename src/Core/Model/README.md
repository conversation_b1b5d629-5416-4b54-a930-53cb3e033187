# 📊 CYSF.Models - 数据模型层

## 📋 项目概述

CYSF.Models是系统的数据模型层，包含所有的实体类、请求/响应模型、枚举定义和常量。该层定义了系统中所有数据结构，为其他层提供统一的数据契约。

## 🏗️ 项目架构

### 技术栈
- **框架**: .NET 8.0
- **ORM特性**: SqlSugar Attributes
- **验证**: FluentValidation 11.4.0
- **序列化**: Newtonsoft.Json

### 项目结构
```
CYSF.Models/
├── Entities/                  # 实体类 (代码生成)
│   ├── Tenant.cs                 # 租户实体
│   ├── TenantUser.cs             # 用户实体
│   ├── Role.cs                   # 角色实体
│   ├── UserTenantRela.cs         # 用户租户关系实体
│   └── UserLoginRecord.cs        # 用户登录记录实体
├── Request/                   # 请求模型
│   ├── Tenant/                   # 租户相关请求
│   ├── TenantUser/               # 用户相关请求
│   ├── Role/                     # 角色相关请求 (代码生成)
│   ├── UserTenantRela/           # 用户租户关系请求 (代码生成)
│   ├── UserLoginRecord/          # 用户登录记录请求 (代码生成)
│   ├── PageListReq.cs            # 分页请求基类
│   └── PageSort.cs               # 排序模型
├── Response/                  # 响应模型
│   ├── ListRes.cs                # 分页响应模型
│   └── ApiResult.cs              # API统一响应模型
├── Const/                     # 常量定义
│   └── CacheKey.cs               # 缓存键常量
├── Enum/                      # 枚举定义 (代码生成)
│   ├── TenantStatusEnum.cs       # 租户状态枚举
│   ├── TenantLevelEnum.cs        # 租户级别枚举
│   └── UserStatusEnum.cs         # 用户状态枚举
└── CYSF.Models.csproj         # 项目文件
```

## 🚀 核心功能

### 1. 实体类 (Entities)
实体类通过代码生成器自动生成，包含SqlSugar特性标注：

<augment_code_snippet path="src/Core/Model/CYSF.Models/Entities/Tenant.cs" mode="EXCERPT">
````csharp
/// <summary>
/// 租户表
/// </summary>
[SugarTable("Tenant")]
public class Tenant
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
    public int Id { get; set; }

    /// <summary>
    /// 租户编号
    /// </summary>
    [SugarColumn(Length = 50)]
    public string Code { get; set; }

    /// <summary>
    /// 租户名称
    /// </summary>
    [SugarColumn(Length = 100)]
    public string Name { get; set; }
}
````
</augment_code_snippet>

### 2. 请求模型 (Request)
请求模型包含验证规则和数据传输对象：

```csharp
/// <summary>
/// 创建租户请求
/// </summary>
public class CreateTenantReq
{
    /// <summary>
    /// 租户编号
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    /// 租户名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 租户级别
    /// </summary>
    public TenantLevelEnum Level { get; set; }
}

/// <summary>
/// 创建租户请求验证器
/// </summary>
public class CreateTenantReqValidator : AbstractValidator<CreateTenantReq>
{
    public CreateTenantReqValidator()
    {
        RuleFor(x => x.Code)
            .NotEmpty().WithMessage("租户编号不能为空")
            .MaximumLength(50).WithMessage("租户编号长度不能超过50个字符");

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("租户名称不能为空")
            .MaximumLength(100).WithMessage("租户名称长度不能超过100个字符");
    }
}
```

### 3. 响应模型 (Response)
统一的响应格式和分页模型：

```csharp
/// <summary>
/// API统一响应模型
/// </summary>
public class ApiResult
{
    /// <summary>
    /// 状态码
    /// </summary>
    public ApiStatusCode code { get; set; } = ApiStatusCode.Success;

    /// <summary>
    /// 消息
    /// </summary>
    public string message { get; set; } = string.Empty;

    /// <summary>
    /// 数据对象
    /// </summary>
    public object data { get; set; }
}

/// <summary>
/// 分页响应模型
/// </summary>
public class ListRes<T>
{
    /// <summary>
    /// 数据列表
    /// </summary>
    public List<T> Items { get; set; } = new List<T>();

    /// <summary>
    /// 总记录数
    /// </summary>
    public int TotalCount { get; set; }

    /// <summary>
    /// 页码
    /// </summary>
    public int PageIndex { get; set; }

    /// <summary>
    /// 页大小
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// 总页数
    /// </summary>
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
}

/// <summary>
/// API状态码枚举
/// </summary>
public enum ApiStatusCode
{
    /// <summary>
    /// 执行成功
    /// </summary>
    Success = 200,

    /// <summary>
    /// 逻辑异常（模型验证、业务处理等）
    /// </summary>
    LogicException = 201,

    /// <summary>
    /// 未登录，用户身份非法
    /// </summary>
    Unauthorized = 401,

    /// <summary>
    /// Token过期，无法刷新有效身份
    /// </summary>
    SecurityTokenExpired = 402,

    /// <summary>
    /// 无权限
    /// </summary>
    NonPermission = 403,

    /// <summary>
    /// 未找到
    /// </summary>
    Notfound = 404
}
```

### 4. 枚举定义 (Enum)
通过代码生成器根据数据库注释自动生成：

```csharp
/// <summary>
/// 租户状态枚举
/// </summary>
public enum TenantStatusEnum
{
    /// <summary>
    /// 正常
    /// </summary>
    Normal = 1,

    /// <summary>
    /// 过期
    /// </summary>
    Expired = 2,

    /// <summary>
    /// 禁用
    /// </summary>
    Disabled = 3
}
```

## 🔧 核心特性

### 1. SqlSugar特性标注
```csharp
[SugarTable("TableName")]
public class Entity
{
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
    public int Id { get; set; }

    [SugarColumn(Length = 50, IsNullable = false)]
    public string Code { get; set; }

    [SugarColumn(ColumnDataType = "datetime", IsNullable = false)]
    public DateTime CreateTime { get; set; }
}
```

### 2. FluentValidation验证
```csharp
public class EntityValidator : AbstractValidator<Entity>
{
    public EntityValidator()
    {
        RuleFor(x => x.Code)
            .NotEmpty().WithMessage("编号不能为空")
            .Length(1, 50).WithMessage("编号长度必须在1-50个字符之间");

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("名称不能为空")
            .MaximumLength(100).WithMessage("名称长度不能超过100个字符");
    }
}
```

### 3. 分页查询支持
```csharp
/// <summary>
/// 分页请求基类
/// </summary>
public class PageListReq<T>
{
    /// <summary>
    /// 页码
    /// </summary>
    public int PageIndex { get; set; } = 1;

    /// <summary>
    /// 页大小
    /// </summary>
    public int PageSize { get; set; } = 20;

    /// <summary>
    /// 排序条件
    /// </summary>
    public List<PageSort> Sorts { get; set; } = new List<PageSort>();

    /// <summary>
    /// 查询条件
    /// </summary>
    public T Data { get; set; }

    /// <summary>
    /// 是否为空排序
    /// </summary>
    public bool IsSortEmpty => Sorts == null || !Sorts.Any();
}
```

## 📦 依赖关系

### 项目引用
- **CYSF.Core**: 核心基础设施

### NuGet包
- `FluentValidation` 11.4.0
- `SqlSugarCore` 5.1.4.196

## 🛠️ 开发指南

### 添加新的实体类

1. **手动创建实体**
```csharp
/// <summary>
/// 新实体表
/// </summary>
[SugarTable("NewEntity")]
public class NewEntity
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
    public int Id { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    [SugarColumn(Length = 100, IsNullable = false)]
    public string Name { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public DateTime UpdateTime { get; set; }
}
```

2. **使用代码生成器**
```bash
cd src/Core/Infrastructure/CYSF.Generate
dotnet run
```

### 添加请求模型

1. **创建请求类**
```csharp
/// <summary>
/// 创建新实体请求
/// </summary>
public class CreateNewEntityReq
{
    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; }
}

/// <summary>
/// 更新新实体请求
/// </summary>
public class UpdateNewEntityReq
{
    /// <summary>
    /// ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; }
}

/// <summary>
/// 新实体分页查询请求
/// </summary>
public class NewEntityPageListReq
{
    /// <summary>
    /// 名称（模糊查询）
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 创建时间范围
    /// </summary>
    public List<DateTime> CreateTimeRange { get; set; }
}
```

2. **添加验证器**
```csharp
public class CreateNewEntityReqValidator : AbstractValidator<CreateNewEntityReq>
{
    public CreateNewEntityReqValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("名称不能为空")
            .MaximumLength(100).WithMessage("名称长度不能超过100个字符");
    }
}
```

### 添加枚举

1. **手动创建枚举**
```csharp
/// <summary>
/// 新实体状态枚举
/// </summary>
public enum NewEntityStatusEnum
{
    /// <summary>
    /// 启用
    /// </summary>
    Enabled = 1,

    /// <summary>
    /// 禁用
    /// </summary>
    Disabled = 2
}
```

2. **使用代码生成器**
代码生成器会根据数据库字段注释自动生成枚举。

## 🔍 最佳实践

### 1. 实体设计
- 使用有意义的属性名称
- 添加完整的XML注释
- 合理使用SqlSugar特性
- 遵循命名规范

### 2. 验证规则
- 在请求模型中添加验证规则
- 提供清晰的错误消息
- 使用FluentValidation的高级功能
- 考虑业务规则验证

### 3. 枚举使用
- 为枚举值添加描述
- 使用有意义的枚举名称
- 考虑枚举的扩展性
- 避免修改已有枚举值

### 4. 响应模型
- 使用统一的响应格式
- 提供完整的分页信息
- 考虑API版本兼容性
- 避免暴露敏感信息

## 🧪 测试

### 验证器测试
```csharp
[Test]
public void CreateTenantReqValidator_EmptyCode_ShouldHaveError()
{
    // Arrange
    var validator = new CreateTenantReqValidator();
    var request = new CreateTenantReq { Code = "", Name = "Test" };

    // Act
    var result = validator.Validate(request);

    // Assert
    Assert.IsFalse(result.IsValid);
    Assert.IsTrue(result.Errors.Any(x => x.PropertyName == nameof(CreateTenantReq.Code)));
}
```

### 模型映射测试
```csharp
[Test]
public void Tenant_MapToDto_ShouldMapCorrectly()
{
    // Arrange
    var tenant = new Tenant { Id = 1, Name = "Test", Code = "TEST" };

    // Act
    var dto = tenant.Adapt<TenantDto>();

    // Assert
    Assert.AreEqual(tenant.Id, dto.Id);
    Assert.AreEqual(tenant.Name, dto.Name);
    Assert.AreEqual(tenant.Code, dto.Code);
}
```

## 📚 相关文档

- [CYSF.Repositories - 数据访问层](../Repository/README.md)
- [CYSF.Core - 核心基础设施](../Infrastructure/README.md)
- [CYSF.Generate - 代码生成器](../Infrastructure/CYSF.Generate/README.md)
- [FluentValidation文档](https://docs.fluentvalidation.net/)
- [SqlSugar文档](https://www.donet5.com/Home/Doc)

---

📚 更多信息请参考 [主项目文档](../../../README.md)
