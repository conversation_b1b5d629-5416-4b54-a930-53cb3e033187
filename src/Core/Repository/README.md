# 🗄️ CYSF.Repositories - 数据访问层

## 📋 项目概述

CYSF.Repositories是系统的数据访问层，负责与数据库的直接交互。该层使用SqlSugar ORM框架，提供高效、类型安全的数据访问操作，并封装了常用的CRUD操作。

## 🏗️ 项目架构

### 技术栈
- **框架**: .NET 8.0
- **ORM**: SqlSugar ********* (线程安全的 SqlSugarScope)
- **数据库**: SQL Server / MySQL
- **依赖注入**: Autofac (模块化注册)

### 项目结构
```
CYSF.Repositories/
├── TenantRepository.cs        # 租户数据访问
├── TenantUserRepository.cs    # 用户数据访问
├── RoleRepository.cs          # 角色数据访问 (代码生成)
├── UserTenantRelaRepository.cs # 用户租户关系数据访问 (代码生成)
├── UserLoginRecordRepository.cs # 用户登录记录数据访问 (代码生成)
└── CYSF.Repositories.csproj   # 项目文件
```

## 🚀 核心功能

### 1. 租户数据访问 (TenantRepository)
- **基础CRUD**: 租户的增删改查操作
- **条件查询**: 根据编号、名称等条件查询
- **分页查询**: 支持分页的租户列表查询
- **批量操作**: 批量更新、删除操作

<augment_code_snippet path="src/Core/Repository/CYSF.Repositories/TenantRepository.cs" mode="EXCERPT">
````csharp
public class TenantRepository
{
    private readonly ISqlSugarClient _db;

    public TenantRepository(ISqlSugarClient db)
    {
        _db = db;
    }

    public async Task<Tenant> GetByIdAsync(int id)
    {
        return await _db.Queryable<Tenant>().FirstAsync(x => x.Id == id);
    }

    public async Task<Tenant> CreateAsync(Tenant entity)
    {
        var id = await _db.Insertable(entity).ExecuteReturnIdentityAsync();
        entity.Id = id;
        return entity;
    }
}
````
</augment_code_snippet>

### 2. 用户数据访问 (TenantUserRepository)
- **用户查询**: 根据用户名、手机号等查询
- **密码验证**: 用户登录验证相关查询
- **角色关联**: 用户角色关系查询
- **状态管理**: 用户状态相关操作

### 3. 代码生成的仓储
- **RoleRepository**: 角色数据访问
- **UserTenantRelaRepository**: 用户租户关系数据访问
- **UserLoginRecordRepository**: 用户登录记录数据访问

## 🔧 核心特性

### 1. SqlSugar集成
```csharp
public class BaseRepository<T> where T : class, new()
{
    protected readonly ISqlSugarClient _db;

    public BaseRepository(ISqlSugarClient db)
    {
        _db = db;
    }

    public virtual async Task<T> GetByIdAsync(int id)
    {
        return await _db.Queryable<T>().InSingleAsync(id);
    }

    public virtual async Task<T> CreateAsync(T entity)
    {
        var id = await _db.Insertable(entity).ExecuteReturnIdentityAsync();
        return entity;
    }
}
```

### 2. 条件查询构建
```csharp
public async Task<List<Tenant>> GetByConditionAsync(TenantQueryCondition condition)
{
    var query = _db.Queryable<Tenant>();

    if (!string.IsNullOrEmpty(condition.Name))
        query = query.Where(x => x.Name.Contains(condition.Name));

    if (!string.IsNullOrEmpty(condition.Code))
        query = query.Where(x => x.Code == condition.Code);

    if (condition.Status.HasValue)
        query = query.Where(x => x.Status == condition.Status.Value);

    return await query.ToListAsync();
}
```

### 3. 分页查询
```csharp
public async Task<(List<T> Items, int TotalCount)> GetPageListAsync<T>(
    ISugarQueryable<T> query, 
    int pageIndex, 
    int pageSize)
{
    var totalCount = await query.CountAsync();
    var items = await query
        .Skip((pageIndex - 1) * pageSize)
        .Take(pageSize)
        .ToListAsync();

    return (items, totalCount);
}
```

### 4. 批量操作
```csharp
public async Task<int> BatchUpdateAsync(List<Tenant> entities)
{
    return await _db.Updateable(entities).ExecuteCommandAsync();
}

public async Task<int> BatchDeleteAsync(List<int> ids)
{
    return await _db.Deleteable<Tenant>().In(ids).ExecuteCommandAsync();
}
```

## 📦 依赖关系

### 项目引用
- **CYSF.Core**: 核心基础设施
- **CYSF.Models**: 数据模型

### NuGet包
- `SqlSugarCore` *********

### 自动注册
仓储类通过Autofac自动注册，无需手动配置依赖注入。

## 🛠️ 开发指南

### 添加新的仓储类

1. **创建仓储类**
```csharp
public class NewRepository
{
    private readonly ISqlSugarClient _db;

    public NewRepository(ISqlSugarClient db)
    {
        _db = db;
    }

    public async Task<New> GetByIdAsync(int id)
    {
        return await _db.Queryable<New>().FirstAsync(x => x.Id == id);
    }

    public async Task<New> CreateAsync(New entity)
    {
        entity.CreateTime = DateTime.Now;
        entity.UpdateTime = DateTime.Now;
        
        var id = await _db.Insertable(entity).ExecuteReturnIdentityAsync();
        entity.Id = id;
        return entity;
    }

    public async Task<bool> UpdateAsync(New entity)
    {
        entity.UpdateTime = DateTime.Now;
        return await _db.Updateable(entity).ExecuteCommandHasChangeAsync();
    }

    public async Task<bool> DeleteAsync(int id)
    {
        return await _db.Deleteable<New>().In(id).ExecuteCommandHasChangeAsync();
    }
}
```

2. **实现复杂查询**
```csharp
public async Task<List<New>> GetByComplexConditionAsync(ComplexCondition condition)
{
    var query = _db.Queryable<New>()
        .LeftJoin<RelatedEntity>((n, r) => n.RelatedId == r.Id);

    // 动态条件
    if (condition.StartDate.HasValue)
        query = query.Where((n, r) => n.CreateTime >= condition.StartDate.Value);

    if (condition.EndDate.HasValue)
        query = query.Where((n, r) => n.CreateTime <= condition.EndDate.Value);

    // 排序
    query = query.OrderBy((n, r) => n.CreateTime, OrderByType.Desc);

    return await query.Select((n, r) => new New
    {
        Id = n.Id,
        Name = n.Name,
        RelatedName = r.Name
    }).ToListAsync();
}
```

3. **实现事务操作**
```csharp
public async Task<bool> CreateWithRelatedAsync(New entity, List<RelatedEntity> related)
{
    return await _db.Ado.UseTranAsync(async () =>
    {
        // 创建主实体
        var created = await CreateAsync(entity);
        
        // 创建关联实体
        foreach (var item in related)
        {
            item.NewId = created.Id;
            await _db.Insertable(item).ExecuteCommandAsync();
        }
        
        return true;
    });
}
```

### 使用代码生成器
可以使用项目的代码生成器自动生成仓储类：

```bash
cd src/Core/Infrastructure/CYSF.Generate
dotnet run
```

生成器会根据数据库表结构自动创建：
- 仓储类基础结构
- 标准CRUD方法
- 基础查询方法

## 🔍 最佳实践

### 1. 查询优化
- 使用合适的索引
- 避免SELECT *，只查询需要的字段
- 合理使用JOIN操作
- 使用分页避免大量数据查询

### 2. 数据访问
- Repository层专注于数据访问操作
- 提供标准的CRUD方法
- 不处理事务管理，事务由App层统一管理

### 3. 异常处理
- 捕获数据库异常并转换为业务异常
- 记录数据库操作日志
- 提供有意义的错误信息

### 4. 性能监控
- 监控慢查询
- 使用SqlSugar的性能分析功能
- 定期优化数据库查询

## 🧪 测试

### 单元测试
```csharp
[Test]
public async Task GetByIdAsync_ExistingId_ReturnsEntity()
{
    // Arrange
    var id = 1;
    
    // Act
    var result = await _tenantRepository.GetByIdAsync(id);
    
    // Assert
    Assert.IsNotNull(result);
    Assert.AreEqual(id, result.Id);
}

[Test]
public async Task CreateAsync_ValidEntity_ReturnsCreatedEntity()
{
    // Arrange
    var tenant = new Tenant { Name = "Test", Code = "TEST" };
    
    // Act
    var result = await _tenantRepository.CreateAsync(tenant);
    
    // Assert
    Assert.IsNotNull(result);
    Assert.Greater(result.Id, 0);
}
```

### 集成测试
```csharp
[Test]
public async Task GetPageListAsync_WithCondition_ReturnsFilteredResults()
{
    // Arrange
    var condition = new TenantQueryCondition { Name = "Test" };
    
    // Act
    var (items, totalCount) = await _tenantRepository.GetPageListAsync(condition, 1, 10);
    
    // Assert
    Assert.IsNotNull(items);
    Assert.GreaterOrEqual(totalCount, 0);
    Assert.IsTrue(items.All(x => x.Name.Contains("Test")));
}
```

## 📊 SqlSugar配置

### 连接字符串配置
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=CYSF;Trusted_Connection=true;"
  }
}
```

### SqlSugar配置
```csharp
// 在Startup.cs或Program.cs中配置
services.AddSqlSugar(new ConnectionConfig()
{
    ConnectionString = connectionString,
    DbType = DbType.SqlServer,
    IsAutoCloseConnection = true,
    InitKeyType = InitKeyType.Attribute
});
```

## 📚 相关文档

- [CYSF.Services - 业务服务层](../Service/README.md)
- [CYSF.Models - 数据模型层](../Model/README.md)
- [CYSF.Core - 核心基础设施](../Infrastructure/README.md)
- [SqlSugar官方文档](https://www.donet5.com/Home/Doc)

---

📚 更多信息请参考 [主项目文档](../../../README.md)
