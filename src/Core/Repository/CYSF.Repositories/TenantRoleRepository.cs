using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CYSF.Core.Extensions;
using CYSF.Repositories.Extensions;
using CYSF.Core.Providers;
using CYSF.Models.Entities;
using CYSF.Models.Request;
using CYSF.Models.Request.TenantRole;
using CYSF.Models.Response;
using CYSF.Repositories.Base;
using Mapster;
using SqlSugar;

namespace CYSF.Repositories;

/// <summary>
/// TenantRole仓储
/// </summary>
public class TenantRoleRepository(ISqlSugarClient db) : BaseRepository<TenantRole>(db)
{
    /// <summary>
    /// 获取TenantRole分页列表
    /// </summary>
    /// <param name="req">分页查询请求</param>
    /// <returns>TenantRole分页列表</returns>
    public async Task<ListRes<TenantRoleDto>> GetPageListAsync(PageListReq<TenantRolePageListReq> req)
    {
        // 构建查询
        var query = Context.Queryable<TenantRole>()
                .WhereIF(req.Filters.CreateTime != null && req.Filters.CreateTime.Count > 0, t => t.CreateTime >= req.Filters.CreateTime[0])
                .WhereIF(req.Filters.CreateTime != null && req.Filters.CreateTime.Count > 1, t => t.CreateTime <= req.Filters.CreateTime[1])
                .WhereIF(req.Filters.UpdateTime != null && req.Filters.UpdateTime.Count > 0, t => t.UpdateTime >= req.Filters.UpdateTime[0])
                .WhereIF(req.Filters.UpdateTime != null && req.Filters.UpdateTime.Count > 1, t => t.UpdateTime <= req.Filters.UpdateTime[1])
                .WhereIF(req.Filters.TenantId.HasValue, t => t.TenantId == req.Filters.TenantId.Value)
                .WhereIF(!string.IsNullOrEmpty(req.Filters.Name), t => t.Name.Contains(req.Filters.Name))
                .WhereIF(!string.IsNullOrEmpty(req.Filters.Description), t => t.Description.Contains(req.Filters.Description))
                .WhereIF(req.Filters.Sort.HasValue, t => t.Sort == req.Filters.Sort.Value)
                .WhereIF(req.Filters.UpdatorId.HasValue, t => t.UpdatorId == req.Filters.UpdatorId.Value)
                .Select(t => new TenantRoleDto(), true)  // true表示自动映射其他字段
                .SortBy(req.Sorts);

        // 执行分页查询
        RefAsync<int> totalCount = 0;
        var list = await query.ToPageListAsync(req.Pagination.PageIndex, req.Pagination.PageSize, totalCount);

        return new ListRes<TenantRoleDto>
        {
            Data = list,
            Total = totalCount.Value
        };
    }

    /// <summary>
    /// 根据条件获取TenantRole列表
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>TenantRole列表</returns>
    public async Task<List<TenantRole>> GetListByConditionAsync(System.Linq.Expressions.Expression<Func<TenantRole, bool>> predicate)
    {
        return await Context.Queryable<TenantRole>()
            .Where(predicate)
            .ToListAsync();
    }

    /// <summary>
    /// 根据条件获取TenantRole数量
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>TenantRole数量</returns>
    public async Task<int> GetCountByConditionAsync(System.Linq.Expressions.Expression<Func<TenantRole, bool>> predicate)
    {
        return await Context.Queryable<TenantRole>()
            .Where(predicate)
            .CountAsync();
    }

    /// <summary>
    /// 检查TenantRole是否存在
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>是否存在</returns>
    public async Task<bool> ExistsAsync(System.Linq.Expressions.Expression<Func<TenantRole, bool>> predicate)
    {
        return await Context.Queryable<TenantRole>()
            .Where(predicate)
            .AnyAsync();
    }

    /// <summary>
    /// 获取TenantRole的第一条记录
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>TenantRole实体</returns>
    public async Task<TenantRole> GetFirstOrDefaultAsync(System.Linq.Expressions.Expression<Func<TenantRole, bool>> predicate)
    {
        return await Context.Queryable<TenantRole>()
            .Where(predicate)
            .FirstAsync();
    }

    /// <summary>
    /// 批量插入TenantRole
    /// </summary>
    /// <param name="entities">TenantRole实体列表</param>
    /// <returns>插入结果</returns>
    public async Task<bool> InsertBatchAsync(List<TenantRole> entities)
    {
        return await Context.Insertable(entities).ExecuteCommandAsync() > 0;
    }

    /// <summary>
    /// 批量更新TenantRole
    /// </summary>
    /// <param name="entities">TenantRole实体列表</param>
    /// <returns>更新结果</returns>
    public async Task<bool> UpdateBatchAsync(List<TenantRole> entities)
    {
        return await Context.Updateable(entities).ExecuteCommandAsync() > 0;
    }
}
