using System;
using System.Linq;
using System.Threading.Tasks;
using CYSF.Core.Extensions;
using CYSF.Repositories.Extensions;
using CYSF.Core.Providers;
using CYSF.Models.Entities;
using CYSF.Models.Request;
using CYSF.Models.Request.Dic;
using CYSF.Models.Response;
using CYSF.Repositories.Base;
using Mapster;
using SqlSugar;

namespace CYSF.Repositories;

/// <summary>
/// Dic仓储
/// </summary>
public class DicRepository(ISqlSugarClient db) : BaseRepository<Dic>(db)
{
    /// <summary>
    /// 获取Dic分页列表
    /// </summary>
    /// <param name="req">分页查询请求</param>
    /// <returns>Dic分页列表</returns>
    public async Task<ListRes<DicDto>> GetPageListAsync(PageListReq<DicPageListReq> req)
    {
        // 构建查询
        var query = Context.Queryable<Dic>()
                .WhereIF(req.Filters.Id.HasValue, t => t.Id == req.Filters.Id.Value)
                .WhereIF(req.Filters.CreateTime != null && req.Filters.CreateTime.Count > 0, t => t.CreateTime >= req.Filters.CreateTime[0])
                .WhereIF(req.Filters.CreateTime != null && req.Filters.CreateTime.Count > 1, t => t.CreateTime <= req.Filters.CreateTime[1])
                .WhereIF(req.Filters.UpdateTime != null && req.Filters.UpdateTime.Count > 0, t => t.UpdateTime >= req.Filters.UpdateTime[0])
                .WhereIF(req.Filters.UpdateTime != null && req.Filters.UpdateTime.Count > 1, t => t.UpdateTime <= req.Filters.UpdateTime[1])
                .Select(t => new DicDto(), true)  // true表示自动映射其他字段
                .SortBy(req.Sorts);

        // 执行分页查询
        RefAsync<int> totalCount = 0;
        var list = await query.ToPageListAsync(req.Pagination.PageIndex, req.Pagination.PageSize, totalCount);

        return new ListRes<DicDto>
        {
            Data = list,
            Total = totalCount.Value
        };
    }

    /// <summary>
    /// 根据条件获取Dic列表
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>Dic列表</returns>
    public async Task<List<Dic>> GetListByConditionAsync(System.Linq.Expressions.Expression<Func<Dic, bool>> predicate)
    {
        return await Context.Queryable<Dic>()
            .Where(predicate)
            .ToListAsync();
    }

    /// <summary>
    /// 根据条件获取Dic数量
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>Dic数量</returns>
    public async Task<int> GetCountByConditionAsync(System.Linq.Expressions.Expression<Func<Dic, bool>> predicate)
    {
        return await Context.Queryable<Dic>()
            .Where(predicate)
            .CountAsync();
    }

    /// <summary>
    /// 检查Dic是否存在
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>是否存在</returns>
    public async Task<bool> ExistsAsync(System.Linq.Expressions.Expression<Func<Dic, bool>> predicate)
    {
        return await Context.Queryable<Dic>()
            .Where(predicate)
            .AnyAsync();
    }

    /// <summary>
    /// 获取Dic的第一条记录
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>Dic实体</returns>
    public async Task<Dic> GetFirstOrDefaultAsync(System.Linq.Expressions.Expression<Func<Dic, bool>> predicate)
    {
        return await Context.Queryable<Dic>()
            .Where(predicate)
            .FirstAsync();
    }

    /// <summary>
    /// 批量插入Dic
    /// </summary>
    /// <param name="entities">Dic实体列表</param>
    /// <returns>插入结果</returns>
    public async Task<bool> InsertBatchAsync(List<Dic> entities)
    {
        return await Context.Insertable(entities).ExecuteCommandAsync() > 0;
    }

    /// <summary>
    /// 批量更新Dic
    /// </summary>
    /// <param name="entities">Dic实体列表</param>
    /// <returns>更新结果</returns>
    public async Task<bool> UpdateBatchAsync(List<Dic> entities)
    {
        return await Context.Updateable(entities).ExecuteCommandAsync() > 0;
    }
}
