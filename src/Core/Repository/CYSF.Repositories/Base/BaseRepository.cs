using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using CYSF.Models.Request;
using CYSF.Models.Response;
using SqlSugar;

namespace CYSF.Repositories.Base
{
    /// <summary>
    /// 数据库基础仓储类
    /// 提供通用的CRUD操作和事务管理功能
    /// 使用SqlSugar ORM框架，确保线程安全
    /// </summary>
    /// <typeparam name="T">实体类型，必须是类且具有无参构造函数</typeparam>
    public class BaseRepository<T>(ISqlSugarClient context) : SimpleClient<T>(context)
        where T : class, new()
    {
        // /// <summary>
        // /// 构造函数
        // /// </summary>
        // /// <param name="context">SqlSugar客户端实例，通过依赖注入提供</param>

        #region Insert 插入操作

        /// <summary>
        /// 添加单个实体
        /// </summary>
        /// <param name="entity">要添加的实体</param>
        /// <returns>是否添加成功</returns>
        public bool Add(T entity)
        {
            return Context.Insertable(entity).ExecuteCommand() > 0;
        }

        /// <summary>
        /// 异步添加单个实体
        /// </summary>
        /// <param name="entity">要添加的实体</param>
        /// <returns>是否添加成功</returns>
        public async Task<bool> AddAsync(T entity)
        {
            return await Context.Insertable(entity).ExecuteCommandAsync() > 0;
        }

        /// <summary>
        /// 添加实体并返回自增ID
        /// </summary>
        /// <param name="entity">要添加的实体</param>
        /// <returns>新插入记录的ID</returns>
        public int AddReturnIdentity(T entity)
        {
            return Context.Insertable(entity).ExecuteReturnIdentity();
        }

        /// <summary>
        /// 异步添加实体并返回自增ID
        /// </summary>
        /// <param name="entity">要添加的实体</param>
        /// <returns>新插入记录的ID</returns>
        public async Task<int> AddReturnIdentityAsync(T entity)
        {
            return await Context.Insertable(entity).ExecuteReturnIdentityAsync();
        }

        /// <summary>
        /// 添加实体并返回长整型自增ID
        /// </summary>
        /// <param name="entity">要添加的实体</param>
        /// <returns>新插入记录的长整型ID</returns>
        public long AddReturnBigIdentity(T entity)
        {
            return Context.Insertable(entity).ExecuteReturnBigIdentity();
        }

        /// <summary>
        /// 异步添加实体并返回长整型自增ID
        /// </summary>
        /// <param name="entity">要添加的实体</param>
        /// <returns>新插入记录的长整型ID</returns>
        public async Task<long> AddReturnBigIdentityAsync(T entity)
        {
            return await Context.Insertable(entity).ExecuteReturnIdentityAsync();
        }

        /// <summary>
        /// 批量添加实体
        /// </summary>
        /// <param name="entities">要添加的实体列表</param>
        /// <returns>是否添加成功</returns>
        public bool AddRange(List<T> entities)
        {
            return Context.Insertable(entities).ExecuteCommand() > 0;
        }

        /// <summary>
        /// 异步批量添加实体
        /// </summary>
        /// <param name="entities">要添加的实体列表</param>
        /// <returns>是否添加成功</returns>
        public async Task<bool> AddRangeAsync(List<T> entities)
        {
            return await Context.Insertable(entities).ExecuteCommandAsync() > 0;
        }

        /// <summary>
        /// 添加实体并返回完整的实体对象（包含自增ID等）
        /// </summary>
        /// <param name="entity">要添加的实体</param>
        /// <returns>添加后的完整实体对象</returns>
        public T AddReturnEntity(T entity)
        {
            return Context.Insertable(entity).ExecuteReturnEntity();
        }

        /// <summary>
        /// 异步添加实体并返回完整的实体对象（包含自增ID等）
        /// </summary>
        /// <param name="entity">要添加的实体</param>
        /// <returns>添加后的完整实体对象</returns>
        public async Task<T> AddReturnEntityAsync(T entity)
        {
            return await Context.Insertable(entity).ExecuteReturnEntityAsync();
        }

        #endregion

        #region Delete 删除操作

        /// <summary>
        /// 根据主键删除实体（int类型主键）
        /// </summary>
        /// <param name="key">主键值</param>
        /// <returns>是否删除成功</returns>
        public bool Delete(int key)
        {
            return Context.Deleteable<T>().In(key).ExecuteCommandHasChange();
        }

        /// <summary>
        /// 异步根据主键删除实体（int类型主键）
        /// </summary>
        /// <param name="key">主键值</param>
        /// <returns>是否删除成功</returns>
        public async Task<bool> DeleteAsync(int key)
        {
            return await Context.Deleteable<T>().In(key).ExecuteCommandHasChangeAsync();
        }

        /// <summary>
        /// 根据主键删除实体（long类型主键）
        /// </summary>
        /// <param name="key">主键值</param>
        /// <returns>是否删除成功</returns>
        public bool Delete(long key)
        {
            return Context.Deleteable<T>().In(key).ExecuteCommandHasChange();
        }

        /// <summary>
        /// 异步根据主键删除实体（long类型主键）
        /// </summary>
        /// <param name="key">主键值</param>
        /// <returns>是否删除成功</returns>
        public async Task<bool> DeleteAsync(long key)
        {
            return await Context.Deleteable<T>().In(key).ExecuteCommandHasChangeAsync();
        }

        /// <summary>
        /// 根据条件删除实体
        /// </summary>
        /// <param name="where">删除条件</param>
        /// <returns>是否删除成功</returns>
        public override bool Delete(Expression<Func<T, bool>> where)
        {
            return Context.Deleteable<T>().Where(where).ExecuteCommandHasChange();
        }

        /// <summary>
        /// 异步根据条件删除实体
        /// </summary>
        /// <param name="where">删除条件</param>
        /// <returns>是否删除成功</returns>
        public override async Task<bool> DeleteAsync(Expression<Func<T, bool>> where)
        {
            return await Context.Deleteable<T>().Where(where).ExecuteCommandHasChangeAsync();
        }

        #endregion

        #region Update 更新操作

        /// <summary>
        /// 更新实体（根据主键）
        /// </summary>
        /// <param name="entity">要更新的实体</param>
        /// <returns>是否更新成功</returns>
        public override bool Update(T entity)
        {
            return Context.Updateable(entity).ExecuteCommandHasChange();
        }

        /// <summary>
        /// 根据指定列作为条件更新实体
        /// </summary>
        /// <param name="entity">要更新的实体</param>
        /// <param name="whereColumns">作为条件的列</param>
        /// <returns>是否更新成功</returns>
        public bool Update(T entity, Expression<Func<T, object>> whereColumns)
        {
            return Context.Updateable(entity).WhereColumns(whereColumns).ExecuteCommandHasChange();
        }

        /// <summary>
        /// 只更新指定的列
        /// </summary>
        /// <param name="entity">要更新的实体</param>
        /// <param name="updateColumns">要更新的列</param>
        /// <returns>是否更新成功</returns>
        public bool UpdateColumns(T entity, Expression<Func<T, object>> updateColumns)
        {
            return Context.Updateable(entity).UpdateColumns(updateColumns).ExecuteCommandHasChange();
        }

        /// <summary>
        /// 根据条件更新指定列
        /// </summary>
        /// <param name="setColumns">要设置的列和值</param>
        /// <param name="where">更新条件</param>
        /// <returns>是否更新成功</returns>
        public bool UpdateColumns(Expression<Func<T, T>> setColumns, Expression<Func<T, bool>> where)
        {
            return Context.Updateable<T>().SetColumns(setColumns).Where(where).ExecuteCommandHasChange();
        }

        /// <summary>
        /// 根据条件更新指定列（布尔表达式设置）
        /// </summary>
        /// <param name="setColumns">要设置的列和值（布尔表达式）</param>
        /// <param name="where">更新条件</param>
        /// <returns>是否更新成功</returns>
        public bool UpdateColumns(Expression<Func<T, bool>> setColumns, Expression<Func<T, bool>> where)
        {
            return Context.Updateable<T>().SetColumns(setColumns).Where(where).ExecuteCommandHasChange();
        }

        /// <summary>
        /// 异步更新实体（根据主键）
        /// </summary>
        /// <param name="entity">要更新的实体</param>
        /// <returns>是否更新成功</returns>
        public override async Task<bool> UpdateAsync(T entity)
        {
            return await Context.Updateable(entity).ExecuteCommandHasChangeAsync();
        }

        /// <summary>
        /// 异步只更新指定的列
        /// </summary>
        /// <param name="entity">要更新的实体</param>
        /// <param name="updateColumns">要更新的列</param>
        /// <returns>是否更新成功</returns>
        public async Task<bool> UpdateColumnsAsync(T entity, Expression<Func<T, object>> updateColumns)
        {
            return await Context.Updateable(entity).UpdateColumns(updateColumns).ExecuteCommandHasChangeAsync();
        }

        /// <summary>
        /// 异步根据指定列作为条件更新实体
        /// </summary>
        /// <param name="entity">要更新的实体</param>
        /// <param name="whereColumns">作为条件的列</param>
        /// <returns>是否更新成功</returns>
        public async Task<bool> UpdateAsync(T entity, Expression<Func<T, object>> whereColumns)
        {
            return await Context.Updateable(entity).WhereColumns(whereColumns).ExecuteCommandHasChangeAsync();
        }

        /// <summary>
        /// 异步根据条件更新指定列
        /// </summary>
        /// <param name="setColumns">要设置的列和值</param>
        /// <param name="where">更新条件</param>
        /// <returns>是否更新成功</returns>
        public async Task<bool> UpdateColumnsAsync(Expression<Func<T, T>> setColumns, Expression<Func<T, bool>> where)
        {
            return await Context.Updateable<T>().SetColumns(setColumns).Where(where).ExecuteCommandHasChangeAsync();
        }

        /// <summary>
        /// 异步根据条件更新指定列（布尔表达式设置）
        /// </summary>
        /// <param name="setColumns">要设置的列和值（布尔表达式）</param>
        /// <param name="where">更新条件</param>
        /// <returns>是否更新成功</returns>
        public async Task<bool> UpdateColumnsAsync(Expression<Func<T, bool>> setColumns, Expression<Func<T, bool>> where)
        {
            return await Context.Updateable<T>().SetColumns(setColumns).Where(where).ExecuteCommandHasChangeAsync();
        }
        #region Query 查询操作

        /// <summary>
        /// 根据条件统计记录数量
        /// </summary>
        /// <param name="where">查询条件，为null时统计所有记录</param>
        /// <returns>记录数量</returns>
        public override int Count(Expression<Func<T, bool>> where)
        {
            return Context.Queryable<T>().WhereIF(where != null, where).Count();
        }

        /// <summary>
        /// 异步根据条件统计记录数量
        /// </summary>
        /// <param name="where">查询条件，为null时统计所有记录</param>
        /// <returns>记录数量</returns>
        public override async Task<int> CountAsync(Expression<Func<T, bool>> where)
        {
            return await Context.Queryable<T>().WhereIF(where != null, where).CountAsync();
        }

        /// <summary>
        /// 检查是否存在满足条件的记录
        /// </summary>
        /// <param name="where">查询条件</param>
        /// <returns>是否存在</returns>
        public bool Exist(Expression<Func<T, bool>> where)
        {
            return Context.Queryable<T>().WhereIF(where != null, where).Any();
        }

        /// <summary>
        /// 异步检查是否存在满足条件的记录
        /// </summary>
        /// <param name="where">查询条件</param>
        /// <returns>是否存在</returns>
        public async Task<bool> ExistAsync(Expression<Func<T, bool>> where)
        {
            return await Context.Queryable<T>().WhereIF(where != null, where).AnyAsync();
        }

        /// <summary>
        /// 获取第一条满足条件的记录
        /// </summary>
        /// <param name="where">查询条件</param>
        /// <returns>实体对象，如果不存在则抛出异常</returns>
        public new T GetFirst(Expression<Func<T, bool>> where)
        {
            return Context.Queryable<T>().WhereIF(where != null, where).First();
        }

        /// <summary>
        /// 异步获取第一条满足条件的记录
        /// </summary>
        /// <param name="where">查询条件</param>
        /// <returns>实体对象，如果不存在则抛出异常</returns>
        public new async Task<T> GetFirstAsync(Expression<Func<T, bool>> where)
        {
            return await Context.Queryable<T>().WhereIF(where != null, where).FirstAsync();
        }

        /// <summary>
        /// 获取满足条件的所有记录
        /// </summary>
        /// <param name="where">查询条件，为null时获取所有记录</param>
        /// <returns>实体列表</returns>
        public override List<T> GetList(Expression<Func<T, bool>> where)
        {
            return Context.Queryable<T>().WhereIF(where != null, where).ToList();
        }

        /// <summary>
        /// 异步获取满足条件的所有记录
        /// </summary>
        /// <param name="where">查询条件，为null时获取所有记录</param>
        /// <returns>实体列表</returns>
        public override async Task<List<T>> GetListAsync(Expression<Func<T, bool>> where)
        {
            return await Context.Queryable<T>().WhereIF(where != null, where).ToListAsync();
        }

        /// <summary>
        /// 分页查询满足条件的记录
        /// </summary>
        /// <param name="pagination">分页参数</param>
        /// <param name="sorts">排序配置列表</param>
        /// <param name="where">查询条件</param>
        /// <returns>分页结果，包含记录和总数</returns>
        public ListRes<T> GetPageList(Pagination pagination, List<PageSort> sorts, Expression<Func<T, bool>> where)
        {
            var total = 0;
            var query = Context.Queryable<T>().WhereIF(where != null, where);
            var result = query
                .SortBy(sorts)
                .ToPageList(pagination.PageIndex, pagination.PageSize, ref total);
            return new ListRes<T>
            {
                Total = total,
                Data = result
            };
        }

        /// <summary>
        /// 异步分页查询满足条件的记录
        /// </summary>
        /// <param name="pagination">分页参数</param>
        /// <param name="sorts">排序配置列表</param>
        /// <param name="where">查询条件</param>
        /// <returns>分页结果，包含记录和总数</returns>
        public async Task<ListRes<T>> GetPageListAsync(Pagination pagination, List<PageSort> sorts,
            Expression<Func<T, bool>> where)
        {
            RefAsync<int> total = 0;
            var result = await Context.Queryable<T>().WhereIF(where != null, where)
                .SortBy(sorts)
                .ToPageListAsync(pagination.PageIndex, pagination.PageSize, total);

            return new ListRes<T>
            {
                Total = total,
                Data = result
            };
        }

        #endregion

        #endregion
    }
}

