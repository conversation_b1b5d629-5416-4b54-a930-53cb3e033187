using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CYSF.Core.Extensions;
using CYSF.Repositories.Extensions;
using CYSF.Core.Providers;
using CYSF.Models.Entities;
using CYSF.Models.Request;
using CYSF.Models.Request.Tenant;
using CYSF.Models.Response;
using CYSF.Repositories.Base;
using Mapster;
using SqlSugar;

namespace CYSF.Repositories;

/// <summary>
/// Tenant仓储
/// </summary>
public class TenantRepository(ISqlSugarClient db) : BaseRepository<Tenant>(db)
{
    /// <summary>
    /// 获取Tenant分页列表
    /// </summary>
    /// <param name="req">分页查询请求</param>
    /// <returns>Tenant分页列表</returns>
    public async Task<ListRes<TenantDto>> GetPageListAsync(PageListReq<TenantPageListReq> req)
    {
        // 构建查询
        var query = Context.Queryable<Tenant>()
                .WhereIF(req.Filters.CreateTime != null && req.Filters.CreateTime.Count > 0, t => t.CreateTime >= req.Filters.CreateTime[0])
                .WhereIF(req.Filters.CreateTime != null && req.Filters.CreateTime.Count > 1, t => t.CreateTime <= req.Filters.CreateTime[1])
                .WhereIF(req.Filters.UpdateTime != null && req.Filters.UpdateTime.Count > 0, t => t.UpdateTime >= req.Filters.UpdateTime[0])
                .WhereIF(req.Filters.UpdateTime != null && req.Filters.UpdateTime.Count > 1, t => t.UpdateTime <= req.Filters.UpdateTime[1])
                .WhereIF(!string.IsNullOrEmpty(req.Filters.Code), t => t.Code.Contains(req.Filters.Code))
                .WhereIF(!string.IsNullOrEmpty(req.Filters.Name), t => t.Name.Contains(req.Filters.Name))
                .WhereIF(!string.IsNullOrEmpty(req.Filters.ShortName), t => t.ShortName.Contains(req.Filters.ShortName))
                .WhereIF(!string.IsNullOrEmpty(req.Filters.CompanyName), t => t.CompanyName.Contains(req.Filters.CompanyName))
                .WhereIF(!string.IsNullOrEmpty(req.Filters.ContactName), t => t.ContactName.Contains(req.Filters.ContactName))
                .WhereIF(!string.IsNullOrEmpty(req.Filters.ContactPhone), t => t.ContactPhone.Contains(req.Filters.ContactPhone))
                .WhereIF(!string.IsNullOrEmpty(req.Filters.DicLevel), t => t.DicLevel.Contains(req.Filters.DicLevel))
                .WhereIF(req.Filters.ExpireTime != null && req.Filters.ExpireTime.Count > 0, t => t.ExpireTime >= req.Filters.ExpireTime[0])
                .WhereIF(req.Filters.ExpireTime != null && req.Filters.ExpireTime.Count > 1, t => t.ExpireTime <= req.Filters.ExpireTime[1])
                .WhereIF(req.Filters.UpdatorId.HasValue, t => t.UpdatorId == req.Filters.UpdatorId.Value)
                .Select(t => new TenantDto(), true)  // true表示自动映射其他字段
                .SortBy(req.Sorts);

        // 执行分页查询
        RefAsync<int> totalCount = 0;
        var list = await query.ToPageListAsync(req.Pagination.PageIndex, req.Pagination.PageSize, totalCount);

        return new ListRes<TenantDto>
        {
            Data = list,
            Total = totalCount.Value
        };
    }

    /// <summary>
    /// 根据条件获取Tenant列表
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>Tenant列表</returns>
    public async Task<List<Tenant>> GetListByConditionAsync(System.Linq.Expressions.Expression<Func<Tenant, bool>> predicate)
    {
        return await Context.Queryable<Tenant>()
            .Where(predicate)
            .ToListAsync();
    }

    /// <summary>
    /// 根据条件获取Tenant数量
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>Tenant数量</returns>
    public async Task<int> GetCountByConditionAsync(System.Linq.Expressions.Expression<Func<Tenant, bool>> predicate)
    {
        return await Context.Queryable<Tenant>()
            .Where(predicate)
            .CountAsync();
    }

    /// <summary>
    /// 检查Tenant是否存在
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>是否存在</returns>
    public async Task<bool> ExistsAsync(System.Linq.Expressions.Expression<Func<Tenant, bool>> predicate)
    {
        return await Context.Queryable<Tenant>()
            .Where(predicate)
            .AnyAsync();
    }

    /// <summary>
    /// 获取Tenant的第一条记录
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>Tenant实体</returns>
    public async Task<Tenant> GetFirstOrDefaultAsync(System.Linq.Expressions.Expression<Func<Tenant, bool>> predicate)
    {
        return await Context.Queryable<Tenant>()
            .Where(predicate)
            .FirstAsync();
    }

    /// <summary>
    /// 批量插入Tenant
    /// </summary>
    /// <param name="entities">Tenant实体列表</param>
    /// <returns>插入结果</returns>
    public async Task<bool> InsertBatchAsync(List<Tenant> entities)
    {
        return await Context.Insertable(entities).ExecuteCommandAsync() > 0;
    }

    /// <summary>
    /// 批量更新Tenant
    /// </summary>
    /// <param name="entities">Tenant实体列表</param>
    /// <returns>更新结果</returns>
    public async Task<bool> UpdateBatchAsync(List<Tenant> entities)
    {
        return await Context.Updateable(entities).ExecuteCommandAsync() > 0;
    }
}
