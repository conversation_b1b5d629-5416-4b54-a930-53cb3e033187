using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CYSF.Core.Extensions;
using CYSF.Repositories.Extensions;
using CYSF.Core.Providers;
using CYSF.Models.Entities;
using CYSF.Models.Request;
using CYSF.Models.Request.TenantUser;
using CYSF.Models.Response;
using CYSF.Repositories.Base;
using Mapster;
using SqlSugar;

namespace CYSF.Repositories;

/// <summary>
/// TenantUser仓储
/// </summary>
public class TenantUserRepository(ISqlSugarClient db) : BaseRepository<TenantUser>(db)
{
    /// <summary>
    /// 获取TenantUser分页列表
    /// </summary>
    /// <param name="req">分页查询请求</param>
    /// <returns>TenantUser分页列表</returns>
    public async Task<ListRes<TenantUserDto>> GetPageListAsync(PageListReq<TenantUserPageListReq> req)
    {
        // 构建查询
        var query = Context.Queryable<TenantUser>()
                .WhereIF(req.Filters.CreateTime != null && req.Filters.CreateTime.Count > 0, t => t.CreateTime >= req.Filters.CreateTime[0])
                .WhereIF(req.Filters.CreateTime != null && req.Filters.CreateTime.Count > 1, t => t.CreateTime <= req.Filters.CreateTime[1])
                .WhereIF(req.Filters.UpdateTime != null && req.Filters.UpdateTime.Count > 0, t => t.UpdateTime >= req.Filters.UpdateTime[0])
                .WhereIF(req.Filters.UpdateTime != null && req.Filters.UpdateTime.Count > 1, t => t.UpdateTime <= req.Filters.UpdateTime[1])
                .WhereIF(req.Filters.TenantId.HasValue, t => t.TenantId == req.Filters.TenantId.Value)
                .WhereIF(!string.IsNullOrEmpty(req.Filters.UserName), t => t.UserName.Contains(req.Filters.UserName))
                .WhereIF(!string.IsNullOrEmpty(req.Filters.Password), t => t.Password.Contains(req.Filters.Password))
                .WhereIF(!string.IsNullOrEmpty(req.Filters.PasswordSalt), t => t.PasswordSalt.Contains(req.Filters.PasswordSalt))
                .WhereIF(req.Filters.RoleId.HasValue, t => t.RoleId == req.Filters.RoleId.Value)
                .WhereIF(!string.IsNullOrEmpty(req.Filters.Name), t => t.Name.Contains(req.Filters.Name))
                .WhereIF(!string.IsNullOrEmpty(req.Filters.Mobile), t => t.Mobile.Contains(req.Filters.Mobile))
                .WhereIF(!string.IsNullOrEmpty(req.Filters.Email), t => t.Email.Contains(req.Filters.Email))
                .WhereIF(!string.IsNullOrEmpty(req.Filters.LastLoginIp), t => t.LastLoginIp.Contains(req.Filters.LastLoginIp))
                .WhereIF(req.Filters.LastLoginTime != null && req.Filters.LastLoginTime.Count > 0, t => t.LastLoginTime >= req.Filters.LastLoginTime[0])
                .WhereIF(req.Filters.LastLoginTime != null && req.Filters.LastLoginTime.Count > 1, t => t.LastLoginTime <= req.Filters.LastLoginTime[1])
                .WhereIF(req.Filters.UpdatorId.HasValue, t => t.UpdatorId == req.Filters.UpdatorId.Value)
                .Select(t => new TenantUserDto(), true)  // true表示自动映射其他字段
                .SortBy(req.Sorts);

        // 执行分页查询
        RefAsync<int> totalCount = 0;
        var list = await query.ToPageListAsync(req.Pagination.PageIndex, req.Pagination.PageSize, totalCount);

        return new ListRes<TenantUserDto>
        {
            Data = list,
            Total = totalCount.Value
        };
    }

    /// <summary>
    /// 根据条件获取TenantUser列表
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>TenantUser列表</returns>
    public async Task<List<TenantUser>> GetListByConditionAsync(System.Linq.Expressions.Expression<Func<TenantUser, bool>> predicate)
    {
        return await Context.Queryable<TenantUser>()
            .Where(predicate)
            .ToListAsync();
    }

    /// <summary>
    /// 根据条件获取TenantUser数量
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>TenantUser数量</returns>
    public async Task<int> GetCountByConditionAsync(System.Linq.Expressions.Expression<Func<TenantUser, bool>> predicate)
    {
        return await Context.Queryable<TenantUser>()
            .Where(predicate)
            .CountAsync();
    }

    /// <summary>
    /// 检查TenantUser是否存在
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>是否存在</returns>
    public async Task<bool> ExistsAsync(System.Linq.Expressions.Expression<Func<TenantUser, bool>> predicate)
    {
        return await Context.Queryable<TenantUser>()
            .Where(predicate)
            .AnyAsync();
    }

    /// <summary>
    /// 获取TenantUser的第一条记录
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>TenantUser实体</returns>
    public async Task<TenantUser> GetFirstOrDefaultAsync(System.Linq.Expressions.Expression<Func<TenantUser, bool>> predicate)
    {
        return await Context.Queryable<TenantUser>()
            .Where(predicate)
            .FirstAsync();
    }

    /// <summary>
    /// 批量插入TenantUser
    /// </summary>
    /// <param name="entities">TenantUser实体列表</param>
    /// <returns>插入结果</returns>
    public async Task<bool> InsertBatchAsync(List<TenantUser> entities)
    {
        return await Context.Insertable(entities).ExecuteCommandAsync() > 0;
    }

    /// <summary>
    /// 批量更新TenantUser
    /// </summary>
    /// <param name="entities">TenantUser实体列表</param>
    /// <returns>更新结果</returns>
    public async Task<bool> UpdateBatchAsync(List<TenantUser> entities)
    {
        return await Context.Updateable(entities).ExecuteCommandAsync() > 0;
    }
}
