using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CYSF.Core.Extensions;
using CYSF.Repositories.Extensions;
using CYSF.Core.Providers;
using CYSF.Models.Entities;
using CYSF.Models.Request;
using CYSF.Models.Request.UserLoginRecord;
using CYSF.Models.Response;
using CYSF.Repositories.Base;
using Mapster;
using SqlSugar;

namespace CYSF.Repositories;

/// <summary>
/// UserLoginRecord仓储
/// </summary>
public class UserLoginRecordRepository(ISqlSugarClient db) : BaseRepository<UserLoginRecord>(db)
{
    /// <summary>
    /// 获取UserLoginRecord分页列表
    /// </summary>
    /// <param name="req">分页查询请求</param>
    /// <returns>UserLoginRecord分页列表</returns>
    public async Task<ListRes<UserLoginRecordDto>> GetPageListAsync(PageListReq<UserLoginRecordPageListReq> req)
    {
        // 构建查询
        var query = Context.Queryable<UserLoginRecord>()
                .WhereIF(req.Filters.CreateTime != null && req.Filters.CreateTime.Count > 0, t => t.CreateTime >= req.Filters.CreateTime[0])
                .WhereIF(req.Filters.CreateTime != null && req.Filters.CreateTime.Count > 1, t => t.CreateTime <= req.Filters.CreateTime[1])
                .WhereIF(req.Filters.UpdateTime != null && req.Filters.UpdateTime.Count > 0, t => t.UpdateTime >= req.Filters.UpdateTime[0])
                .WhereIF(req.Filters.UpdateTime != null && req.Filters.UpdateTime.Count > 1, t => t.UpdateTime <= req.Filters.UpdateTime[1])
                .WhereIF(req.Filters.TenantId.HasValue, t => t.TenantId == req.Filters.TenantId.Value)
                .WhereIF(req.Filters.UserId.HasValue, t => t.UserId == req.Filters.UserId.Value)
                .WhereIF(!string.IsNullOrEmpty(req.Filters.LoginIp), t => t.LoginIp.Contains(req.Filters.LoginIp))
                .WhereIF(!string.IsNullOrEmpty(req.Filters.Description), t => t.Description.Contains(req.Filters.Description))
                .WhereIF(req.Filters.RecordTime != null && req.Filters.RecordTime.Count > 0, t => t.RecordTime >= req.Filters.RecordTime[0])
                .WhereIF(req.Filters.RecordTime != null && req.Filters.RecordTime.Count > 1, t => t.RecordTime <= req.Filters.RecordTime[1])
                .Select(t => new UserLoginRecordDto(), true)  // true表示自动映射其他字段
                .SortBy(req.Sorts);

        // 执行分页查询
        RefAsync<int> totalCount = 0;
        var list = await query.ToPageListAsync(req.Pagination.PageIndex, req.Pagination.PageSize, totalCount);

        return new ListRes<UserLoginRecordDto>
        {
            Data = list,
            Total = totalCount.Value
        };
    }

    /// <summary>
    /// 根据条件获取UserLoginRecord列表
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>UserLoginRecord列表</returns>
    public async Task<List<UserLoginRecord>> GetListByConditionAsync(System.Linq.Expressions.Expression<Func<UserLoginRecord, bool>> predicate)
    {
        return await Context.Queryable<UserLoginRecord>()
            .Where(predicate)
            .ToListAsync();
    }

    /// <summary>
    /// 根据条件获取UserLoginRecord数量
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>UserLoginRecord数量</returns>
    public async Task<int> GetCountByConditionAsync(System.Linq.Expressions.Expression<Func<UserLoginRecord, bool>> predicate)
    {
        return await Context.Queryable<UserLoginRecord>()
            .Where(predicate)
            .CountAsync();
    }

    /// <summary>
    /// 检查UserLoginRecord是否存在
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>是否存在</returns>
    public async Task<bool> ExistsAsync(System.Linq.Expressions.Expression<Func<UserLoginRecord, bool>> predicate)
    {
        return await Context.Queryable<UserLoginRecord>()
            .Where(predicate)
            .AnyAsync();
    }

    /// <summary>
    /// 获取UserLoginRecord的第一条记录
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>UserLoginRecord实体</returns>
    public async Task<UserLoginRecord> GetFirstOrDefaultAsync(System.Linq.Expressions.Expression<Func<UserLoginRecord, bool>> predicate)
    {
        return await Context.Queryable<UserLoginRecord>()
            .Where(predicate)
            .FirstAsync();
    }

    /// <summary>
    /// 批量插入UserLoginRecord
    /// </summary>
    /// <param name="entities">UserLoginRecord实体列表</param>
    /// <returns>插入结果</returns>
    public async Task<bool> InsertBatchAsync(List<UserLoginRecord> entities)
    {
        return await Context.Insertable(entities).ExecuteCommandAsync() > 0;
    }

    /// <summary>
    /// 批量更新UserLoginRecord
    /// </summary>
    /// <param name="entities">UserLoginRecord实体列表</param>
    /// <returns>更新结果</returns>
    public async Task<bool> UpdateBatchAsync(List<UserLoginRecord> entities)
    {
        return await Context.Updateable(entities).ExecuteCommandAsync() > 0;
    }
}
