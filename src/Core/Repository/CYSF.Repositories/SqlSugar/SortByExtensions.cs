using System;
using System.Collections.Generic;
using System.Linq;
using CYSF.Core.Extensions;
using CYSF.Models.Request;
using SqlSugar;

namespace CYSF.Repositories.SqlSugar
{
    public static class SortByExtensions
    {
        public static ISugarQueryable<T> SortBy<T>(this ISugarQueryable<T> query, List<PageSort> sorts)
        {
            return query._SortBy(sorts);
        }

        private static ISugarQueryable<T> _SortBy<T>(this ISugarQueryable<T> query, List<PageSort> sorts)
        {
            if (sorts.Count == 0) return query;

            foreach (var sort in sorts.Where(sort => sort.SortField.IsNotNullOrEmpty()))
            {
                // 排序字段只能为英文/数字/下划线
                if (!sort.SortField.IsMatch(RegExpressions.sort_field))
                    throw new ArgumentException("排序参数错误");

                query = query.OrderBy($"{sort.SortField} {(sort.OrderDesc ? "DESC" : "ASC")}");
            }

            return query;
        }
    }
}