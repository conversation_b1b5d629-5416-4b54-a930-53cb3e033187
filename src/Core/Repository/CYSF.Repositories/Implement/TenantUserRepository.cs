using System;
using System.Linq;
using System.Threading.Tasks;
using CYSF.Core.Extensions;
using CYSF.Core.Providers;
using CYSF.Models.Entities;
using CYSF.Models.Request;
using CYSF.Models.Request.TenantUser;
using CYSF.Models.Response;

using CYSF.Repositories.SqlSugar;
using Mapster;
using SqlSugar;

namespace CYSF.Repositories.Implement;

/// <summary>
/// 租户用户仓储
/// </summary>
public class TenantUserRepository(ISqlSugarClient db) : BaseRepository<TenantUser>(db)
{
    /// <summary>
    /// 获取用户分页列表（包含关联的角色名称和租户名称）
    /// </summary>
    /// <param name="req">分页查询请求</param>
    /// <returns>用户分页列表</returns>
    public async Task<ListRes<TenantUserDto>> GetPageListAsync(PageListReq<TenantUserPageListReq> req)
    {
        // 构建查询
        var query = Context.Queryable<TenantUser>()
                .LeftJoin<TenantRole>((u, r) => u.RoleId == r.Id)
                .LeftJoin<Tenant>((u, r, t) => u.TenantId == t.Id)
                .WhereIF(req.Filters.TenantId.HasValue, (u, r, t) => u.TenantId == req.Filters.TenantId.Value)
                .WhereIF(req.Filters.UserName.IsNotNullOrEmpty(),(u, r, t)=> u.UserName.Contains(req.Filters.UserName))
                .WhereIF(req.Filters.Name.IsNotNullOrEmpty(),(u, r, t)=> u.Name.Contains(req.Filters.Name))
                .WhereIF(req.Filters.Mobile.IsNotNullOrEmpty(),(u, r, t)=> u.Mobile.Contains(req.Filters.Mobile))
                .WhereIF(req.Filters.Email.IsNotNullOrEmpty(),(u, r, t)=> u.Email.Contains(req.Filters.Email))
                .WhereIF(req.Filters.TenantId.HasValue,(u, r, t)=> u.TenantId == req.Filters.TenantId.Value)
                .WhereIF(req.Filters.UserState.HasValue,(u, r, t)=> u.UserState == req.Filters.UserState.Value)
                .WhereIF(req.Filters.RoleId.HasValue,(u, r, t)=> u.RoleId == req.Filters.RoleId.Value)
                .WhereIF(req.Filters.CreateTime != null && req.Filters.CreateTime.Count > 0,(u, r, t)=> t.CreateTime >= req.Filters.CreateTime[0])
                .WhereIF(req.Filters.CreateTime != null && req.Filters.CreateTime.Count > 1,(u, r, t)=> t.CreateTime <= req.Filters.CreateTime[1])
                .WhereIF(req.Filters.LastLoginTime != null && req.Filters.LastLoginTime.Count > 0,(u, r, t)=> u.LastLoginTime >= req.Filters.LastLoginTime[0])
                .WhereIF(req.Filters.LastLoginTime != null && req.Filters.LastLoginTime.Count > 1,(u, r, t)=> u.LastLoginTime <= req.Filters.LastLoginTime[1])
                .Select((u, r, t) => new TenantUserDto
                {
                    RoleName = r.Name ?? "",
                    TenantName = t.ShortName ?? ""
                }, true)  // true表示自动映射其他字段
                .SortBy(req.Sorts);
        // 执行分页查询
        RefAsync<int> totalCount = 0;
        var list = await query.ToPageListAsync(req.Pagination.PageIndex, req.Pagination.PageSize, totalCount);

        // 处理敏感字段
        foreach (var item in list)
        {
            item.Password = "******";
            item.PasswordSalt = "******";
        }

        return new ListRes<TenantUserDto>
        {
            Data = list,
            Total = totalCount.Value
        };
    }
}
