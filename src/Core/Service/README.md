# ⚙️ CYSF.Services - 业务服务层

## 📋 项目概述

CYSF.Services是系统的业务服务层，负责实现核心业务逻辑、数据处理和业务规则验证。该层位于应用服务层和数据访问层之间，提供可重用的业务操作。

## 🏗️ 项目架构

### 技术栈
- **框架**: .NET 8.0
- **ORM**: SqlSugar (线程安全的 SqlSugarScope)
- **对象映射**: Mapster
- **缓存**: 统一缓存抽象（Redis/MemoryCache 可配置）
- **依赖注入**: Autofac (模块化注册)

### 项目结构
```
CYSF.Services/
├── TenantService.cs           # 租户业务服务
├── TenantUserService.cs       # 用户业务服务
├── RoleService.cs             # 角色业务服务 (代码生成)
├── UserTenantRelaService.cs   # 用户租户关系服务 (代码生成)
├── UserLoginRecordService.cs  # 用户登录记录服务 (代码生成)
└── CYSF.Services.csproj       # 项目文件
```

## 🚀 核心功能

### 1. 租户业务服务 (TenantService)
- **基础CRUD**: 租户的创建、读取、更新、删除
- **业务验证**: 租户编号唯一性验证
- **初始化逻辑**: 租户创建时的初始化操作
- **状态管理**: 租户状态变更

<augment_code_snippet path="src/Core/Service/CYSF.Services/TenantService.cs" mode="EXCERPT">
````csharp
public async Task<Tenant> CreateAsync(Tenant tenant)
{
    // 业务规则验证
    if (await ExistsByCodeAsync(tenant.Code))
        throw new BusinessException("租户编码已存在");

    // 设置基础信息
    tenant.Id = SnowflakeHelper.NextId();
    tenant.CreateTime = DateTime.Now;

    // 保存租户
    return await _tenantRepository.CreateAsync(tenant);
}
````
</augment_code_snippet>

### 2. 用户业务服务 (TenantUserService)
- **用户管理**: 用户的完整生命周期管理
- **密码处理**: 密码加密、验证、重置
- **角色关联**: 用户角色分配和管理
- **状态控制**: 用户启用/禁用状态管理

### 3. 代码生成的服务
- **RoleService**: 角色管理业务逻辑
- **UserTenantRelaService**: 用户租户关系管理
- **UserLoginRecordService**: 用户登录记录管理

## 🔧 核心特性

### 1. 数据访问模式
```csharp
public class TenantService
{
    private readonly TenantRepository _tenantRepository;
    private readonly ISqlSugarClient _db;

    public TenantService(TenantRepository tenantRepository, ISqlSugarClient db)
    {
        _tenantRepository = tenantRepository;
        _db = db;
    }

    public async Task<Tenant> GetByIdAsync(int id)
    {
        return await _tenantRepository.GetByIdAsync(id);
    }
}
```

### 2. 业务验证
```csharp
public async Task<bool> CheckCodeAvailableAsync(string code, int? excludeId = null)
{
    var query = _db.Queryable<Tenant>().Where(x => x.Code == code);
    
    if (excludeId.HasValue)
        query = query.Where(x => x.Id != excludeId.Value);
    
    return !await query.AnyAsync();
}
```

### 3. 业务逻辑组合
```csharp
public async Task<Tenant> CreateAsync(Tenant tenant)
{
    // 业务规则验证
    if (await ExistsByCodeAsync(tenant.Code))
        throw new BusinessException("租户编码已存在");

    // 设置基础信息
    tenant.Id = SnowflakeHelper.NextId();
    tenant.CreateTime = DateTime.Now;

    // 保存租户
    return await _tenantRepository.CreateAsync(tenant);
}

public async Task CreateDefaultRoleAsync(int tenantId)
{
    // 创建默认角色
    var defaultRole = new Role
    {
        TenantId = tenantId,
        Name = "默认角色",
        CreateTime = DateTime.Now
    };
    await _roleRepository.CreateAsync(defaultRole);
}
```

### 4. 分页查询
```csharp
public async Task<ListRes<Tenant>> GetPageListAsync(PageListReq<TenantPageListReq> req)
{
    var query = _db.Queryable<Tenant>();
    
    // 动态条件构建
    if (!string.IsNullOrEmpty(req.Data?.Name))
        query = query.Where(x => x.Name.Contains(req.Data.Name));
    
    if (!string.IsNullOrEmpty(req.Data?.Code))
        query = query.Where(x => x.Code.Contains(req.Data.Code));
    
    // 分页查询
    var totalCount = await query.CountAsync();
    var items = await query
        .OrderByDescending(x => x.UpdateTime)
        .Skip((req.PageIndex - 1) * req.PageSize)
        .Take(req.PageSize)
        .ToListAsync();
    
    return new ListRes<Tenant>
    {
        Items = items,
        TotalCount = totalCount
    };
}
```

## 📦 依赖关系

### 项目引用
- **CYSF.Core**: 核心基础设施
- **CYSF.Repositories**: 数据访问层

### 自动注册
服务类通过Autofac自动注册，无需手动配置依赖注入。

## 🛠️ 开发指南

### 添加新的业务服务

1. **创建服务类**
```csharp
public class NewService
{
    private readonly NewRepository _newRepository;
    private readonly ISqlSugarClient _db;

    public NewService(NewRepository newRepository, ISqlSugarClient db)
    {
        _newRepository = newRepository;
        _db = db;
    }

    public async Task<New> GetByIdAsync(int id)
    {
        return await _newRepository.GetByIdAsync(id);
    }

    public async Task<New> CreateAsync(New entity)
    {
        return await _newRepository.CreateAsync(entity);
    }
}
```

2. **实现业务验证**
```csharp
public async Task<bool> ValidateBusinessRuleAsync(New entity)
{
    // 实现具体的业务规则验证
    if (string.IsNullOrEmpty(entity.Name))
        return false;
    
    // 检查唯一性
    var exists = await _db.Queryable<New>()
        .Where(x => x.Name == entity.Name && x.Id != entity.Id)
        .AnyAsync();
    
    return !exists;
}
```

3. **实现复杂业务逻辑**
```csharp
public async Task<New> CreateWithRelatedDataAsync(New entity)
{
    return await _db.Ado.UseTranAsync(async () =>
    {
        // 1. 验证业务规则
        if (!await ValidateBusinessRuleAsync(entity))
            throw new BusinessException("业务规则验证失败");
        
        // 2. 创建主实体
        var created = await CreateAsync(entity);
        
        // 3. 创建关联数据
        await CreateRelatedDataAsync(created.Id);
        
        return created;
    });
}
```

### 使用代码生成器
可以使用项目的代码生成器自动生成服务类：

```bash
cd src/Core/Infrastructure/CYSF.Generate
dotnet run
```

生成器会根据数据库表结构自动创建：
- 服务类基础结构
- 标准CRUD方法
- 基础业务验证

## 🔍 最佳实践

### 1. 业务逻辑封装
- 将复杂的业务逻辑封装在服务层
- 避免在控制器中编写业务逻辑
- 保持服务方法的单一职责

### 2. 事务管理
- Service层负责复杂业务的事务管理
- 使用 `UseTranAsync` 处理多表操作
- 提供组合方法供App层调用
- 保持业务逻辑的完整性

### 3. 异常处理
- 使用业务异常表示业务规则违反
- 提供清晰的错误信息
- 记录关键操作的日志

### 4. 性能优化
- 合理使用缓存
- 优化数据库查询
- 避免N+1查询问题

## 🧪 测试

### 单元测试
```csharp
[Test]
public async Task CreateAsync_ValidTenant_ReturnsCreatedTenant()
{
    // Arrange
    var tenant = new Tenant { Name = "Test Tenant", Code = "TEST" };
    
    // Act
    var result = await _tenantService.CreateAsync(tenant);
    
    // Assert
    Assert.IsNotNull(result);
    Assert.Greater(result.Id, 0);
    Assert.AreEqual("Test Tenant", result.Name);
}

[Test]
public async Task CheckCodeAvailableAsync_ExistingCode_ReturnsFalse()
{
    // Arrange
    var existingCode = "EXISTING";
    
    // Act
    var result = await _tenantService.CheckCodeAvailableAsync(existingCode);
    
    // Assert
    Assert.IsFalse(result);
}
```

### 集成测试
```csharp
[Test]
public async Task CreateWithInitAsync_ValidTenant_CreatesWithRelatedData()
{
    // Arrange
    var tenant = new Tenant { Name = "Test", Code = "TEST" };
    
    // Act
    var result = await _tenantService.CreateWithInitAsync(tenant);
    
    // Assert
    Assert.IsNotNull(result);
    
    // 验证关联数据是否创建
    var roles = await _roleService.GetByTenantIdAsync(result.Id);
    Assert.IsNotEmpty(roles);
}
```

## 📚 相关文档

- [CYSF.Application - 应用服务层](../Application/README.md)
- [CYSF.Repositories - 数据访问层](../Repository/README.md)
- [CYSF.Core - 核心基础设施](../Infrastructure/README.md)

---

📚 更多信息请参考 [主项目文档](../../../README.md)
