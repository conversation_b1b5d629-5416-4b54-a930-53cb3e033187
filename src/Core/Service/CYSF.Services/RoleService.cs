using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using CYSF.Models.Entities;
using CYSF.Models.Request;
using CYSF.Models.Response;
using CYSF.Repositories.Implement;
using SqlSugar;

namespace CYSF.Services
{
    public class RoleService(BaseRepository<Role> roleRepo)
    {
        private readonly BaseRepository<Role> _roleRepo = roleRepo;

        public async Task<Role> GetByIdAsync(int id)
        {
            return await _roleRepo.GetFirstAsync(r => r.Id == id);
        }

        public async Task<List<Role>> GetListAsync()
        {
            return await _roleRepo.GetListAsync();
        }

        /// <summary>
        /// 根据租户ID获取角色列表
        /// </summary>
        /// <param name="tenantId">租户ID</param>
        /// <returns>角色列表</returns>
        public async Task<List<Role>> GetByTenantIdAsync(int tenantId)
        {
            return await _roleRepo.GetListAsync(r => r.TenantId == tenantId);
        }

        /// <summary>
        /// 检查角色名称是否存在
        /// </summary>
        /// <param name="name">角色名称</param>
        /// <param name="excludeId">排除的角色ID</param>
        /// <returns>是否存在</returns>
        public async Task<bool> ExistsByNameAsync(string name, int? excludeId = null)
        {
            var query = _roleRepo.AsQueryable().Where(r => r.Name == name);

            if (excludeId.HasValue)
            {
                query = query.Where(r => r.Id != excludeId.Value);
            }

            return await query.AnyAsync();
        }

        /// <summary>
        /// 检查角色名称是否存在（在指定租户范围内）
        /// </summary>
        /// <param name="name">角色名称</param>
        /// <param name="tenantId">租户ID</param>
        /// <param name="excludeId">排除的角色ID</param>
        /// <returns>是否存在</returns>
        public async Task<bool> ExistsByNameAsync(string name, int tenantId, int? excludeId = null)
        {
            var query = _roleRepo.AsQueryable().Where(r => r.Name == name && r.TenantId == tenantId);

            if (excludeId.HasValue)
            {
                query = query.Where(r => r.Id != excludeId.Value);
            }

            return await query.AnyAsync();
        }

        /// <summary>
        /// 创建角色
        /// </summary>
        /// <param name="role">角色实体</param>
        /// <returns>创建的角色</returns>
        public async Task<Role> CreateAsync(Role role)
        {
            return await _roleRepo.InsertReturnEntityAsync(role);
        }

        /// <summary>
        /// 获取分页列表
        /// </summary>
        /// <param name="req">查询请求</param>
        /// <returns>分页结果</returns>
        public async Task<ListRes<Role>> GetPageListAsync<T>(PageListReq<T> req) where T : class
        {
            return await _roleRepo.GetPageListAsync(req.Pagination, req.Sorts, null);
        }



        /// <summary>
        /// 异步删除角色
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <returns>是否删除成功</returns>
        public async Task<bool> DeleteAsync(int roleId)
        {
            return await _roleRepo.DeleteAsync(role => role.Id == roleId);
        }

        /// <summary>
        /// 添加角色
        /// </summary>
        /// <param name="role">角色实体</param>
        /// <returns>是否添加成功</returns>
        public async Task<bool> AddAsync(Role role)
        {
            return await _roleRepo.AddAsync(role);
        }

        /// <summary>
        /// 更新角色
        /// </summary>
        /// <param name="role">角色实体</param>
        /// <returns>是否更新成功</returns>
        public async Task<bool> UpdateAsync(Role role)
        {
            return await _roleRepo.UpdateAsync(role);
        }

        /// <summary>
        /// 根据条件获取角色列表
        /// </summary>
        /// <param name="where">查询条件</param>
        /// <returns>角色列表</returns>
        public async Task<List<Role>> GetListAsync(Expression<Func<Role, bool>> where)
        {
            return await _roleRepo.GetListAsync(where);
        }

        /// <summary>
        /// 根据条件统计角色数量
        /// </summary>
        /// <param name="where">查询条件</param>
        /// <returns>角色数量</returns>
        public async Task<int> CountAsync(Expression<Func<Role, bool>> where = null)
        {
            return await _roleRepo.CountAsync(where);
        }

        /// <summary>
        /// 删除角色及其关联用户 - Service层处理完整业务逻辑
        /// 推荐方案：符合分层架构原则
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <param name="uow">工作单元</param>
        /// <returns>删除结果</returns>
        public async Task<DeleteResult> DeleteRoleWithUsersAsync(int roleId, ISugarUnitOfWork uow)
        {
            // 1. 检查角色是否存在
            var roleExists = await uow.Db.Queryable<Role>()
                .AnyAsync(r => r.Id == roleId);

            if (!roleExists)
            {
                return new DeleteResult { Success = false, Message = "角色不存在" };
            }

            // 2. 统计关联用户数量
            var affectedUserCount = await uow.Db.Queryable<TenantUser>()
                .CountAsync(u => u.RoleId == roleId);

            // 3. 更新关联用户的RoleId为0
            if (affectedUserCount > 0)
            {
                var updateCount = await uow.Db.Updateable<TenantUser>()
                    .SetColumns(u => new TenantUser { RoleId = 0 })
                    .Where(u => u.RoleId == roleId)
                    .ExecuteCommandAsync();

                if (updateCount != affectedUserCount)
                {
                    return new DeleteResult 
                    { 
                        Success = false, 
                        Message = $"更新用户角色失败，预期更新 {affectedUserCount} 个，实际更新 {updateCount} 个" 
                    };
                }
            }

            // 4. 删除角色记录
            var deleteCount = await uow.Db.Deleteable<Role>()
                .Where(r => r.Id == roleId)
                .ExecuteCommandAsync();

            if (deleteCount > 0)
            {
                return new DeleteResult 
                { 
                    Success = true, 
                    Message = "删除成功", 
                    AffectedUserCount = affectedUserCount 
                };
            }
            else
            {
                return new DeleteResult { Success = false, Message = "删除角色失败" };
            }
        }

        /// <summary>
        /// 使用指定的工作单元删除角色
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <param name="uow">工作单元</param>
        /// <returns>是否删除成功</returns>
        public async Task<bool> DeleteWithUowAsync(int roleId, ISugarUnitOfWork uow)
        {
            var deleteCount = await uow.Db.Deleteable<Role>()
                .Where(r => r.Id == roleId)
                .ExecuteCommandAsync();

            return deleteCount > 0;
        }

        /// <summary>
        /// 批量删除角色
        /// </summary>
        /// <param name="roleIds">角色ID列表</param>
        /// <param name="uow">工作单元</param>
        /// <returns>批量删除结果</returns>
        public async Task<DeleteResult> DeleteRolesBatchAsync(int[] roleIds, ISugarUnitOfWork uow)
        {
            var successCount = 0;
            var totalAffectedUsers = 0;

            foreach (var roleId in roleIds)
            {
                // 检查角色是否存在
                var roleExists = await uow.Db.Queryable<Role>()
                    .AnyAsync(r => r.Id == roleId);

                if (!roleExists)
                {
                    continue; // 跳过不存在的角色
                }

                // 统计并更新关联用户
                var userCount = await uow.Db.Queryable<TenantUser>()
                    .CountAsync(u => u.RoleId == roleId);

                if (userCount > 0)
                {
                    await uow.Db.Updateable<TenantUser>()
                        .SetColumns(u => new TenantUser { RoleId = 0 })
                        .Where(u => u.RoleId == roleId)
                        .ExecuteCommandAsync();

                    totalAffectedUsers += userCount;
                }

                // 删除角色
                var deleteCount = await uow.Db.Deleteable<Role>()
                    .Where(r => r.Id == roleId)
                    .ExecuteCommandAsync();

                if (deleteCount > 0)
                {
                    successCount++;
                }
            }

            return new DeleteResult
            {
                Success = true,
                Message = $"批量删除完成，成功删除 {successCount}/{roleIds.Length} 个角色",
                SuccessCount = successCount,
                AffectedUserCount = totalAffectedUsers
            };
        }
    }

    /// <summary>
    /// 删除操作结果
    /// </summary>
    public class DeleteResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int AffectedUserCount { get; set; }
        public int SuccessCount { get; set; }
    }
}
