using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using CYSF.Models.Entities;
using CYSF.Repositories.Implement;

namespace CYSF.Services
{
    /// <summary>
    /// DicType业务服务
    /// 字典类型表
    /// </summary>
    public class DicTypeService
    {
        private readonly BaseRepository<DicType> _dicTypeRepository;
        private readonly ILogger<DicTypeService> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="dicTypeRepository">DicType仓储</param>
        /// <param name="logger">日志记录器</param>
        public DicTypeService(
            BaseRepository<DicType> dicTypeRepository,
            ILogger<DicTypeService> logger)
        {
            _dicTypeRepository = dicTypeRepository;
            _logger = logger;
        }
                
        /// <summary>
        /// 获取所有DicType列表
        /// 字典类型表数据查询
        /// </summary>
        /// <returns>DicType列表</returns>
        public async Task<List<DicType>> GetListAsync()
        {
            return await _dicTypeRepository.GetListAsync();
        }

        /// <summary>
        /// 根据条件获取DicType列表
        /// </summary>
        /// <param name="where">查询条件</param>
        /// <returns>DicType列表</returns>
        public async Task<List<DicType>> GetListAsync(Expression<Func<DicType, bool>> where)
        {
            return await _dicTypeRepository.GetListAsync(where);
        }

        /// <summary>
        /// 根据ID获取DicType
        /// </summary>
        /// <param name="id">主键ID</param>
        /// <returns>DicType实体</returns>
        public async Task<DicType> GetByIdAsync(int id)
        {
            return await _dicTypeRepository.GetByIdAsync(id);
        }

        /// <summary>
        /// 创建DicType
        /// 新增字典类型表记录
        /// </summary>
        /// <param name="model">DicType实体</param>
        /// <returns>创建的DicType</returns>
        public async Task<DicType> CreateAsync(DicType model)
        {
            return await _dicTypeRepository.AddReturnEntityAsync(model);
        }

        /// <summary>
        /// 更新DicType
        /// 修改字典类型表记录
        /// </summary>
        /// <param name="model">DicType实体</param>
        /// <returns>更新结果</returns>
        public async Task<bool> UpdateAsync(DicType model)
        {
            return await _dicTypeRepository.UpdateAsync(model);
        }

        /// <summary>
        /// 删除DicType
        /// 删除字典类型表记录
        /// </summary>
        /// <param name="id">主键ID</param>
        /// <returns>删除结果</returns>
        public async Task<bool> DeleteAsync(int id)
        {
            return await _dicTypeRepository.DeleteAsync(r => r.Id == id);
        }

        /// <summary>
        /// 检查DicType是否存在
        /// </summary>
        /// <param name="id">主键ID</param>
        /// <returns>是否存在</returns>
        public async Task<bool> ExistsAsync(int id)
        {
            return await _dicTypeRepository.ExistAsync(r => r.Id == id);
        }
    }
}