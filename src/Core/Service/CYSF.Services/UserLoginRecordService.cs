using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CYSF.Models.Entities;
using CYSF.Models.Request;
using CYSF.Models.Request.UserLoginRecord;
using CYSF.Models.Response;
using CYSF.Repositories.Base;
using SqlSugar;

namespace CYSF.Services
{
    /// <summary>
    /// UserLoginRecord服务
    /// </summary>
    public class UserLoginRecordService
    {
        private readonly BaseRepository<UserLoginRecord> _userLoginRecordRepo;
        private readonly UserLoginRecordRepository _userLoginRecordRepository;

        public UserLoginRecordService(BaseRepository<UserLoginRecord> userLoginRecordRepo, UserLoginRecordRepository userLoginRecordRepository)
        {
            _userLoginRecordRepo = userLoginRecordRepo;
            _userLoginRecordRepository = userLoginRecordRepository;
        }

        /// <summary>
        /// 根据ID获取UserLoginRecord
        /// </summary>
        /// <param name="id">UserLoginRecordID</param>
        /// <returns>UserLoginRecord实体</returns>
        public async Task<UserLoginRecord> GetByIdAsync(int id)
        {
            return await _userLoginRecordRepo.GetFirstAsync(r => r.Id == id);
        }

        /// <summary>
        /// 获取UserLoginRecord分页列表
        /// </summary>
        /// <param name="req">分页查询请求</param>
        /// <returns>UserLoginRecord分页列表</returns>
        public async Task<ListRes<UserLoginRecordDto>> GetPageListAsync(PageListReq<UserLoginRecordPageListReq> req)
        {
            return await _userLoginRecordRepository.GetPageListAsync(req);
        }

        /// <summary>
        /// 获取UserLoginRecord分页列表（返回Entity）
        /// </summary>
        /// <param name="req">分页查询请求</param>
        /// <returns>UserLoginRecord分页列表</returns>
        public async Task<ListRes<UserLoginRecord>> GetPageListAsync<T>(PageListReq<T> req) where T : class
        {
            return await _userLoginRecordRepo.GetPageListAsync(req.Pagination, req.Sorts, null);
        }

        /// <summary>
        /// 创建UserLoginRecord
        /// </summary>
        /// <param name="userLoginRecord">UserLoginRecord实体</param>
        /// <returns>创建的UserLoginRecord</returns>
        public async Task<UserLoginRecord> CreateAsync(UserLoginRecord userLoginRecord)
        {
            return await _userLoginRecordRepo.InsertReturnEntityAsync(userLoginRecord);
        }

        /// <summary>
        /// 更新UserLoginRecord
        /// </summary>
        /// <param name="userLoginRecord">UserLoginRecord实体</param>
        /// <returns>更新结果</returns>
        public async Task<bool> UpdateAsync(UserLoginRecord userLoginRecord)
        {
            return await _userLoginRecordRepo.UpdateAsync(userLoginRecord);
        }

        /// <summary>
        /// 删除UserLoginRecord
        /// </summary>
        /// <param name="id">UserLoginRecordID</param>
        /// <returns>删除结果</returns>
        public async Task<bool> DeleteAsync(int id)
        {
            return await _userLoginRecordRepo.DeleteAsync(userLoginRecord => userLoginRecord.Id == id);
        }



        /// <summary>
        /// 获取UserLoginRecord总数
        /// </summary>
        /// <returns>UserLoginRecord总数</returns>
        public async Task<int> GetCountAsync()
        {
            return await _userLoginRecordRepo.CountAsync();
        }

        /// <summary>
        /// 获取所有UserLoginRecord列表
        /// </summary>
        /// <returns>UserLoginRecord列表</returns>
        public async Task<List<UserLoginRecord>> GetAllAsync()
        {
            return await _userLoginRecordRepo.GetListAsync();
        }
    }
}
