using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using CYSF.Core;
using CYSF.Models.Entities;
using CYSF.Models.Enum;
using CYSF.Models.Request;
using CYSF.Models.Response;
using CYSF.Repositories.Implement;
using SqlSugar;


namespace CYSF.Services
{
    public class UserLoginRecordService
    {
        private readonly BaseRepository<UserLoginRecord> _userLoginRepo;

        public UserLoginRecordService(BaseRepository<UserLoginRecord> userLoginRepo)
        {
            _userLoginRepo = userLoginRepo;
        }

        /// <summary>
        /// 根据ID获取登录记录
        /// </summary>
        /// <param name="id">记录ID</param>
        /// <returns>登录记录</returns>
        public async Task<UserLoginRecord> GetByIdAsync(int id)
        {
            return await _userLoginRepo.GetFirstAsync(r => r.Id == id);
        }

        /// <summary>
        /// 获取分页列表
        /// </summary>
        /// <param name="req">查询请求</param>
        /// <returns>分页结果</returns>
        public async Task<ListRes<UserLoginRecord>> GetPageListAsync<T>(PageListReq<T> req) where T : class
        {
            return await _userLoginRepo.GetPageListAsync(req.Pagination, req.Sorts, null);
        }

        /// <summary>
        /// 创建登录记录
        /// </summary>
        /// <param name="record">登录记录</param>
        /// <returns>创建的记录</returns>
        public async Task<UserLoginRecord> CreateAsync(UserLoginRecord record)
        {
            return await _userLoginRepo.InsertReturnEntityAsync(record);
        }

        /// <summary>
        /// 获取用户最近的登录记录
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="count">获取数量</param>
        /// <returns>登录记录列表</returns>
        public async Task<List<UserLoginRecord>> GetRecentLoginsByUserIdAsync(int userId, int count = 10)
        {
            return await _userLoginRepo.AsQueryable()
                .Where(r => r.UserId == userId)
                .OrderByDescending(r => r.RecordTime)
                .Take(count)
                .ToListAsync();
        }

        /// <summary>
        /// 清除指定天数前的登录记录
        /// </summary>
        /// <param name="days">保留天数</param>
        /// <returns>清除的记录数</returns>
        public async Task<int> ClearOldRecordsAsync(int days)
        {
            var cutoffDate = DateTime.Now.AddDays(-days);
            return await _userLoginRepo.AsDeleteable()
                .Where(r => r.RecordTime <= cutoffDate)
                .ExecuteCommandAsync();
        }

        /// <summary>
        /// 清除用户登录记录（兼容原有方法）
        /// </summary>
        /// <param name="prevDays">保留天数</param>
        public async Task ClearUserLoginRecordAsync(int prevDays)
        {
            await ClearOldRecordsAsync(prevDays);
        }
    }
}