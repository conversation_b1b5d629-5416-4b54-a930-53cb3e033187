using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using CYSF.Models.Entities;
using CYSF.Repositories.Implement;

namespace CYSF.Services
{
    /// <summary>
    /// Dic业务服务
    /// 字典表
    /// </summary>
    public class DicService
    {
        private readonly BaseRepository<Dic> _dicRepository;
        private readonly ILogger<DicService> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="dicRepository">Dic仓储</param>
        /// <param name="logger">日志记录器</param>
        public DicService(
            BaseRepository<Dic> dicRepository,
            ILogger<DicService> logger)
        {
            _dicRepository = dicRepository;
            _logger = logger;
        }
                
        /// <summary>
        /// 获取所有Dic列表
        /// 字典表数据查询
        /// </summary>
        /// <returns>Dic列表</returns>
        public async Task<List<Dic>> GetListAsync()
        {
            return await _dicRepository.GetListAsync();
        }

        /// <summary>
        /// 根据条件获取Dic列表
        /// </summary>
        /// <param name="where">查询条件</param>
        /// <returns>Dic列表</returns>
        public async Task<List<Dic>> GetListAsync(Expression<Func<Dic, bool>> where)
        {
            return await _dicRepository.GetListAsync(where);
        }

        /// <summary>
        /// 根据ID获取Dic
        /// </summary>
        /// <param name="id">主键ID</param>
        /// <returns>Dic实体</returns>
        public async Task<Dic> GetByIdAsync(int id)
        {
            return await _dicRepository.GetByIdAsync(id);
        }

        /// <summary>
        /// 创建Dic
        /// 新增字典表记录
        /// </summary>
        /// <param name="model">Dic实体</param>
        /// <returns>创建的Dic</returns>
        public async Task<Dic> CreateAsync(Dic model)
        {
            return await _dicRepository.AddReturnEntityAsync(model);
        }

        /// <summary>
        /// 更新Dic
        /// 修改字典表记录
        /// </summary>
        /// <param name="model">Dic实体</param>
        /// <returns>更新结果</returns>
        public async Task<bool> UpdateAsync(Dic model)
        {
            return await _dicRepository.UpdateAsync(model);
        }

        /// <summary>
        /// 删除Dic
        /// 删除字典表记录
        /// </summary>
        /// <param name="id">主键ID</param>
        /// <returns>删除结果</returns>
        public async Task<bool> DeleteAsync(int id)
        {
            return await _dicRepository.DeleteAsync(r => r.Id == id);
        }

        /// <summary>
        /// 检查Dic是否存在
        /// </summary>
        /// <param name="id">主键ID</param>
        /// <returns>是否存在</returns>
        public async Task<bool> ExistsAsync(int id)
        {
            return await _dicRepository.ExistAsync(r => r.Id == id);
        }
    }
}