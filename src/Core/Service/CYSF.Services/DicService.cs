using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CYSF.Models.Entities;
using CYSF.Models.Request;
using CYSF.Models.Request.Dic;
using CYSF.Models.Response;
using CYSF.Repositories.Base;
using SqlSugar;

namespace CYSF.Services
{
    /// <summary>
    /// Dic服务
    /// </summary>
    public class DicService
    {
        private readonly BaseRepository<Dic> _dicRepo;
        private readonly DicRepository _dicRepository;

        public DicService(BaseRepository<Dic> dicRepo, DicRepository dicRepository)
        {
            _dicRepo = dicRepo;
            _dicRepository = dicRepository;
        }

        /// <summary>
        /// 根据ID获取Dic
        /// </summary>
        /// <param name="id">DicID</param>
        /// <returns>Dic实体</returns>
        public async Task<Dic> GetByIdAsync(int id)
        {
            return await _dicRepo.GetFirstAsync(r => r.Id == id);
        }

        /// <summary>
        /// 获取Dic分页列表
        /// </summary>
        /// <param name="req">分页查询请求</param>
        /// <returns>Dic分页列表</returns>
        public async Task<ListRes<DicDto>> GetPageListAsync(PageListReq<DicPageListReq> req)
        {
            return await _dicRepository.GetPageListAsync(req);
        }

        /// <summary>
        /// 获取Dic分页列表（返回Entity）
        /// </summary>
        /// <param name="req">分页查询请求</param>
        /// <returns>Dic分页列表</returns>
        public async Task<ListRes<Dic>> GetPageListAsync<T>(PageListReq<T> req) where T : class
        {
            return await _dicRepo.GetPageListAsync(req.Pagination, req.Sorts, null);
        }

        /// <summary>
        /// 创建Dic
        /// </summary>
        /// <param name="dic">Dic实体</param>
        /// <returns>创建的Dic</returns>
        public async Task<Dic> CreateAsync(Dic dic)
        {
            return await _dicRepo.InsertReturnEntityAsync(dic);
        }

        /// <summary>
        /// 更新Dic
        /// </summary>
        /// <param name="dic">Dic实体</param>
        /// <returns>更新结果</returns>
        public async Task<bool> UpdateAsync(Dic dic)
        {
            return await _dicRepo.UpdateAsync(dic);
        }

        /// <summary>
        /// 删除Dic
        /// </summary>
        /// <param name="id">DicID</param>
        /// <returns>删除结果</returns>
        public async Task<bool> DeleteAsync(int id)
        {
            return await _dicRepo.DeleteAsync(dic => dic.Id == id);
        }



        /// <summary>
        /// 获取Dic总数
        /// </summary>
        /// <returns>Dic总数</returns>
        public async Task<int> GetCountAsync()
        {
            return await _dicRepo.CountAsync();
        }

        /// <summary>
        /// 获取所有Dic列表
        /// </summary>
        /// <returns>Dic列表</returns>
        public async Task<List<Dic>> GetAllAsync()
        {
            return await _dicRepo.GetListAsync();
        }
    }
}
