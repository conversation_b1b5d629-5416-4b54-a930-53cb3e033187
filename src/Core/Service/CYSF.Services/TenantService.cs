using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CYSF.Models.Entities;
using CYSF.Models.Request;
using CYSF.Models.Request.Tenant;
using CYSF.Models.Response;
using CYSF.Repositories.Base;
using SqlSugar;

namespace CYSF.Services
{
    /// <summary>
    /// Tenant服务
    /// </summary>
    public class TenantService
    {
        private readonly BaseRepository<Tenant> _tenantRepo;
        private readonly TenantRepository _tenantRepository;

        public TenantService(BaseRepository<Tenant> tenantRepo, TenantRepository tenantRepository)
        {
            _tenantRepo = tenantRepo;
            _tenantRepository = tenantRepository;
        }

        /// <summary>
        /// 根据ID获取Tenant
        /// </summary>
        /// <param name="id">TenantID</param>
        /// <returns>Tenant实体</returns>
        public async Task<Tenant> GetByIdAsync(int id)
        {
            return await _tenantRepo.GetFirstAsync(r => r.Id == id);
        }

        /// <summary>
        /// 获取Tenant分页列表
        /// </summary>
        /// <param name="req">分页查询请求</param>
        /// <returns>Tenant分页列表</returns>
        public async Task<ListRes<TenantDto>> GetPageListAsync(PageListReq<TenantPageListReq> req)
        {
            return await _tenantRepository.GetPageListAsync(req);
        }

        /// <summary>
        /// 获取Tenant分页列表（返回Entity）
        /// </summary>
        /// <param name="req">分页查询请求</param>
        /// <returns>Tenant分页列表</returns>
        public async Task<ListRes<Tenant>> GetPageListAsync<T>(PageListReq<T> req) where T : class
        {
            return await _tenantRepo.GetPageListAsync(req.Pagination, req.Sorts, null);
        }

        /// <summary>
        /// 创建Tenant
        /// </summary>
        /// <param name="tenant">Tenant实体</param>
        /// <returns>创建的Tenant</returns>
        public async Task<Tenant> CreateAsync(Tenant tenant)
        {
            return await _tenantRepo.InsertReturnEntityAsync(tenant);
        }

        /// <summary>
        /// 更新Tenant
        /// </summary>
        /// <param name="tenant">Tenant实体</param>
        /// <returns>更新结果</returns>
        public async Task<bool> UpdateAsync(Tenant tenant)
        {
            return await _tenantRepo.UpdateAsync(tenant);
        }

        /// <summary>
        /// 删除Tenant
        /// </summary>
        /// <param name="id">TenantID</param>
        /// <returns>删除结果</returns>
        public async Task<bool> DeleteAsync(int id)
        {
            return await _tenantRepo.DeleteAsync(tenant => tenant.Id == id);
        }



        /// <summary>
        /// 获取Tenant总数
        /// </summary>
        /// <returns>Tenant总数</returns>
        public async Task<int> GetCountAsync()
        {
            return await _tenantRepo.CountAsync();
        }

        /// <summary>
        /// 获取所有Tenant列表
        /// </summary>
        /// <returns>Tenant列表</returns>
        public async Task<List<Tenant>> GetAllAsync()
        {
            return await _tenantRepo.GetListAsync();
        }
    }
}
