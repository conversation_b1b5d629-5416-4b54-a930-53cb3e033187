using System.Collections.Generic;
using System.Threading.Tasks;
using CYSF.Models.Entities;
using CYSF.Repositories.Implement;

namespace CYSF.Services;

public class TenantService
{
    private readonly BaseRepository<Tenant> _tenantRepo;

    public TenantService(BaseRepository<Tenant> tenantRepo)
    {
        _tenantRepo = tenantRepo;
    }
    
    public async Task<Tenant> GetByIdAsync(int id)
    {
        return await _tenantRepo.GetFirstAsync(r => r.Id == id);
    }

    public async Task<List<Tenant>> GetListAsync()
    {
        return await _tenantRepo.GetListAsync();
    }

    public async Task<Tenant> CreateAsync(Tenant tenant)
    {
        return await _tenantRepo.InsertReturnEntityAsync(tenant);
    }

}