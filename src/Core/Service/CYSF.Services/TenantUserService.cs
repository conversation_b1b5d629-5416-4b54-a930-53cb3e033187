using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CYSF.Models.Entities;
using CYSF.Models.Request;
using CYSF.Models.Request.TenantUser;
using CYSF.Models.Response;
using CYSF.Repositories;
using CYSF.Repositories.Base;
using SqlSugar;

namespace CYSF.Services
{
    public class TenantUserService
    {
        private readonly BaseRepository<TenantUser> _userRepo;
        private readonly TenantUserRepository _tenantUserRepo;

        public TenantUserService(BaseRepository<TenantUser> userRepo, TenantUserRepository tenantUserRepo)
        {
            _userRepo = userRepo;
            _tenantUserRepo = tenantUserRepo;
        }
        public async Task<TenantUser> GetByMobile(string mobile)
        {
            return await _userRepo.GetFirstAsync(r=>r.Mobile == mobile);
        }

        public async Task<TenantUser> GetByUserName(string userName)
        {
            return await _userRepo.GetFirstAsync(r=>r.UserName == userName);
        }

        public async Task<TenantUser> GetByUserId(int userId)
        {
            return await _userRepo.GetFirstAsync(r=>r.Id == userId);
        }

        /// <summary>
        /// 将指定角色ID的所有用户的RoleId更新为0
        /// 用于删除角色时清理关联数据
        /// </summary>
        /// <param name="roleId">要清理的角色ID</param>
        /// <returns>是否更新成功</returns>
        public async Task<bool> UpdateUserRoleToZero(int roleId)
        {
            // 使用UpdateColumns方法更新指定条件的记录
            return await _userRepo.UpdateColumnsAsync(
                setColumns: user => new TenantUser { RoleId = 0 },
                where: user => user.RoleId == roleId
            );
        }

        /// <summary>
        /// 根据角色ID获取用户数量
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <returns>用户数量</returns>
        public async Task<int> GetUserCountByRoleId(int roleId)
        {
            return await _userRepo.CountAsync(user => user.RoleId == roleId);
        }

        /// <summary>
        /// 根据角色ID获取用户列表
        /// </summary>
        /// <param name="roleId">角色ID</param>
        /// <returns>用户列表</returns>
        public async Task<List<TenantUser>> GetUsersByRoleId(int roleId)
        {
            return await _userRepo.GetListAsync(user => user.RoleId == roleId);
        }

        /// <summary>
        /// 批量更新用户角色
        /// </summary>
        /// <param name="userIds">用户ID列表</param>
        /// <param name="newRoleId">新的角色ID</param>
        /// <returns>是否更新成功</returns>
        public async Task<bool> UpdateUsersRole(List<int> userIds, int newRoleId)
        {
            return await _userRepo.UpdateColumnsAsync(
                setColumns: user => new TenantUser { RoleId = newRoleId },
                where: user => userIds.Contains(user.Id)
            );
        }



        /// <summary>
        /// 删除用户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>是否删除成功</returns>
        public async Task<bool> DeleteAsync(int userId)
        {
            return await _userRepo.DeleteAsync(user => user.Id == userId);
        }

        /// <summary>
        /// 使用指定的工作单元将角色ID更新为0
        /// 符合分层架构：Service层处理业务逻辑
        /// </summary>
        /// <param name="roleId">要清理的角色ID</param>
        /// <param name="uow">工作单元</param>
        /// <returns>是否更新成功</returns>
        public async Task<bool> UpdateUserRoleToZeroWithUow(int roleId, ISugarUnitOfWork uow)
        {
            var updateCount = await uow.Db.Updateable<TenantUser>()
                .SetColumns(u => new TenantUser { RoleId = 0 })
                .Where(u => u.RoleId == roleId)
                .ExecuteCommandAsync();

            return updateCount > 0;
        }

        /// <summary>
        /// 获取用户分页列表（包含关联的角色名称和租户名称）
        /// </summary>
        /// <param name="req">分页查询请求</param>
        /// <returns>用户分页列表</returns>
        public async Task<ListRes<TenantUserDto>> GetPageListAsync(PageListReq<TenantUserPageListReq> req)
        {
            return await _tenantUserRepo.GetPageListAsync(req);
        }

        /// <summary>
        /// 获取用户分页列表（返回Entity）
        /// </summary>
        /// <param name="req">分页查询请求</param>
        /// <returns>用户分页列表</returns>
        public async Task<ListRes<TenantUser>> GetPageListAsync<T>(PageListReq<T> req) where T : class
        {
            return await _userRepo.GetPageListAsync(req.Pagination, req.Sorts, null);
        }

        /// <summary>
        /// 根据ID获取用户
        /// </summary>
        /// <param name="id">用户ID</param>
        /// <returns>用户实体</returns>
        public async Task<TenantUser> GetByIdAsync(int id)
        {
            return await _userRepo.GetFirstAsync(r => r.Id == id);
        }

        /// <summary>
        /// 创建用户
        /// </summary>
        /// <param name="user">用户实体</param>
        /// <returns>创建的用户</returns>
        public async Task<TenantUser> CreateAsync(TenantUser user)
        {
            return await _userRepo.InsertReturnEntityAsync(user);
        }

        /// <summary>
        /// 更新用户
        /// </summary>
        /// <param name="user">用户实体</param>
        /// <returns>更新结果</returns>
        public async Task<bool> UpdateAsync(TenantUser user)
        {
            return await _userRepo.UpdateAsync(user);
        }

        /// <summary>
        /// 检查用户名是否存在
        /// </summary>
        /// <param name="userName">用户名</param>
        /// <param name="excludeId">排除的用户ID</param>
        /// <returns>是否存在</returns>
        public async Task<bool> ExistsByUserNameAsync(string userName, int? excludeId = null)
        {
            var query = _userRepo.AsQueryable().Where(t => t.UserName == userName);

            if (excludeId.HasValue)
            {
                query = query.Where(t => t.Id != excludeId.Value);
            }

            return await query.AnyAsync();
        }

        /// <summary>
        /// 检查用户名是否存在（在指定租户范围内）
        /// </summary>
        /// <param name="userName">用户名</param>
        /// <param name="tenantId">租户ID</param>
        /// <param name="excludeId">排除的用户ID</param>
        /// <returns>是否存在</returns>
        public async Task<bool> ExistsByUserNameAsync(string userName, int tenantId, int? excludeId = null)
        {
            var query = _userRepo.AsQueryable().Where(t => t.UserName == userName && t.TenantId == tenantId);

            if (excludeId.HasValue)
            {
                query = query.Where(t => t.Id != excludeId.Value);
            }

            return await query.AnyAsync();
        }

        /// <summary>
        /// 检查手机号是否存在
        /// </summary>
        /// <param name="mobile">手机号</param>
        /// <param name="excludeId">排除的用户ID</param>
        /// <returns>是否存在</returns>
        public async Task<bool> ExistsByMobileAsync(string mobile, int? excludeId = null)
        {
            var query = _userRepo.AsQueryable().Where(t => t.Mobile == mobile);

            if (excludeId.HasValue)
            {
                query = query.Where(t => t.Id != excludeId.Value);
            }

            return await query.AnyAsync();
        }

        /// <summary>
        /// 检查手机号是否存在（在指定租户范围内）
        /// </summary>
        /// <param name="mobile">手机号</param>
        /// <param name="tenantId">租户ID</param>
        /// <param name="excludeId">排除的用户ID</param>
        /// <returns>是否存在</returns>
        public async Task<bool> ExistsByMobileAsync(string mobile, int tenantId, int? excludeId = null)
        {
            var query = _userRepo.AsQueryable().Where(t => t.Mobile == mobile && t.TenantId == tenantId);

            if (excludeId.HasValue)
            {
                query = query.Where(t => t.Id != excludeId.Value);
            }

            return await query.AnyAsync();
        }

        /// <summary>
        /// 更新用户密码
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="hashedPassword">加密后的密码</param>
        /// <param name="passwordSalt">密码盐</param>
        /// <returns>更新结果</returns>
        public async Task<bool> UpdatePasswordAsync(int userId, string hashedPassword, string passwordSalt)
        {
            return await _userRepo.UpdateColumnsAsync(
                setColumns: user => new TenantUser
                {
                    Password = hashedPassword,
                    PasswordSalt = passwordSalt,
                    UpdateTime = DateTime.Now
                },
                where: user => user.Id == userId
            );
        }


    }
}
