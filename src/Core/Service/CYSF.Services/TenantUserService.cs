using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CYSF.Models.Entities;
using CYSF.Models.Request;
using CYSF.Models.Request.TenantUser;
using CYSF.Models.Response;
using CYSF.Repositories.Base;
using SqlSugar;

namespace CYSF.Services
{
    /// <summary>
    /// TenantUser服务
    /// </summary>
    public class TenantUserService
    {
        private readonly BaseRepository<TenantUser> _tenantUserRepo;
        private readonly TenantUserRepository _tenantUserRepository;

        public TenantUserService(BaseRepository<TenantUser> tenantUserRepo, TenantUserRepository tenantUserRepository)
        {
            _tenantUserRepo = tenantUserRepo;
            _tenantUserRepository = tenantUserRepository;
        }

        /// <summary>
        /// 根据ID获取TenantUser
        /// </summary>
        /// <param name="id">TenantUserID</param>
        /// <returns>TenantUser实体</returns>
        public async Task<TenantUser> GetByIdAsync(int id)
        {
            return await _tenantUserRepo.GetFirstAsync(r => r.Id == id);
        }

        /// <summary>
        /// 获取TenantUser分页列表
        /// </summary>
        /// <param name="req">分页查询请求</param>
        /// <returns>TenantUser分页列表</returns>
        public async Task<ListRes<TenantUserDto>> GetPageListAsync(PageListReq<TenantUserPageListReq> req)
        {
            return await _tenantUserRepository.GetPageListAsync(req);
        }

        /// <summary>
        /// 获取TenantUser分页列表（返回Entity）
        /// </summary>
        /// <param name="req">分页查询请求</param>
        /// <returns>TenantUser分页列表</returns>
        public async Task<ListRes<TenantUser>> GetPageListAsync<T>(PageListReq<T> req) where T : class
        {
            return await _tenantUserRepo.GetPageListAsync(req.Pagination, req.Sorts, null);
        }

        /// <summary>
        /// 创建TenantUser
        /// </summary>
        /// <param name="tenantUser">TenantUser实体</param>
        /// <returns>创建的TenantUser</returns>
        public async Task<TenantUser> CreateAsync(TenantUser tenantUser)
        {
            return await _tenantUserRepo.InsertReturnEntityAsync(tenantUser);
        }

        /// <summary>
        /// 更新TenantUser
        /// </summary>
        /// <param name="tenantUser">TenantUser实体</param>
        /// <returns>更新结果</returns>
        public async Task<bool> UpdateAsync(TenantUser tenantUser)
        {
            return await _tenantUserRepo.UpdateAsync(tenantUser);
        }

        /// <summary>
        /// 删除TenantUser
        /// </summary>
        /// <param name="id">TenantUserID</param>
        /// <returns>删除结果</returns>
        public async Task<bool> DeleteAsync(int id)
        {
            return await _tenantUserRepo.DeleteAsync(tenantUser => tenantUser.Id == id);
        }

        /// <summary>
        /// 批量删除TenantUser
        /// </summary>
        /// <param name="ids">TenantUserID列表</param>
        /// <returns>删除结果</returns>
        public async Task<bool> DeleteBatchAsync(List<int> ids)
        {
            return await _tenantUserRepo.DeleteAsync(tenantUser => ids.Contains(tenantUser.Id));
        }

        /// <summary>
        /// 检查TenantUser是否存在
        /// </summary>
        /// <param name="id">TenantUserID</param>
        /// <returns>是否存在</returns>
        public async Task<bool> ExistsAsync(int id)
        {
            return await _tenantUserRepo.IsAnyAsync(tenantUser => tenantUser.Id == id);
        }

        /// <summary>
        /// 获取TenantUser总数
        /// </summary>
        /// <returns>TenantUser总数</returns>
        public async Task<int> GetCountAsync()
        {
            return await _tenantUserRepo.CountAsync();
        }

        /// <summary>
        /// 获取所有TenantUser列表
        /// </summary>
        /// <returns>TenantUser列表</returns>
        public async Task<List<TenantUser>> GetAllAsync()
        {
            return await _tenantUserRepo.GetListAsync();
        }
    }
}
