using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CYSF.Models.Entities;
using CYSF.Models.Request;
using CYSF.Models.Request.TenantRole;
using CYSF.Models.Response;
using CYSF.Repositories.Base;
using SqlSugar;

namespace CYSF.Services
{
    /// <summary>
    /// TenantRole服务
    /// </summary>
    public class TenantRoleService
    {
        private readonly BaseRepository<TenantRole> _tenantRoleRepo;
        private readonly TenantRoleRepository _tenantRoleRepository;

        public TenantRoleService(BaseRepository<TenantRole> tenantRoleRepo, TenantRoleRepository tenantRoleRepository)
        {
            _tenantRoleRepo = tenantRoleRepo;
            _tenantRoleRepository = tenantRoleRepository;
        }

        /// <summary>
        /// 根据ID获取TenantRole
        /// </summary>
        /// <param name="id">TenantRoleID</param>
        /// <returns>TenantRole实体</returns>
        public async Task<TenantRole> GetByIdAsync(int id)
        {
            return await _tenantRoleRepo.GetFirstAsync(r => r.Id == id);
        }

        /// <summary>
        /// 获取TenantRole分页列表
        /// </summary>
        /// <param name="req">分页查询请求</param>
        /// <returns>TenantRole分页列表</returns>
        public async Task<ListRes<TenantRoleDto>> GetPageListAsync(PageListReq<TenantRolePageListReq> req)
        {
            return await _tenantRoleRepository.GetPageListAsync(req);
        }

        /// <summary>
        /// 获取TenantRole分页列表（返回Entity）
        /// </summary>
        /// <param name="req">分页查询请求</param>
        /// <returns>TenantRole分页列表</returns>
        public async Task<ListRes<TenantRole>> GetPageListAsync<T>(PageListReq<T> req) where T : class
        {
            return await _tenantRoleRepo.GetPageListAsync(req.Pagination, req.Sorts, null);
        }

        /// <summary>
        /// 创建TenantRole
        /// </summary>
        /// <param name="tenantRole">TenantRole实体</param>
        /// <returns>创建的TenantRole</returns>
        public async Task<TenantRole> CreateAsync(TenantRole tenantRole)
        {
            return await _tenantRoleRepo.InsertReturnEntityAsync(tenantRole);
        }

        /// <summary>
        /// 更新TenantRole
        /// </summary>
        /// <param name="tenantRole">TenantRole实体</param>
        /// <returns>更新结果</returns>
        public async Task<bool> UpdateAsync(TenantRole tenantRole)
        {
            return await _tenantRoleRepo.UpdateAsync(tenantRole);
        }

        /// <summary>
        /// 删除TenantRole
        /// </summary>
        /// <param name="id">TenantRoleID</param>
        /// <returns>删除结果</returns>
        public async Task<bool> DeleteAsync(int id)
        {
            return await _tenantRoleRepo.DeleteAsync(tenantRole => tenantRole.Id == id);
        }



        /// <summary>
        /// 获取TenantRole总数
        /// </summary>
        /// <returns>TenantRole总数</returns>
        public async Task<int> GetCountAsync()
        {
            return await _tenantRoleRepo.CountAsync();
        }

        /// <summary>
        /// 获取所有TenantRole列表
        /// </summary>
        /// <returns>TenantRole列表</returns>
        public async Task<List<TenantRole>> GetAllAsync()
        {
            return await _tenantRoleRepo.GetListAsync();
        }
    }
}
