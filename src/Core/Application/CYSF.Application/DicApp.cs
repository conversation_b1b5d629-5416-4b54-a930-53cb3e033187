using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Mapster;
using CYSF.Services;
using CYSF.Models.Request.Dic;
using CYSF.Models.Response;
using CYSF.Core.Cache;
using CYSF.Models.Entities;
using CYSF.Core.Exceptions;
using CYSF.Core.Models;
using CYSF.Models.Const;
using CYSF.Models.Request;
using CYSF.Core.Helpers;
using CYSF.Core.Extensions;
using CYSF.Core.HttpContextUser;

namespace CYSF.Application;

/// <summary>
/// Dic应用服务
/// </summary>
public class DicApp
{
    private readonly DicService _dicService;
    private readonly ILogger<DicApp> _logger;
    private readonly IContextUser _contextUser;
    private readonly CacheHelper _cacheHelper;

    public DicApp(
        DicService dicService,
        ILogger<DicApp> logger,
        IContextUser contextUser,
        CacheHelper cacheHelper)
    {
        _dicService = dicService;
        _logger = logger;
        _contextUser = contextUser;
        _cacheHelper = cacheHelper;
    }

    /// <summary>
    /// 根据ID获取Dic
    /// </summary>
    /// <param name="id">DicID</param>
    /// <returns>Dic实体</returns>
    public async Task<Dic> GetByIdAsync(int id)
    {
        if (id <= 0)
            throw new ApiException("DicID无效");

        var entity = await _dicService.GetByIdAsync(id);
        if (entity == null)
            throw new ApiException("Dic不存在");

        return entity;
    }

    /// <summary>
    /// 获取Dic列表
    /// </summary>
    /// <param name="req">查询请求</param>
    /// <returns>Dic列表</returns>
    public async Task<ListRes<DicDto>> GetPageListAsync(PageListReq<DicPageListReq> req)
    {
        if (req.IsSortEmpty)
            req.Sorts = [new PageSort(nameof(Dic.UpdateTime), true)];

        var res = await _dicService.GetPageListAsync(req);
        return res;
    }

    /// <summary>
    /// 创建Dic
    /// </summary>
    /// <param name="entity">Dic实体</param>
    /// <returns>创建的Dic</returns>
    public async Task<Dic> CreateAsync(Dic entity)
    {
        entity.CreateTime = DateTime.Now;
        entity.CreatorId = _contextUser.UserId;

        var createdDic = await _dicService.CreateAsync(entity);

        return createdDic;
    }

    /// <summary>
    /// 更新Dic
    /// </summary>
    /// <param name="entity">Dic实体</param>
    /// <returns>更新结果</returns>
    public async Task<bool> UpdateAsync(Dic entity)
    {
        // 检查Dic是否存在
        var existingDic = await _dicService.GetByIdAsync(entity.Id);
        if (existingDic == null)
            throw new ApiException("Dic不存在");

        entity.UpdateTime = DateTime.Now;

        var result = await _dicService.UpdateAsync(entity);

        return result;
    }

    /// <summary>
    /// 删除Dic
    /// </summary>
    /// <param name="id">DicID</param>
    /// <returns>删除结果</returns>
    public async Task<bool> DeleteAsync(int id)
    {
        if (id <= 0)
            throw new ApiException("DicID无效");

        // 检查Dic是否存在
        var entity = await _dicService.GetByIdAsync(id);
        if (entity == null)
            throw new ApiException("Dic不存在");

        var result = await _dicService.DeleteAsync(id);

        return result;
    }


}
