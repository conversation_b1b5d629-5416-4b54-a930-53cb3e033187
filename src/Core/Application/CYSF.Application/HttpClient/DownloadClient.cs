using System.IO;
using System.Threading.Tasks;

namespace CYSF.Application.HttpClient
{
    /// <summary>
    ///     下载远程文件
    /// </summary>
    public class DownloadClient
    {
        private readonly System.Net.Http.HttpClient _httpClient;

        public DownloadClient(System.Net.Http.HttpClient httpClient)
        {
            _httpClient = httpClient;
        }

        /// <summary>
        /// 下载文件
        /// </summary>
        /// <param name="url">下载链接</param>
        /// <param name="destDir">下载目录</param>
        /// <param name="destFilePath">保存路径（包含下载目录）</param>
        /// <returns></returns>
        public async Task<bool> Download(string url,string destDir, string destFilePath)
        {
            using var result = await _httpClient.GetAsync(url);
            if (!result.IsSuccessStatusCode) return false;
            var res = await result.Content.ReadAsByteArrayAsync();
            if (!File.Exists(destDir))
                Directory.CreateDirectory(destDir);
            await File.WriteAllBytesAsync(destFilePath, res);
            return true;
        }
    }
}