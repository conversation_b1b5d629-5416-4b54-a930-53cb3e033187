using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Mapster;
using CYSF.Services;
using CYSF.Models.Request.Tenant;
using CYSF.Models.Response;
using CYSF.Core.Cache;
using CYSF.Models.Entities;
using CYSF.Core.Exceptions;
using CYSF.Core.Models;
using CYSF.Models.Const;
using CYSF.Models.Request;
using CYSF.Core.Helpers;
using CYSF.Core.Extensions;
using CYSF.Core.HttpContextUser;

namespace CYSF.Application;

/// <summary>
/// Tenant应用服务
/// </summary>
public class TenantApp
{
    private readonly TenantService _tenantService;
    private readonly ILogger<TenantApp> _logger;
    private readonly IContextUser _contextUser;
    private readonly CacheHelper _cacheHelper;

    public TenantApp(
        TenantService tenantService,
        ILogger<TenantApp> logger,
        IContextUser contextUser,
        CacheHelper cacheHelper)
    {
        _tenantService = tenantService;
        _logger = logger;
        _contextUser = contextUser;
        _cacheHelper = cacheHelper;
    }

    /// <summary>
    /// 根据ID获取Tenant
    /// </summary>
    /// <param name="id">TenantID</param>
    /// <returns>Tenant实体</returns>
    public async Task<Tenant> GetByIdAsync(int id)
    {
        if (id <= 0)
            throw new ApiException("TenantID无效");

        var entity = await _tenantService.GetByIdAsync(id);
        if (entity == null)
            throw new ApiException("Tenant不存在");

        return entity;
    }

    /// <summary>
    /// 获取Tenant列表
    /// </summary>
    /// <param name="req">查询请求</param>
    /// <returns>Tenant列表</returns>
    public async Task<ListRes<TenantDto>> GetPageListAsync(PageListReq<TenantPageListReq> req)
    {
        if (req.IsSortEmpty)
            req.Sorts = [new PageSort(nameof(Tenant.UpdateTime), true)];

        var res = await _tenantService.GetPageListAsync(req);
        return res;
    }

    /// <summary>
    /// 创建Tenant
    /// </summary>
    /// <param name="entity">Tenant实体</param>
    /// <returns>创建的Tenant</returns>
    public async Task<Tenant> CreateAsync(Tenant entity)
    {
        entity.CreateTime = DateTime.Now;
        entity.CreatorId = _contextUser.UserId;

        var createdTenant = await _tenantService.CreateAsync(entity);

        return createdTenant;
    }

    /// <summary>
    /// 更新Tenant
    /// </summary>
    /// <param name="entity">Tenant实体</param>
    /// <returns>更新结果</returns>
    public async Task<bool> UpdateAsync(Tenant entity)
    {
        // 检查Tenant是否存在
        var existingTenant = await _tenantService.GetByIdAsync(entity.Id);
        if (existingTenant == null)
            throw new ApiException("Tenant不存在");

        entity.UpdateTime = DateTime.Now;

        var result = await _tenantService.UpdateAsync(entity);

        return result;
    }

    /// <summary>
    /// 删除Tenant
    /// </summary>
    /// <param name="id">TenantID</param>
    /// <returns>删除结果</returns>
    public async Task<bool> DeleteAsync(int id)
    {
        if (id <= 0)
            throw new ApiException("TenantID无效");

        // 检查Tenant是否存在
        var entity = await _tenantService.GetByIdAsync(id);
        if (entity == null)
            throw new ApiException("Tenant不存在");

        var result = await _tenantService.DeleteAsync(id);

        return result;
    }

    /// <summary>
    /// 批量删除Tenant
    /// </summary>
    /// <param name="ids">TenantID列表</param>
    /// <returns>删除结果</returns>
    public async Task<bool> DeleteBatchAsync(List<int> ids)
    {
        if (ids == null || ids.Count == 0)
            throw new ApiException("请选择要删除的Tenant");

        var result = await _tenantService.DeleteBatchAsync(ids);

        return result;
    }

    /// <summary>
    /// 检查Tenant是否存在
    /// </summary>
    /// <param name="id">TenantID</param>
    /// <returns>是否存在</returns>
    public async Task<bool> ExistsAsync(int id)
    {
        return await _tenantService.ExistsAsync(id);
    }
}
