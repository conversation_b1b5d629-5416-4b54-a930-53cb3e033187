using System.Threading.Tasks;
using CYSF.Models.Entities;
using CYSF.Services;
using Microsoft.Extensions.Logging;

namespace CYSF.Application;

public class TenantApp
{
    private readonly TenantService _tenantService;
    private readonly ILogger<TenantApp> _logger;

    public TenantApp(TenantService tenantService,ILogger<TenantApp> logger)
    {
        _tenantService = tenantService;
        _logger = logger;
    }

    public async Task<Tenant> GetByIdAsync(int id)
    {
        return await _tenantService.GetByIdAsync(id);
    }
}