using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CYSF.Core.Enums;
using Microsoft.Extensions.Logging;
using Mapster;
using CYSF.Services;
using CYSF.Models.Request.Tenant;
using CYSF.Models.Entities;
using CYSF.Core.Exceptions;
using CYSF.Core.Models;
using CYSF.Models.Const;
using CYSF.Models.Request;
using CYSF.Models.Response;
using CYSF.Core.Cache;
using CYSF.Core.HttpContextUser;

namespace CYSF.Application;

/// <summary>
/// 租户应用服务 - 只有平台管理员可以管理租户
/// </summary>
public class TenantApp
{
    private readonly TenantUserService _tenantUserService;
    private readonly ILogger<TenantApp> _logger;
    private readonly CacheHelper _cacheHelper;
    private readonly IContextUser _contextUser;

    public TenantApp(
        TenantUserService tenantUserService,
        ILogger<TenantApp> logger,
        CacheHelper cacheHelper,
        IContextUser contextUser)
    {
        _tenantUserService = tenantUserService;
        _logger = logger;
        _cacheHelper = cacheHelper;
        _contextUser = contextUser;
    }

    /// <summary>
    /// 检查当前用户是否为平台管理员
    /// </summary>
    private void CheckPlatformAdmin()
    {
        if (_contextUser.IsTenant())
            throw new ApiException("只有平台管理员可以管理租户");
    }

    /// <summary>
    /// 根据ID获取租户（带缓存）
    /// </summary>
    /// <param name="tenantId">租户ID</param>
    /// <returns>租户实体</returns>
    public async Task<Tenant> Get(int tenantId)
    {
        return await _cacheHelper.CacheShellAsync(
            CacheKeys.Tenant(tenantId),
            CacheKeys.DayExpired,
            () => _tenantService.GetAsync(tenantId)
        );
    }

    /// <summary>
    /// 获取租户列表 - 只有平台管理员可以查看
    /// </summary>
    /// <param name="req">查询请求</param>
    /// <returns>租户列表</returns>
    public async Task<ListRes<Tenant>> GetPageListAsync(PageListReq<TenantPageListReq> req)
    {
        CheckPlatformAdmin();

        if(req.IsSortEmpty)
            req.Sorts = [new PageSort(nameof(Tenant.UpdateTime), true)];

        var res = await _tenantService.GetPageListAsync(req);
        return res;
    }

    /// <summary>
    /// 根据ID获取租户详情 - 只有平台管理员可以查看
    /// </summary>
    /// <param name="id">租户ID</param>
    /// <returns>租户详情</returns>
    public async Task<Tenant> GetByIdAsync(int id)
    {
        CheckPlatformAdmin();

        if (id <= 0)
            throw new ApiException("租户ID无效");

        var tenant = await _tenantService.GetByIdAsync(id);
        if (tenant == null)
            throw new ApiException("租户不存在");

        return tenant;
    }

    /// <summary>
    /// 创建租户 - 只有平台管理员可以创建
    /// </summary>
    /// <param name="req">创建请求</param>
    /// <returns>创建的租户</returns>
    public async Task<Tenant> CreateAsync(CreateTenantReq req)
    {
        CheckPlatformAdmin();

        // 检查租户编号是否已存在
        if (await _tenantService.ExistsByCodeAsync(req.Code))
            throw new ApiException($"租户编号 {req.Code} 已存在");

        // 使用Mapster映射
        var tenant = req.Adapt<Tenant>();
        tenant.CreateTime = DateTime.Now;
        tenant.CreatorId = _contextUser.UserId;

        // 使用Service层的组合方法（内部管理事务）
        var createdTenant = await _tenantService.CreateWithInitAsync(tenant);

        _logger.LogInformation("创建租户成功，租户ID: {TenantId}，租户编号: {Code}，租户名称: {Name}",
            createdTenant.Id, createdTenant.Code, createdTenant.ShortName);

        return createdTenant;
    }

    /// <summary>
    /// 更新租户
    /// </summary>
    /// <param name="req">更新请求</param>
    /// <returns>更新结果</returns>
    public async Task<bool> UpdateAsync(UpdateTenantReq req)
    {
        // 检查租户是否存在
        var existingTenant = await _tenantService.GetByIdAsync(req.Id);
        if (existingTenant == null)
            throw new ApiException("租户不存在");

        // 检查租户编号是否被其他租户使用
        if (await _tenantService.ExistsByCodeAsync(req.Code, req.Id))
            throw new ApiException($"租户编号 {req.Code} 已被其他租户使用");

        // 使用Mapster映射更新字段
        req.Adapt(existingTenant);
        existingTenant.UpdateTime = DateTime.Now;

        var result = await _tenantService.UpdateAsync(existingTenant);

        // 清除缓存
        if (result)
        {
            await _cacheHelper.RemoveAsync(CacheKeys.Tenant(req.Id));
        }

        return result;
    }

    /// <summary>
    /// 删除租户
    /// </summary>
    /// <param name="id">租户ID</param>
    /// <returns>删除结果</returns>
    public async Task<bool> DeleteAsync(int id)
    {
        if (id <= 0)
            throw new ApiException("租户ID无效");

        // 检查租户是否存在
        var tenant = await _tenantService.GetByIdAsync(id);
        if (tenant == null)
            throw new ApiException("租户不存在");

        // 检查租户下是否有用户 - 暂时跳过检查

        // 删除租户
        var result = await _tenantService.DeleteAsync(id);

        // 清除缓存
        if (result)
        {
            await _cacheHelper.RemoveAsync(CacheKeys.Tenant(id));
        }

        return result;
    }

    /// <summary>
    /// 检查租户编号是否可用
    /// </summary>
    /// <param name="code">租户编号</param>
    /// <param name="excludeId">排除的租户ID</param>
    /// <returns>是否可用</returns>
    public async Task<bool> IsCodeAvailableAsync(string code, int? excludeId = null)
    {
        if (string.IsNullOrWhiteSpace(code))
            return false;

        var exists = await _tenantService.ExistsByCodeAsync(code, excludeId);
        return !exists;
    }
}