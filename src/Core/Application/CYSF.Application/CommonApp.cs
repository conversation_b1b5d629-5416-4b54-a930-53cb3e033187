using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CYSF.Core.Helpers;
using CYSF.Core.Models;
using CYSF.Models.Const;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;

namespace CYSF.Application
{
    public class CommonApp
    {
        private readonly ILogger<CommonApp> _logger;
        private readonly IMemoryCache _memoryCache;

        public CommonApp(ILogger<CommonApp> logger, IMemoryCache memoryCache)
        {
            _logger = logger;
            _memoryCache = memoryCache;
        }

        /// <summary>
        ///     获取一个或者多个枚举键值对
        ///     多个使用英文逗号分隔
        /// </summary>
        /// <param name="enums">枚举类型名称</param>
        /// <returns></returns>
        public async Task<Dictionary<string, List<EnumItem>>> GetEnumList(string enums)
        {
            var res = new Dictionary<string, List<EnumItem>>();
            if (string.IsNullOrEmpty(enums)) return res;
            foreach (var name in enums.Split(',', StringSplitOptions.RemoveEmptyEntries))
                res.Add(name, _memoryCache.GetOrCreate(CacheKeys.Enum(name), entry =>
                {
                    entry.SlidingExpiration = TimeSpan.FromMinutes(30);
                    entry.Size = 1; // 设置缓存项大小，当配置了SizeLimit时必需
                    return EnumHelper.GetEnumList(name);
                }));

            return await Task.FromResult(res);
        }
    }
}