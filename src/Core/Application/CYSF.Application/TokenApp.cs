using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Security.Principal;
using System.Text;
using System.Threading.Tasks;
using CYSF.Core.Enums;
using CYSF.Core.Exceptions;
using CYSF.Core.Extensions;
using CYSF.Core.Helpers;
using CYSF.Core.HttpContextUser;
using CYSF.Core.Models;
using CYSF.Models.Const;
using CYSF.Models.Entities;
using CYSF.Models.Enum;
using CYSF.Models.Request.Auth;
using CYSF.Models.Response;
using CYSF.Core.Cache;
using Mapster;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;

namespace CYSF.Application
{
    public class TokenApp(
        IContextUser contextUser,
        ILogger<TokenApp> logger,
        IHttpContextAccessor httpContextAccessor,
        IWebHostEnvironment webHostEnvironment,
        TenantUserApp tenantUserApp,
        TenantApp tenantApp,
        IOptions<JwtIssuerOptions> jwtOptions,
        CacheHelper cacheHelper)
    {
        private readonly ILogger<TokenApp> _logger = logger;
        private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;
        private readonly IWebHostEnvironment _webHostEnvironment = webHostEnvironment;
        private readonly TenantApp _tenantApp = tenantApp;
        private readonly JwtIssuerOptions _jwtOptions = jwtOptions.Value;
        private readonly CacheHelper _cacheHelper = cacheHelper;

        /// <summary>
        ///     获取Token
        /// </summary>
        /// <returns></returns>
        /// <exception cref="ApiException"></exception>
        public async Task<TokenDto> GetToken(LoginReq req)
        {
            TenantUser tenantUser = null;
            if (req.Identifier.IsMatch(RegExpressions.mobile))
                tenantUser = await tenantUserApp.GetByMobile(req.Identifier);
            else
                tenantUser = await tenantUserApp.GetByUserName(req.Identifier);
            
            if (tenantUser == null)
                throw new CustomException("账号不存在");

            if (tenantUser.UserState != UserState.Normal)
                throw new ApiException("账号状态异常,登录失败");

            if (tenantUser.PasswordSalt.IsNotNullOrEmpty() && tenantUser.Password != req.Password.EncryptToMd5(tenantUser.PasswordSalt))
                throw new ApiException("登录密码错误");
            
            if(tenantUser.PasswordSalt.IsNullOrEmpty() && tenantUser.Password != req.Password)
                throw new ApiException("登录密码错误");
            
            var res = new TokenDto
            {
                UserId = tenantUser.Id,
                RoleId = tenantUser.TenantId == 0 ? 0 : tenantUser.RoleId,
                TenantId = tenantUser.TenantId,
                TenantName = string.Empty,
                Name = tenantUser.Name,
                Token = string.Empty,
                RefreshToken = string.Empty,
                TokenExpires = 0,
                RefreshTokenExpires = 0,
            };
            
            if(res.TenantId > 0)
            {
                var tenant = await _tenantApp.Get(res.TenantId);
                res.TenantName = tenant.ShortName ?? tenant.CompanyName;
            }

            //生成Token
            (res.Token, res.RefreshToken) = await GenerateToken(tenantUser.Name, tenantUser.Id, tenantUser.RoleId, tenantUser.TenantId);

            //RefreshToken持久化（Token不再持久化，避免MemoryCache重启导致用户下线）
            await _cacheHelper.SetAsync(CacheKeys.RefreshToken(tenantUser.Id, res.RefreshToken), res.RefreshToken, _jwtOptions.RefreshTokenExpireTimeSpan);
            return res;
            
        }

        /// <summary>
        ///     退出,删除Token
        /// </summary>
        /// <param name="req"></param>
        /// <returns></returns>
        public async Task<bool> DelToken(DelTokenReq req)
        {
            if (contextUser != null)
            {
                // 只删除RefreshToken缓存（Token不再持久化，无需删除）
                if (req.RefreshToken.IsNotNullOrEmpty())
                {
                    var refreshTokenKey = CacheKeys.RefreshToken(contextUser.UserId, req.RefreshToken);
                    await _cacheHelper.RemoveAsync(refreshTokenKey);
                }
            }

            return true;
        }

        /// <summary>
        ///     刷新令牌
        /// </summary>
        /// <param name="req">请求实体</param>
        /// <returns></returns>
        public async Task<TokenDto> RefreshToken(RefreshTokenReq req)
        {
            if (req.RefreshToken.IsNullOrEmpty() || req.UserId == 0)
                throw new SecurityTokenExpiredException("您的登录身份已经过期，请重新登录");

            //验证刷新Token是否过期
            var currentRefreshToken = await _cacheHelper.GetAsync<string>(CacheKeys.RefreshToken(req.UserId, req.RefreshToken));
            if (string.IsNullOrEmpty(currentRefreshToken))
                throw new SecurityTokenExpiredException("您的登录身份已经过期，请重新登录");

            var tenantUser = await tenantUserApp.GetByUserId(req.UserId);
            if (tenantUser is not { UserState: UserState.Normal })
                throw new SecurityTokenExpiredException("账号不存在或状态异常,登录失败");

            var res = tenantUser.Adapt<TokenDto>();
            
            (res.Token, res.RefreshToken) =
                await GenerateToken(tenantUser.Name, tenantUser.Id, tenantUser.RoleId, tenantUser.TenantId);
            
            //RefreshToken持久化：删除旧的RefreshToken，保存新的RefreshToken（Token不再持久化）
            await _cacheHelper.RemoveAsync(CacheKeys.RefreshToken(tenantUser.Id, req.RefreshToken));
            await _cacheHelper.SetAsync(CacheKeys.RefreshToken(tenantUser.Id, res.RefreshToken), res.RefreshToken, _jwtOptions.RefreshTokenExpireTimeSpan);
            return res;
        }
        #region 私有方法

        private void ThrowIfInvalidOptions(JwtIssuerOptions options)
        {
            if (options == null) throw new ArgumentNullException(nameof(options));

            if (options.TokenExpireTimeSpan <= TimeSpan.Zero)
                throw new ArgumentException("过期时间不能为0", nameof(JwtIssuerOptions.TokenExpireTimeSpan));

            if (options.SigningCredentials == null)
                throw new ArgumentNullException(nameof(JwtIssuerOptions.SigningCredentials));

            if (options.JtiGenerator == null) throw new ArgumentNullException(nameof(JwtIssuerOptions.JtiGenerator));
        }

        private static long ToUnixEpochDate(DateTime date)
        {
            return (long)Math.Round((date.ToUniversalTime() - new DateTimeOffset(1970, 1, 1, 0, 0, 0, TimeSpan.Zero))
                .TotalSeconds);
        }

        private async Task<(string token, string refreshToken)> GenerateToken(string name,int userId, int roleId, int tenantId)
        {
            var identity = new ClaimsIdentity(new GenericIdentity(JwtBearerDefaults.AuthenticationScheme));
            var refreshToken = $"{Guid.NewGuid():N}";
            identity.AddClaim(new Claim("Name", name));
            identity.AddClaim(new Claim("UserId", userId.ToString()));
            identity.AddClaim(new Claim("RoleId", roleId.ToString()));
            identity.AddClaim(new Claim("TenantId", tenantId.ToString()));
            identity.AddClaim(new Claim("RefreshToken", refreshToken));

            var claims = new List<Claim>
            {
                new(JwtRegisteredClaimNames.Sub, userId.ToString()),
                new(JwtRegisteredClaimNames.Jti, await _jwtOptions.JtiGenerator()),
                new(JwtRegisteredClaimNames.Iat, ToUnixEpochDate(_jwtOptions.IssuedAt).ToString(),
                    ClaimValueTypes.Integer64),
                identity.FindFirst("Name"),
                identity.FindFirst("UserId"),
                identity.FindFirst("RoleId"),
                identity.FindFirst("TenantId"),
                identity.FindFirst("RefreshToken")
            };

            claims.AddRange(identity.FindAll(ClaimTypes.Role));
            ThrowIfInvalidOptions(_jwtOptions);
            var jwt = new JwtSecurityToken(
                _jwtOptions.Issuer,
                _jwtOptions.Audience,
                claims,
                _jwtOptions.NotBefore,
                _jwtOptions.Expiration,
                _jwtOptions.SigningCredentials);

            return (new JwtSecurityTokenHandler().WriteToken(jwt), refreshToken);
        }

        #endregion
    }
}
