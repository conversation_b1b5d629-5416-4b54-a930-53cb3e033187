using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Mapster;
using CYSF.Services;
using CYSF.Models.Request.TenantUser;
using CYSF.Models.Response;
using CYSF.Core.Cache;
using CYSF.Models.Entities;
using CYSF.Core.Exceptions;
using CYSF.Core.Models;
using CYSF.Models.Const;
using CYSF.Models.Request;
using CYSF.Core.Helpers;
using CYSF.Core.Extensions;
using CYSF.Core.HttpContextUser;

namespace CYSF.Application;

/// <summary>
/// 租户用户应用服务 - 平台管理员可以管理所有用户，租户管理员只能管理自己租户的用户
/// </summary>
public class TenantUserApp
{
    private readonly TenantUserService _tenantUserService;
    private readonly ILogger<TenantUserApp> _logger;
    private readonly IContextUser _contextUser;
    private readonly CacheHelper _cacheHelper;

    public TenantUserApp(
        TenantUserService tenantUserService,
        ILogger<TenantUserApp> logger,
        IContextUser contextUser,
        CacheHelper cacheHelper)
    {
        _tenantUserService = tenantUserService;
        _logger = logger;
        _contextUser = contextUser;
        _cacheHelper = cacheHelper;
    }

    /// <summary>
    /// 根据ID获取用户（带缓存）
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>用户实体</returns>
    public async Task<TenantUser> Get(int userId)
    {
        return await _cacheHelper.CacheShellAsync(
            CacheKeys.TenantUser(userId),
            CacheKeys.DayExpired,
            () => _tenantUserService.GetByIdAsync(userId)
        );
    }

    /// <summary>
    /// 获取用户列表 - 根据当前用户权限过滤
    /// </summary>
    /// <param name="req">查询请求</param>
    /// <returns>用户列表</returns>
    public async Task<ListRes<TenantUserDto>> GetPageListAsync(PageListReq<TenantUserPageListReq> req)
    {
        if (req.IsSortEmpty)
            req.Sorts = [new PageSort(nameof(TenantUser.UpdateTime), true)];

        // 租户权限控制：租户只能查看自己的用户
        if (_contextUser.IsTenant())
        {
            req.Filters.TenantId = _contextUser.TenantId;
        }

        var res = await _tenantUserService.GetPageListAsync(req);
        return res;
    }

    /// <summary>
    /// 根据ID获取用户详情
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <returns>用户详情</returns>
    public async Task<TenantUser> GetByIdAsync(int id)
    {
        if (id <= 0)
            throw new ApiException("用户ID无效");

        var user = await _tenantUserService.GetByIdAsync(id);
        if (user == null)
            throw new ApiException("用户不存在");

        // 租户权限检查：租户只能查看自己租户的用户
        if (_contextUser.IsTenant() && user.TenantId != _contextUser.TenantId)
            throw new ApiException("无权查看此用户");

        return user;
    }

    /// <summary>
    /// 创建用户 - 平台管理员可以为任意租户创建用户，租户管理员只能为自己创建用户
    /// </summary>
    /// <param name="req">创建请求</param>
    /// <returns>创建的用户</returns>
    public async Task<TenantUser> CreateAsync(CreateTenantUserReq req)
    {
        // 确定租户ID：租户用户只能为自己创建用户，平台管理员可以为指定租户创建用户
        var tenantId = _contextUser.IsTenant() ? _contextUser.TenantId : req.TenantId;

        // 检查用户名是否已存在（在指定租户范围内）
        if (await _tenantUserService.ExistsByUserNameAsync(req.UserName, tenantId))
            throw new ApiException($"用户名 {req.UserName} 已存在");

        // 检查手机号是否已存在（在指定租户范围内）
        if (await _tenantUserService.ExistsByMobileAsync(req.Mobile, tenantId))
            throw new ApiException($"手机号 {req.Mobile} 已存在");

        // 使用Mapster映射
        var user = req.Adapt<TenantUser>();
        user.TenantId = tenantId;
        user.CreateTime = DateTime.Now;
        user.CreatorId = _contextUser.UserId;

        // 密码加密
        var passwordSalt = GeneratePasswordSalt();
        user.PasswordSalt = passwordSalt;
        user.Password = req.Password.EncryptToMd5(passwordSalt);

        var createdUser = await _tenantUserService.CreateAsync(user);

        _logger.LogInformation("创建用户成功，用户ID: {UserId}，用户名: {UserName}，租户ID: {TenantId}",
            createdUser.Id, createdUser.UserName, createdUser.TenantId);

        return createdUser;
    }

    /// <summary>
    /// 创建平台管理员 - 只有平台管理员可以创建其他平台管理员
    /// </summary>
    /// <param name="req">创建请求</param>
    /// <returns>创建的平台管理员</returns>
    public async Task<TenantUser> CreatePlatformAdminAsync(CreateTenantUserReq req)
    {
        // 检查当前用户是否为平台管理员
        if (_contextUser.IsTenant())
            throw new ApiException("只有平台管理员可以创建其他平台管理员");

        // 检查用户名是否已存在（全局检查）
        if (await _tenantUserService.ExistsByUserNameAsync(req.UserName))
            throw new ApiException($"用户名 {req.UserName} 已存在");

        // 检查手机号是否已存在（全局检查）
        if (await _tenantUserService.ExistsByMobileAsync(req.Mobile))
            throw new ApiException($"手机号 {req.Mobile} 已存在");

        // 使用Mapster映射
        var user = req.Adapt<TenantUser>();
        user.TenantId = 0; // 平台管理员的TenantId为0
        user.RoleId = 0;   // 平台管理员的RoleId为0
        user.CreateTime = DateTime.Now;
        user.CreatorId = _contextUser.UserId;

        // 密码加密
        var passwordSalt = GeneratePasswordSalt();
        user.PasswordSalt = passwordSalt;
        user.Password = req.Password.EncryptToMd5(passwordSalt);

        var createdUser = await _tenantUserService.CreateAsync(user);

        _logger.LogInformation("创建平台管理员成功，用户ID: {UserId}，用户名: {UserName}",
            createdUser.Id, createdUser.UserName);

        return createdUser;
    }

    /// <summary>
    /// 更新用户
    /// </summary>
    /// <param name="req">更新请求</param>
    /// <returns>更新结果</returns>
    public async Task<bool> UpdateAsync(UpdateTenantUserReq req)
    {
        // 检查用户是否存在
        var existingUser = await _tenantUserService.GetByIdAsync(req.Id);
        if (existingUser == null)
            throw new ApiException("用户不存在");

        if (_contextUser.IsTenant() && existingUser.TenantId != _contextUser.TenantId)
            throw new ApiException("无权更改");

        // 检查用户名是否被其他用户使用
        if (await _tenantUserService.ExistsByUserNameAsync(req.UserName, req.Id))
            throw new ApiException($"用户名 {req.UserName} 已被其他用户使用");

        // 检查手机号是否被其他用户使用
        if (await _tenantUserService.ExistsByMobileAsync(req.Mobile, req.Id))
            throw new ApiException($"手机号 {req.Mobile} 已被其他用户使用");

        // 使用Mapster映射更新字段
        req.Adapt(existingUser);
        existingUser.UpdateTime = DateTime.Now;

        var result = await _tenantUserService.UpdateAsync(existingUser);

        // 清除缓存
        if (result)
            await _cacheHelper.RemoveAsync(CacheKeys.TenantUser(req.Id));

        return result;
    }

    /// <summary>
    /// 删除用户
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <returns>删除结果</returns>
    public async Task<bool> DeleteAsync(int id)
    {
        if (id <= 0)
            throw new ApiException("用户ID无效");

        // 检查用户是否存在
        var user = await _tenantUserService.GetByIdAsync(id);
        if (user == null)
            throw new ApiException("用户不存在");

        if(_contextUser.IsTenant() && user.TenantId != _contextUser.TenantId)
            throw new ApiException("无权删除");

        var result = await _tenantUserService.DeleteAsync(id);

        // 清除缓存
        if (result)
        {
            await _cacheHelper.RemoveAsync(CacheKeys.TenantUser(id));
        }

        return result;
    }

    /// <summary>
    /// 检查用户名是否可用
    /// </summary>
    /// <param name="userName">用户名</param>
    /// <param name="excludeId">排除的用户ID</param>
    /// <returns>是否可用</returns>
    public async Task<bool> IsUserNameAvailableAsync(string userName, int? excludeId = null)
    {
        if (string.IsNullOrWhiteSpace(userName))
            return false;

        var exists = await _tenantUserService.ExistsByUserNameAsync(userName, excludeId);
        return !exists;
    }

    /// <summary>
    /// 检查手机号是否可用
    /// </summary>
    /// <param name="mobile">手机号</param>
    /// <param name="excludeId">排除的用户ID</param>
    /// <returns>是否可用</returns>
    public async Task<bool> IsMobileAvailableAsync(string mobile, int? excludeId = null)
    {
        if (string.IsNullOrWhiteSpace(mobile))
            return false;

        var exists = await _tenantUserService.ExistsByMobileAsync(mobile, excludeId);
        return !exists;
    }

    /// <summary>
    /// 重置用户密码
    /// </summary>
    /// <param name="req">重置密码请求</param>
    /// <returns>重置结果</returns>
    public async Task<bool> ResetPasswordAsync(ResetPasswordReq req)
    {
        // 检查用户是否存在
        var user = await _tenantUserService.GetByIdAsync(req.UserId);
        if (user == null)
            throw new ApiException("用户不存在");

        // 生成新的密码盐和加密密码
        var passwordSalt = GeneratePasswordSalt();
        var hashedPassword = req.NewPassword.EncryptToMd5(passwordSalt);

        var result = await _tenantUserService.UpdatePasswordAsync(req.UserId, hashedPassword, passwordSalt);

        // 清除缓存
        if (result)
        {
            await _cacheHelper.RemoveAsync(CacheKeys.TenantUser(req.UserId));
        }

        return result;
    }

    /// <summary>
    /// 根据手机号获取用户
    /// </summary>
    /// <param name="mobile">手机号</param>
    /// <returns>用户实体</returns>
    public async Task<TenantUser> GetByMobile(string mobile)
    {
        return await _tenantUserService.GetByMobile(mobile);
    }

    /// <summary>
    /// 根据用户名获取用户
    /// </summary>
    /// <param name="userName">用户名</param>
    /// <returns>用户实体</returns>
    public async Task<TenantUser> GetByUserName(string userName)
    {
        return await _tenantUserService.GetByUserName(userName);
    }

    /// <summary>
    /// 根据用户ID获取用户
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>用户实体</returns>
    public async Task<TenantUser> GetByUserId(int userId)
    {
        return await _tenantUserService.GetByUserId(userId);
    }

    /// <summary>
    /// 生成密码盐
    /// </summary>
    /// <returns>密码盐</returns>
    private static string GeneratePasswordSalt()
    {
        return Guid.NewGuid().ToString("N")[..16];
    }
}