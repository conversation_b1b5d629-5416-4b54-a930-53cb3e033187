using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Mapster;
using CYSF.Services;
using CYSF.Models.Request.TenantUser;
using CYSF.Models.Response;
using CYSF.Core.Cache;
using CYSF.Models.Entities;
using CYSF.Core.Exceptions;
using CYSF.Core.Models;
using CYSF.Models.Const;
using CYSF.Models.Request;
using CYSF.Core.Helpers;
using CYSF.Core.Extensions;
using CYSF.Core.HttpContextUser;

namespace CYSF.Application;

/// <summary>
/// TenantUser应用服务
/// </summary>
public class TenantUserApp
{
    private readonly TenantUserService _tenantUserService;
    private readonly ILogger<TenantUserApp> _logger;
    private readonly IContextUser _contextUser;
    private readonly CacheHelper _cacheHelper;

    public TenantUserApp(
        TenantUserService tenantUserService,
        ILogger<TenantUserApp> logger,
        IContextUser contextUser,
        CacheHelper cacheHelper)
    {
        _tenantUserService = tenantUserService;
        _logger = logger;
        _contextUser = contextUser;
        _cacheHelper = cacheHelper;
    }

    /// <summary>
    /// 根据ID获取TenantUser
    /// </summary>
    /// <param name="id">TenantUserID</param>
    /// <returns>TenantUser实体</returns>
    public async Task<TenantUser> GetByIdAsync(int id)
    {
        if (id <= 0)
            throw new ApiException("TenantUserID无效");

        var entity = await _tenantUserService.GetByIdAsync(id);
        if (entity == null)
            throw new ApiException("TenantUser不存在");

        return entity;
    }

    /// <summary>
    /// 获取TenantUser列表
    /// </summary>
    /// <param name="req">查询请求</param>
    /// <returns>TenantUser列表</returns>
    public async Task<ListRes<TenantUserDto>> GetPageListAsync(PageListReq<TenantUserPageListReq> req)
    {
        if (req.IsSortEmpty)
            req.Sorts = [new PageSort(nameof(TenantUser.UpdateTime), true)];

        var res = await _tenantUserService.GetPageListAsync(req);
        return res;
    }

    /// <summary>
    /// 创建TenantUser
    /// </summary>
    /// <param name="entity">TenantUser实体</param>
    /// <returns>创建的TenantUser</returns>
    public async Task<TenantUser> CreateAsync(TenantUser entity)
    {
        entity.CreateTime = DateTime.Now;
        entity.CreatorId = _contextUser.UserId;

        var createdTenantUser = await _tenantUserService.CreateAsync(entity);

        return createdTenantUser;
    }

    /// <summary>
    /// 更新TenantUser
    /// </summary>
    /// <param name="entity">TenantUser实体</param>
    /// <returns>更新结果</returns>
    public async Task<bool> UpdateAsync(TenantUser entity)
    {
        // 检查TenantUser是否存在
        var existingTenantUser = await _tenantUserService.GetByIdAsync(entity.Id);
        if (existingTenantUser == null)
            throw new ApiException("TenantUser不存在");

        entity.UpdateTime = DateTime.Now;

        var result = await _tenantUserService.UpdateAsync(entity);

        return result;
    }

    /// <summary>
    /// 删除TenantUser
    /// </summary>
    /// <param name="id">TenantUserID</param>
    /// <returns>删除结果</returns>
    public async Task<bool> DeleteAsync(int id)
    {
        if (id <= 0)
            throw new ApiException("TenantUserID无效");

        // 检查TenantUser是否存在
        var entity = await _tenantUserService.GetByIdAsync(id);
        if (entity == null)
            throw new ApiException("TenantUser不存在");

        var result = await _tenantUserService.DeleteAsync(id);

        return result;
    }

    /// <summary>
    /// 批量删除TenantUser
    /// </summary>
    /// <param name="ids">TenantUserID列表</param>
    /// <returns>删除结果</returns>
    public async Task<bool> DeleteBatchAsync(List<int> ids)
    {
        if (ids == null || ids.Count == 0)
            throw new ApiException("请选择要删除的TenantUser");

        var result = await _tenantUserService.DeleteBatchAsync(ids);

        return result;
    }

    /// <summary>
    /// 检查TenantUser是否存在
    /// </summary>
    /// <param name="id">TenantUserID</param>
    /// <returns>是否存在</returns>
    public async Task<bool> ExistsAsync(int id)
    {
        return await _tenantUserService.ExistsAsync(id);
    }
}
