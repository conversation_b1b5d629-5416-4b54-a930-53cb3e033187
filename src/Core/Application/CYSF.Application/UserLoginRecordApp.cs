using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Mapster;
using CYSF.Services;
using CYSF.Models.Request.UserLoginRecord;
using CYSF.Models.Response;
using CYSF.Core.Cache;
using CYSF.Models.Entities;
using CYSF.Core.Exceptions;
using CYSF.Core.Models;
using CYSF.Models.Const;
using CYSF.Models.Request;
using CYSF.Core.Helpers;
using CYSF.Core.Extensions;
using CYSF.Core.HttpContextUser;

namespace CYSF.Application;

/// <summary>
/// UserLoginRecord应用服务
/// </summary>
public class UserLoginRecordApp
{
    private readonly UserLoginRecordService _userLoginRecordService;
    private readonly ILogger<UserLoginRecordApp> _logger;
    private readonly IContextUser _contextUser;
    private readonly CacheHelper _cacheHelper;

    public UserLoginRecordApp(
        UserLoginRecordService userLoginRecordService,
        ILogger<UserLoginRecordApp> logger,
        IContextUser contextUser,
        CacheHelper cacheHelper)
    {
        _userLoginRecordService = userLoginRecordService;
        _logger = logger;
        _contextUser = contextUser;
        _cacheHelper = cacheHelper;
    }

    /// <summary>
    /// 根据ID获取UserLoginRecord
    /// </summary>
    /// <param name="id">UserLoginRecordID</param>
    /// <returns>UserLoginRecord实体</returns>
    public async Task<UserLoginRecord> GetByIdAsync(int id)
    {
        if (id <= 0)
            throw new ApiException("UserLoginRecordID无效");

        var entity = await _userLoginRecordService.GetByIdAsync(id);
        if (entity == null)
            throw new ApiException("UserLoginRecord不存在");

        return entity;
    }

    /// <summary>
    /// 获取UserLoginRecord列表
    /// </summary>
    /// <param name="req">查询请求</param>
    /// <returns>UserLoginRecord列表</returns>
    public async Task<ListRes<UserLoginRecordDto>> GetPageListAsync(PageListReq<UserLoginRecordPageListReq> req)
    {
        if (req.IsSortEmpty)
            req.Sorts = [new PageSort(nameof(UserLoginRecord.UpdateTime), true)];

        var res = await _userLoginRecordService.GetPageListAsync(req);
        return res;
    }

    /// <summary>
    /// 创建UserLoginRecord
    /// </summary>
    /// <param name="entity">UserLoginRecord实体</param>
    /// <returns>创建的UserLoginRecord</returns>
    public async Task<UserLoginRecord> CreateAsync(UserLoginRecord entity)
    {
        entity.CreateTime = DateTime.Now;
        entity.CreatorId = _contextUser.UserId;

        var createdUserLoginRecord = await _userLoginRecordService.CreateAsync(entity);

        return createdUserLoginRecord;
    }

    /// <summary>
    /// 更新UserLoginRecord
    /// </summary>
    /// <param name="entity">UserLoginRecord实体</param>
    /// <returns>更新结果</returns>
    public async Task<bool> UpdateAsync(UserLoginRecord entity)
    {
        // 检查UserLoginRecord是否存在
        var existingUserLoginRecord = await _userLoginRecordService.GetByIdAsync(entity.Id);
        if (existingUserLoginRecord == null)
            throw new ApiException("UserLoginRecord不存在");

        entity.UpdateTime = DateTime.Now;

        var result = await _userLoginRecordService.UpdateAsync(entity);

        return result;
    }

    /// <summary>
    /// 删除UserLoginRecord
    /// </summary>
    /// <param name="id">UserLoginRecordID</param>
    /// <returns>删除结果</returns>
    public async Task<bool> DeleteAsync(int id)
    {
        if (id <= 0)
            throw new ApiException("UserLoginRecordID无效");

        // 检查UserLoginRecord是否存在
        var entity = await _userLoginRecordService.GetByIdAsync(id);
        if (entity == null)
            throw new ApiException("UserLoginRecord不存在");

        var result = await _userLoginRecordService.DeleteAsync(id);

        return result;
    }

    /// <summary>
    /// 批量删除UserLoginRecord
    /// </summary>
    /// <param name="ids">UserLoginRecordID列表</param>
    /// <returns>删除结果</returns>
    public async Task<bool> DeleteBatchAsync(List<int> ids)
    {
        if (ids == null || ids.Count == 0)
            throw new ApiException("请选择要删除的UserLoginRecord");

        var result = await _userLoginRecordService.DeleteBatchAsync(ids);

        return result;
    }

    /// <summary>
    /// 检查UserLoginRecord是否存在
    /// </summary>
    /// <param name="id">UserLoginRecordID</param>
    /// <returns>是否存在</returns>
    public async Task<bool> ExistsAsync(int id)
    {
        return await _userLoginRecordService.ExistsAsync(id);
    }
}
