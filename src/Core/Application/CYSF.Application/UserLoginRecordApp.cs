using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CYSF.Services;
using Microsoft.Extensions.Logging;
using Mapster;
using CYSF.Models.Entities;
using CYSF.Models.Request.UserLoginRecord;
using CYSF.Models.Response;
using CYSF.Core.Cache;
using CYSF.Models.Const;
using CYSF.Core.HttpContextUser;
using CYSF.Models.Request;
using CYSF.Core.Exceptions;

namespace CYSF.Application;

/// <summary>
/// 用户登录记录应用服务
/// </summary>
public class UserLoginRecordApp
{
    private readonly UserLoginRecordService _userLoginRecordService;
    private readonly ILogger<UserLoginRecordApp> _logger;
    private readonly CacheHelper _cacheHelper;
    private readonly IContextUser _contextUser;

    public UserLoginRecordApp(
        UserLoginRecordService userLoginRecordService,
        ILogger<UserLoginRecordApp> logger,
        CacheHelper cacheHelper,
        IContextUser contextUser)
    {
        _userLoginRecordService = userLoginRecordService;
        _logger = logger;
        _cacheHelper = cacheHelper;
        _contextUser = contextUser;
    }

    /// <summary>
    /// 获取登录记录列表 - 根据当前用户权限过滤
    /// </summary>
    /// <param name="req">查询请求</param>
    /// <returns>登录记录列表</returns>
    public async Task<ListRes<UserLoginRecord>> GetPageListAsync(PageListReq<UserLoginRecordPageListReq> req)
    {
        if (req.IsSortEmpty)
            req.Sorts = [new PageSort(nameof(UserLoginRecord.RecordTime), true)];

        // 租户权限控制：租户只能查看自己租户的用户登录记录
        if (_contextUser.IsTenant())
        {
            req.Filters.TenantId = _contextUser.TenantId;
        }

        var res = await _userLoginRecordService.GetPageListAsync(req);
        return res;
    }

    /// <summary>
    /// 根据ID获取登录记录详情
    /// </summary>
    /// <param name="id">登录记录ID</param>
    /// <returns>登录记录详情</returns>
    public async Task<UserLoginRecord> GetByIdAsync(int id)
    {
        if (id <= 0)
            throw new ApiException("登录记录ID无效");

        var record = await _userLoginRecordService.GetByIdAsync(id);
        if (record == null)
            throw new ApiException("登录记录不存在");

        // 租户权限检查：租户只能查看自己租户的登录记录
        if (_contextUser.IsTenant() && record.TenantId != _contextUser.TenantId)
            throw new ApiException("无权查看此登录记录");

        return record;
    }

    /// <summary>
    /// 创建登录记录
    /// </summary>
    /// <param name="req">创建请求</param>
    /// <returns>创建的登录记录</returns>
    public async Task<UserLoginRecord> CreateAsync(CreateUserLoginRecordReq req)
    {
        var record = req.Adapt<UserLoginRecord>();
        record.RecordTime = DateTime.Now;

        var created = await _userLoginRecordService.CreateAsync(record);

        _logger.LogInformation("创建登录记录成功，用户ID: {UserId}，登录IP: {LoginIp}",
            created.UserId, created.LoginIp);

        return created;
    }

    /// <summary>
    /// 记录用户登录
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="tenantId">租户ID</param>
    /// <param name="loginIp">登录IP</param>
    /// <param name="description">描述信息</param>
    /// <returns>登录记录</returns>
    public async Task<UserLoginRecord> RecordLoginAsync(int userId, int tenantId, string loginIp, string description = "")
    {
        var record = new UserLoginRecord
        {
            UserId = userId,
            TenantId = tenantId,
            LoginIp = loginIp,
            Description = description,
            RecordTime = DateTime.Now
        };

        var created = await _userLoginRecordService.CreateAsync(record);

        _logger.LogInformation("记录用户登录，用户ID: {UserId}，租户ID: {TenantId}，登录IP: {LoginIp}",
            userId, tenantId, loginIp);

        return created;
    }

    /// <summary>
    /// 获取用户最近的登录记录
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="count">获取数量</param>
    /// <returns>登录记录列表</returns>
    public async Task<List<UserLoginRecord>> GetRecentLoginsByUserIdAsync(int userId, int count = 10)
    {
        var records = await _userLoginRecordService.GetRecentLoginsByUserIdAsync(userId, count);

        // 租户权限检查：租户只能查看自己租户的用户登录记录
        if (_contextUser.IsTenant())
        {
            records = records.Where(r => r.TenantId == _contextUser.TenantId).ToList();
        }

        return records;
    }

    /// <summary>
    /// 清除指定天数前的用户登录记录
    /// </summary>
    /// <param name="days">保留天数</param>
    /// <returns>清除的记录数</returns>
    public async Task<int> ClearOldRecordsAsync(int days = 30)
    {
        var count = await _userLoginRecordService.ClearOldRecordsAsync(days);
        _logger.LogInformation("清除 {Days} 天前的登录记录，共清除 {Count} 条记录", days, count);
        return count;
    }

    /// <summary>
    /// 清除30天前的用户登录记录 - 定时任务使用
    /// </summary>
    public async Task ClearUserLoginRecordAsync()
    {
        await ClearOldRecordsAsync(30);
        _logger.LogInformation("定时清理登录记录任务执行完成");
    }
}