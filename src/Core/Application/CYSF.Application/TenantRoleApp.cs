using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CYSF.Core.Exceptions;
using CYSF.Services;
using Microsoft.Extensions.Logging;
using SqlSugar;
using Mapster;
using CYSF.Models.Entities;
using CYSF.Models.Request.Role;
using CYSF.Models.Response;
using CYSF.Core.Cache;
using CYSF.Models.Const;
using CYSF.Core.HttpContextUser;
using CYSF.Models.Request;

namespace CYSF.Application;

/// <summary>
/// 租户角色应用服务
/// </summary>
public class TenantRoleApp
{
    private readonly RoleService _roleService;
    private readonly TenantUserService _tenantUserService;
    private readonly ILogger<TenantRoleApp> _logger;
    private readonly CacheHelper _cacheHelper;
    private readonly IContextUser _contextUser;

    public TenantRoleApp(
        RoleService roleService,
        TenantUserService tenantUserService,
        ILogger<TenantRoleApp> logger,
        SqlSugarScope db,
        <PERSON><PERSON><PERSON><PERSON><PERSON> cacheHelper,
        IContextUser contextUser)
    {
        _roleService = roleService;
        _tenantUserService = tenantUserService;
        _logger = logger;
        _cacheHelper = cacheHelper;
        _contextUser = contextUser;
    }

    /// <summary>
    /// 根据ID获取角色（带缓存）
    /// </summary>
    /// <param name="roleId">角色ID</param>
    /// <returns>角色实体</returns>
    public async Task<Role> Get(int roleId)
    {
        return await _cacheHelper.CacheShellAsync(
            CacheKeys.Role(roleId),
            CacheKeys.DayExpired,
            () => _roleService.GetByIdAsync(roleId)
        );
    }

    /// <summary>
    /// 获取角色列表 - 根据当前用户权限过滤
    /// </summary>
    /// <param name="req">查询请求</param>
    /// <returns>角色列表</returns>
    public async Task<ListRes<Role>> GetPageListAsync(PageListReq<RolePageListReq> req)
    {
        if (req.IsSortEmpty)
            req.Sorts = [new PageSort(nameof(Role.UpdateTime), true)];

        // 租户权限控制：租户只能查看自己的角色
        if (_contextUser.IsTenant())
        {
            // 为查询条件添加租户ID过滤
            req.Filters.TenantId = _contextUser.TenantId;
        }

        var res = await _roleService.GetPageListAsync(req);
        return res;
    }

    /// <summary>
    /// 根据ID获取角色详情
    /// </summary>
    /// <param name="id">角色ID</param>
    /// <returns>角色详情</returns>
    public async Task<Role> GetByIdAsync(int id)
    {
        if (id <= 0)
            throw new ApiException("角色ID无效");

        var role = await _roleService.GetByIdAsync(id);
        if (role == null)
            throw new ApiException("角色不存在");

        // 租户权限检查：租户只能查看自己的角色
        if (_contextUser.IsTenant() && role.TenantId != _contextUser.TenantId)
            throw new ApiException("无权查看此角色");

        return role;
    }

    /// <summary>
    /// 获取所有角色列表（不分页）- 根据当前用户权限过滤
    /// </summary>
    /// <returns>角色列表</returns>
    public async Task<List<Role>> GetAllAsync()
    {
        List<Role> roles;

        if (_contextUser.IsTenant())
        {
            // 租户只能获取自己的角色
            roles = await _roleService.GetByTenantIdAsync(_contextUser.TenantId);
        }
        else
        {
            // 平台管理员可以获取所有角色
            roles = await _roleService.GetListAsync();
        }

        return roles;
    }

    /// <summary>
    /// 创建角色
    /// </summary>
    /// <param name="req">创建请求</param>
    /// <returns>创建的角色</returns>
    public async Task<Role> CreateAsync(CreateRoleReq req)
    {
        // 确定租户ID：租户用户只能为自己创建角色，平台管理员可以为指定租户创建角色
        var tenantId = _contextUser.IsTenant() ? _contextUser.TenantId : req.TenantId;

        // 检查角色名称是否已存在（在当前租户范围内）
        if (await _roleService.ExistsByNameAsync(req.Name, tenantId))
            throw new ApiException($"角色名称 {req.Name} 已存在");

        // 使用Mapster映射
        var role = req.Adapt<Role>();
        role.TenantId = tenantId;
        role.CreateTime = DateTime.Now;
        role.CreatorId = _contextUser.UserId;

        var createdRole = await _roleService.CreateAsync(role);

        _logger.LogInformation("创建角色成功，角色ID: {RoleId}，角色名称: {RoleName}，租户ID: {TenantId}",
            createdRole.Id, createdRole.Name, createdRole.TenantId);

        return createdRole;
    }

    /// <summary>
    /// 更新角色
    /// </summary>
    /// <param name="req">更新请求</param>
    /// <returns>更新结果</returns>
    public async Task<bool> UpdateAsync(UpdateRoleReq req)
    {
        // 检查角色是否存在
        var existingRole = await _roleService.GetByIdAsync(req.Id);
        if (existingRole == null)
            throw new ApiException("角色不存在");

        if (_contextUser.IsTenant() && existingRole.TenantId != _contextUser.TenantId)
            throw new ApiException("无权更改");

        // 检查角色名称是否被其他角色使用
        if (await _roleService.ExistsByNameAsync(req.Name, req.Id))
            throw new ApiException($"角色名称 {req.Name} 已被其他角色使用");

        // 使用Mapster映射更新字段
        req.Adapt(existingRole);
        existingRole.UpdateTime = DateTime.Now;
        existingRole.UpdatorId = _contextUser.UserId;

        var result = await _roleService.UpdateAsync(existingRole);

        // 清除缓存
        if (result)
            await _cacheHelper.RemoveAsync(CacheKeys.Role(req.Id));

        return result;
    }
}
