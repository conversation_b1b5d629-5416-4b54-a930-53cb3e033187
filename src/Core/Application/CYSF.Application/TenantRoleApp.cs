using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Mapster;
using CYSF.Services;
using CYSF.Models.Request.TenantRole;
using CYSF.Models.Response;
using CYSF.Core.Cache;
using CYSF.Models.Entities;
using CYSF.Core.Exceptions;
using CYSF.Core.Models;
using CYSF.Models.Const;
using CYSF.Models.Request;
using CYSF.Core.Helpers;
using CYSF.Core.Extensions;
using CYSF.Core.HttpContextUser;

namespace CYSF.Application;

/// <summary>
/// TenantRole应用服务
/// </summary>
public class TenantRoleApp
{
    private readonly TenantRoleService _tenantRoleService;
    private readonly ILogger<TenantRoleApp> _logger;
    private readonly IContextUser _contextUser;
    private readonly CacheHelper _cacheHelper;

    public TenantRoleApp(
        TenantRoleService tenantRoleService,
        ILogger<TenantRoleApp> logger,
        IContextUser contextUser,
        CacheHelper cacheHelper)
    {
        _tenantRoleService = tenantRoleService;
        _logger = logger;
        _contextUser = contextUser;
        _cacheHelper = cacheHelper;
    }

    /// <summary>
    /// 根据ID获取TenantRole
    /// </summary>
    /// <param name="id">TenantRoleID</param>
    /// <returns>TenantRole实体</returns>
    public async Task<TenantRole> GetByIdAsync(int id)
    {
        if (id <= 0)
            throw new ApiException("TenantRoleID无效");

        var entity = await _tenantRoleService.GetByIdAsync(id);
        if (entity == null)
            throw new ApiException("TenantRole不存在");

        return entity;
    }

    /// <summary>
    /// 获取TenantRole列表
    /// </summary>
    /// <param name="req">查询请求</param>
    /// <returns>TenantRole列表</returns>
    public async Task<ListRes<TenantRoleDto>> GetPageListAsync(PageListReq<TenantRolePageListReq> req)
    {
        if (req.IsSortEmpty)
            req.Sorts = [new PageSort(nameof(TenantRole.UpdateTime), true)];

        var res = await _tenantRoleService.GetPageListAsync(req);
        return res;
    }

    /// <summary>
    /// 创建TenantRole
    /// </summary>
    /// <param name="entity">TenantRole实体</param>
    /// <returns>创建的TenantRole</returns>
    public async Task<TenantRole> CreateAsync(TenantRole entity)
    {
        entity.CreateTime = DateTime.Now;
        entity.CreatorId = _contextUser.UserId;

        var createdTenantRole = await _tenantRoleService.CreateAsync(entity);

        return createdTenantRole;
    }

    /// <summary>
    /// 更新TenantRole
    /// </summary>
    /// <param name="entity">TenantRole实体</param>
    /// <returns>更新结果</returns>
    public async Task<bool> UpdateAsync(TenantRole entity)
    {
        // 检查TenantRole是否存在
        var existingTenantRole = await _tenantRoleService.GetByIdAsync(entity.Id);
        if (existingTenantRole == null)
            throw new ApiException("TenantRole不存在");

        entity.UpdateTime = DateTime.Now;

        var result = await _tenantRoleService.UpdateAsync(entity);

        return result;
    }

    /// <summary>
    /// 删除TenantRole
    /// </summary>
    /// <param name="id">TenantRoleID</param>
    /// <returns>删除结果</returns>
    public async Task<bool> DeleteAsync(int id)
    {
        if (id <= 0)
            throw new ApiException("TenantRoleID无效");

        // 检查TenantRole是否存在
        var entity = await _tenantRoleService.GetByIdAsync(id);
        if (entity == null)
            throw new ApiException("TenantRole不存在");

        var result = await _tenantRoleService.DeleteAsync(id);

        return result;
    }


}
