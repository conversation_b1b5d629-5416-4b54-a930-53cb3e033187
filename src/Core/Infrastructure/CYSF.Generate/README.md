# CYSF.Generate 开发工具集

## 📋 概述

CYSF.Generate 是一个强大的开发工具集，支持两种主要模式：

1. **Code First** - 根据实体类创建数据库表和初始化数据
2. **Database First** - 根据数据库表生成代码文件

## 🚀 功能特性

### Code First 模式
- ✅ 根据 `CYSF.Models.Entities` 实体类自动创建数据库表
- ✅ 智能处理表之间的依赖关系
- ✅ 初始化系统必需的基础数据
- ✅ 验证表完整性和数据库状态

### Database First 模式
- ✅ 根据数据库表生成实体类
- ✅ 生成仓储层代码
- ✅ 生成服务层代码
- ✅ 生成控制器代码（可选）

### 数据库管理
- ✅ 数据库连接状态检查
- ✅ 数据库表信息查看
- ✅ 实体表映射关系分析
- ✅ 详细的数据库状态报告

## 🛠️ 使用方法

### 1. 配置数据库连接

编辑 `appsettings.json` 文件：

```json
{
  "Database": {
    "ConnectionString": "Data Source=.;Initial Catalog=CYSF;Integrated Security=True;TrustServerCertificate=True"
  }
}
```

### 2. 运行工具

```bash
# 进入项目目录
cd src/Core/Infrastructure/CYSF.Generate

# 运行工具
dotnet run
```

### 3. 交互式菜单

启动后会显示主菜单：

```
╔══════════════════════════════════════════════════════════════╗
║                    CYSF 开发工具集                          ║
║                                                              ║
║  🔧 Code First   - 根据实体类创建数据库表和初始化数据        ║
║  📊 Database First - 根据数据库表生成代码文件               ║
║  🗄️ 数据库管理   - 数据库初始化和状态检查                   ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝

┌─────────────────────────────────────────────────────────────┐
│                        主菜单                              │
├─────────────────────────────────────────────────────────────┤
│  1. 🔧 Code First      - 根据实体类创建表和初始化数据      │
│  2. 📊 Database First  - 根据数据库表生成代码文件          │
│  3. 🗄️ 数据库管理      - 数据库状态检查和管理              │
│  0. 🚪 退出程序                                            │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 Code First 使用指南

### 🚀 双向开发工作流

CYSF.Generate现在支持完整的双向开发工作流：

#### 📝 Entity → Database → Code 工作流
1. **在CYSF.Models/Entities中创建Entity** - 包含完整SqlSugar特性
2. **使用Code First创建表** - 根据Entity创建数据库表
3. **可选：同时生成代码** - 生成Service、Repository、App、Controller

#### 🗄️ Database → Entity → Code 工作流
1. **在数据库中创建表** - 直接在数据库中创建表结构
2. **使用Database First生成Entity** - 生成包含完整SqlSugar特性的Entity
3. **可选：同时生成代码** - 生成Service、Repository、App、Controller

### 操作选项

1. **🔨 创建数据库表** - 根据实体类创建表结构
2. **🔄 更新表结构** - 根据实体类更新现有表结构（支持字段修改）
3. **🌱 初始化基础数据** - 创建默认租户、用户等基础数据
4. **🚀 创建表+初始化** - 一键完成表创建和数据初始化
5. **🎯 创建表+生成代码** - 创建表并生成完整代码
6. **✅ 验证表完整性** - 检查所有实体对应的表是否存在

### 支持的实体类

自动扫描 `CYSF.Models.Entities` 命名空间下的所有实体类，包括：

- `Tenant` → `t_tenant` (租户表)
- `TenantUser` → `t_user` (用户表)
- `TenantRole` → `t_role` (角色表)
- `Dic` → `t_dic` (字典表)
- `DicType` → `t_dic_type` (字典类型表)
- `UserTenantRela` → `r_user_tenant` (用户租户关联表)
- `UserLoginRecord` → `rec_user_login` (用户登录记录表)
- **以及您自定义的任何Entity类**

### 🎯 完整使用示例

#### 示例1：新增Entity并创建表
```csharp
// 1. 在 CYSF.Models/Entities 中创建新的Entity
[SugarTable("t_product", TableDescription = "产品表")]
public class Product
{
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
    public int Id { get; set; }

    [SugarColumn(Length = 50, IsNullable = false, ColumnDescription = "产品编码")]
    public string Code { get; set; } = string.Empty;

    [SugarColumn(Length = 100, IsNullable = false, ColumnDescription = "产品名称")]
    public string Name { get; set; } = string.Empty;

    [SugarColumn(DecimalDigits = 18, Scale = 2, DefaultValue = "0", ColumnDescription = "价格")]
    public decimal Price { get; set; }
}

// 2. 运行 CYSF.Generate
// 3. 选择 "1. Code First" → "2. 更新表结构" 或 "5. 创建表+生成代码"
```

#### 示例2：从数据库表生成Entity
```sql
-- 1. 在数据库中创建表
CREATE TABLE t_order (
    Id int IDENTITY(1,1) PRIMARY KEY,
    OrderNo nvarchar(50) NOT NULL COMMENT '订单号',
    Amount decimal(18,2) DEFAULT 0 COMMENT '金额',
    CreateTime datetime DEFAULT GETDATE() COMMENT '创建时间'
);

-- 2. 运行 CYSF.Generate
-- 3. 选择 "2. Database First" → "2. 仅生成实体类"
-- 4. 生成的Entity将包含完整的SqlSugar特性
```

### 初始化的基础数据

- **默认租户**: 代码为 `DEFAULT`
- **字典类型**: 用户状态、租户等级、系统配置
- **字典数据**:
  - 用户状态：正常、禁用、锁定
  - 租户等级：标准版、专业版、企业版
- **默认角色**: 系统管理员
- **默认用户**:
  - 用户名：`admin`
  - 密码：`123456`
  - 角色：系统管理员

## 📊 Database First 使用指南

### 🔥 增强特性

Database First现在生成**包含完整SqlSugar特性的Entity**，支持：

- ✅ **字段长度** (`Length = 50`)
- ✅ **字段精度** (`DecimalDigits = 18, Scale = 2`)
- ✅ **列注释** (`ColumnDescription = "用户名"`)
- ✅ **可空性** (`IsNullable = false`)
- ✅ **默认值** (`DefaultValue = "0"`)
- ✅ **主键标识** (`IsPrimaryKey = true`)
- ✅ **自增标识** (`IsIdentity = true`)

### 操作选项

1. **🔄 生成所有代码** - 生成实体、仓储、服务、控制器等所有代码
2. **📝 仅生成实体类** - 生成包含完整SqlSugar特性的实体类
3. **🗃️ 仅生成仓储类** - 生成数据访问层代码
4. **⚙️ 仅生成服务类** - 生成业务逻辑层代码
5. **🎮 仅生成控制器** - 生成API控制器代码

### 生成的文件结构

```
src/Core/
├── Model/CYSF.Models/
│   └── Entities/          # 实体类
├── Repository/CYSF.Repository/
│   ├── Interface/         # 仓储接口
│   └── Repository/        # 仓储实现
├── Service/CYSF.Service/
│   ├── Interface/         # 服务接口
│   └── Service/           # 服务实现
└── Api/Api.Backend/
    └── Controllers/       # API控制器
```

## 🗄️ 数据库管理功能

### 可用操作

1. **检查数据库连接** - 测试数据库连接状态
2. **显示数据库状态** - 查看数据库整体状态
3. **显示数据库表** - 列出所有数据库表
4. **显示实体映射** - 查看实体类与表的映射关系
5. **生成详细报告** - 生成完整的状态报告

### 状态报告示例

```
📊 数据库状态信息:
   🗄️ 数据库名称: CYSF
   📋 数据库表数: 7
   📝 实体类数: 7
   ✅ 已映射表: 7
   ❌ 缺失表: 0
   📈 完整度: 100.0%
```

## ⚙️ 配置说明

### appsettings.json

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning"
    }
  },
  "Database": {
    "ConnectionString": "Data Source=.;Initial Catalog=CYSF;Integrated Security=True;TrustServerCertificate=True"
  },
  "CodeGeneration": {
    "SolutionName": "CYSF",
    "GenerateControllers": false,
    "GenerateApplications": false,
    "OutputPath": "Generated"
  }
}
```

## 🚨 注意事项

### 安全提醒

1. **默认密码**: 默认管理员密码为 `123456`，生产环境请及时修改
2. **数据库权限**: 确保数据库用户有创建表的权限
3. **备份数据**: 执行操作前建议备份重要数据

### 环境要求

1. **.NET 8.0** 或更高版本
2. **SQL Server** 数据库
3. **SqlSugar** ORM 支持

### 常见问题

1. **连接失败**: 检查连接字符串和数据库服务状态
2. **权限不足**: 确保数据库用户有足够权限
3. **表已存在**: Code First 会跳过已存在的表

## 🔄 开发指南

### 添加新实体类

1. 在 `CYSF.Models.Entities` 中创建实体类
2. 配置 SqlSugar 特性标注
3. 运行 Code First 自动建表

### 自定义生成模板

1. 修改 `Template` 目录下的模板文件
2. 重新运行 Database First 生成代码

### 扩展功能

1. 在 `Services` 目录下添加新服务
2. 在 `Program.cs` 中注册服务
3. 在菜单中添加新选项

## 📚 相关文档

- [CYSF框架文档](../../../README.md)
- [SqlSugar文档](https://www.donet5.com/Home/Doc)
- [实体类设计规范](../../Model/CYSF.Models/README.md)

## 🆘 技术支持

如果遇到问题，请：

1. 检查日志输出
2. 验证配置文件
3. 确认数据库连接
4. 查看相关文档

---

**版本**: 1.0.0  
**更新时间**: 2024-06-28  
**维护者**: CYSF开发团队
