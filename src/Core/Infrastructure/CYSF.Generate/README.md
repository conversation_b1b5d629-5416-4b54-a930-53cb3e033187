# CYSF 开发工具集

CYSF.Generate 是一个强大的代码生成和数据库管理工具，专注于 Code First 开发模式，提供完整的实体驱动开发体验。

## 🚀 功能特性

### ✨ 核心功能
- **🔨 Code First**: 从实体类生成数据库表结构，支持表结构更新
- **📝 代码生成**: 从实体类生成 Service、Repository、Application、Controller 等多层代码
- **🌱 数据初始化**: 模块化的种子数据管理，支持租户、用户、角色等基础数据
- **🔍 孤儿表检测**: 自动检测和清理数据库中的孤儿表
- **📖 XML文档集成**: 读取实体注释生成完整的数据库字段描述

### 🛠️ 技术特性
- **配置驱动**: 静态配置基于 appsettings.json，动态配置自动保存用户偏好
- **反射机制**: 自动发现和处理实体类，无需手动维护实体列表
- **依赖排序**: 支持配置实体创建的依赖顺序
- **用户偏好**: 自动记住用户最后一次的选择配置
- **交互式菜单**: 友好的控制台交互界面
- **完善日志**: 详细的操作日志和错误处理

## 📋 系统要求

- **.NET 8.0** 或更高版本
- **SQL Server** 数据库
- **Visual Studio 2022** 或 **VS Code**（推荐）

## 🔧 安装配置

### 1. 克隆项目
```bash
git clone <repository-url>
cd CYSF
```

### 2. 配置数据库连接
编辑 `src/Core/Infrastructure/CYSF.Generate/appsettings.json`：

```json
{
  "Database": {
    "ConnectionString": "Data Source=.;Initial Catalog=CYSF;Integrated Security=True;TrustServerCertificate=True"
  },
  "CodeGeneration": {
    "SolutionName": "CYSF",
    "EntityNamespace": "CYSF.Models.Entities",
    "GenerateService": true,
    "GenerateRepository": true,
    "GenerateApplication": true,
    "GenerateController": true
  },
  "EntityDependency": {
    "DependencyOrder": [
      "Tenant",
      "DicType", 
      "Dic",
      "TenantRole",
      "TenantUser",
      "UserTenantRela",
      "UserLoginRecord"
    ]
  }
}
```

### 3. 编译项目
```bash
# 首先编译 CYSF.Models 项目以生成 XML 文档
cd src/Core/Model/CYSF.Models
dotnet build

# 然后编译 CYSF.Generate 项目
cd ../../../Infrastructure/CYSF.Generate
dotnet build
```

### 4. 运行工具
```bash
dotnet run
```

## 🔧 Code First 使用指南

### 🚀 完整开发工作流

#### 📝 创建新实体的完整流程
1. **在CYSF.Models/Entities中创建Entity**
2. **添加完整的SqlSugar特性**
3. **编译CYSF.Models项目**
4. **运行CYSF.Generate**
5. **选择相应操作**

### 操作选项

1. **🔨 创建数据库表** - 根据实体类创建表结构
2. **🔄 更新表结构** - 根据实体类更新现有表结构（支持字段修改）
3. **🌱 初始化基础数据** - 创建默认租户、用户等基础数据
4. **🚀 创建表+初始化** - 一键完成表创建和数据初始化
5. **🎯 创建表+生成代码** - 创建表并生成完整代码
6. **✅ 验证表完整性** - 检查所有实体对应的表是否存在

### 支持的实体类

自动扫描 `CYSF.Models.Entities` 命名空间下的所有实体类，包括：

- `Tenant` → `t_tenant` (租户表)
- `TenantUser` → `t_user` (用户表)  
- `TenantRole` → `t_role` (角色表)
- `Dic` → `t_dic` (字典表)
- `DicType` → `t_dic_type` (字典类型表)
- `UserTenantRela` → `r_user_tenant` (用户租户关联表)
- `UserLoginRecord` → `rec_user_login` (用户登录记录表)
- **以及您自定义的任何Entity类**

## 🎯 完整使用示例

### 示例1：新增Entity并创建表
```csharp
// 1. 在 CYSF.Models/Entities 中创建新的Entity
[SugarTable("t_product", TableDescription = "产品表")]
[SugarIndex("IX_Product_Code", nameof(Code), OrderByType.Asc, true)]
public class Product
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true, ColumnDescription = "主键ID")]
    public int Id { get; set; }

    /// <summary>
    /// 产品编码，全局唯一
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = false, ColumnDescription = "产品编码，全局唯一")]
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// 产品名称
    /// </summary>
    [SugarColumn(Length = 100, IsNullable = false, ColumnDescription = "产品名称")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 产品价格
    /// </summary>
    [SugarColumn(DecimalDigits = 18, Scale = 2, DefaultValue = "0", ColumnDescription = "产品价格")]
    public decimal Price { get; set; }
}

// 2. 编译 CYSF.Models 项目
// 3. 运行 CYSF.Generate
// 4. 选择 "1. Code First" → "2. 更新表结构" 或 "5. 创建表+生成代码"
```

### 示例2：SqlSugar特性完整示例
```csharp
/// <summary>
/// 订单表
/// </summary>
[SugarTable("t_order", TableDescription = "订单表")]
[SugarIndex("IX_Order_OrderNo", nameof(OrderNo), OrderByType.Asc, true)]
[SugarIndex("IX_Order_CreateTime", nameof(CreateTime), OrderByType.Desc)]
public class Order
{
    /// <summary>
    /// 主键ID
    /// </summary>
    [SugarColumn(IsPrimaryKey = true, IsIdentity = true, ColumnDescription = "主键ID")]
    public int Id { get; set; }

    /// <summary>
    /// 订单号，全局唯一
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = false, ColumnDescription = "订单号，全局唯一")]
    public string OrderNo { get; set; } = string.Empty;

    /// <summary>
    /// 订单金额
    /// </summary>
    [SugarColumn(DecimalDigits = 18, Scale = 2, DefaultValue = "0", ColumnDescription = "订单金额")]
    public decimal Amount { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    [SugarColumn(IsNullable = false, DefaultValue = "1", ColumnDescription = "订单状态")]
    public OrderStatus Status { get; set; } = OrderStatus.Pending;

    /// <summary>
    /// 创建时间
    /// </summary>
    [SugarColumn(IsNullable = false, ColumnDescription = "创建时间")]
    public DateTime CreateTime { get; set; } = DateTime.Now;
}
```

## 📊 代码生成功能

### 生成的代码结构
- **Service层**: 业务逻辑处理
- **Repository层**: 数据访问层
- **Application层**: 应用服务层
- **Controller层**: API控制器

### 配置代码生成
```json
{
  "CodeGeneration": {
    "GenerateService": true,
    "GenerateRepository": true,
    "GenerateApplication": true,
    "GenerateController": true,
    "ServiceOutputPath": "../../../Core/Service/CYSF.Service",
    "RepositoryOutputPath": "../../../Core/Repository/CYSF.Repository",
    "ApplicationOutputPath": "../../../Core/Application/CYSF.Application",
    "ControllerOutputPath": "../../../Backend/Api.Backend/Controllers"
  }
}
```

## 🌱 种子数据管理

### 种子数据结构
```
InitializationData/
├── Interfaces/
│   └── IDataSeed.cs         # 种子数据接口
├── Seeds/                   # 具体种子数据
│   ├── TenantSeeds.cs       # 租户数据
│   ├── DictionarySeeds.cs   # 字典数据
│   ├── RoleSeeds.cs         # 角色数据
│   └── UserSeeds.cs         # 用户数据
└── SeedManager.cs           # 种子数据管理器
```

### 添加自定义种子数据
```csharp
public class CustomSeeds : IDataSeed
{
    public int Order => 10;  // 执行顺序
    public string Name => "自定义数据";
    public string Description => "创建自定义初始化数据";

    public async Task<bool> SeedAsync(ISqlSugarClient db)
    {
        // 实现种子数据逻辑
        return true;
    }
}
```

## 🔍 孤儿表管理

### 配置孤儿表处理
```json
{
  "TableManagement": {
    "AutoDeleteOrphanTables": false,
    "ConfirmBeforeDelete": true,
    "BackupBeforeDelete": true
  }
}
```

### 孤儿表检测流程
1. **扫描数据库表**
2. **对比实体类**
3. **识别孤儿表**
4. **用户确认**
5. **备份并删除**

## 📖 XML文档集成

### 配置XML文档路径
```json
{
  "CodeGeneration": {
    "EntityXmlDocPath": "../../../Core/Model/CYSF.Models/bin/Debug/net8.0/CYSF.Models.xml"
  }
}
```

### 确保XML文档生成
在 `CYSF.Models.csproj` 中：
```xml
<PropertyGroup>
  <DocumentationFile>CYSF.Models.xml</DocumentationFile>
</PropertyGroup>
```

## 🚀 最佳实践

### 1. Entity设计原则
- 使用完整的SqlSugar特性标注
- 添加详细的XML注释
- 合理设计索引和约束
- 遵循命名规范

### 2. 开发流程
1. 设计Entity类（包含完整特性）
2. 编译CYSF.Models项目
3. 使用Code First创建/更新数据库表
4. 生成相应的业务代码
5. 测试和验证

### 3. 配置管理
- 根据环境调整配置
- 合理设置依赖顺序
- 定期检查孤儿表
- 保持XML文档同步

## 🎉 总结

CYSF.Generate 提供了完整的 Code First 开发体验：

1. **实体驱动** - 以Entity为中心的开发模式
2. **配置灵活** - 完全可配置的代码生成
3. **自动化** - 减少手动维护工作
4. **可扩展** - 支持自定义种子数据和配置
5. **安全可靠** - 完善的错误处理和备份机制

通过这套工具，开发者可以专注于业务逻辑的实现，而将重复性的基础设施代码交给工具自动生成。
