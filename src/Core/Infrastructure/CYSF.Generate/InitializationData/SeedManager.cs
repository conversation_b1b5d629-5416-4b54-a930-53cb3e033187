using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CYSF.Generate.InitializationData.Interfaces;
using CYSF.Generate.InitializationData.Seeds;
using SqlSugar;

namespace CYSF.Generate.InitializationData
{
    /// <summary>
    /// 种子数据管理器
    /// </summary>
    public class SeedManager
    {
        private readonly List<IDataSeed> _seeds;

        public SeedManager()
        {
            // 注册所有种子数据类
            _seeds = new List<IDataSeed>
            {
                new TenantSeeds(),
                new DictionarySeeds(),
                new RoleSeeds(),
                new UserSeeds()
            };

            // 按执行顺序排序
            _seeds = _seeds.OrderBy(s => s.Order).ToList();
        }

        /// <summary>
        /// 执行所有种子数据初始化
        /// </summary>
        /// <param name="db">数据库上下文</param>
        /// <returns>是否全部成功</returns>
        public async Task<bool> SeedAllAsync(ISqlSugarClient db)
        {
            try
            {
                Console.WriteLine("🌱 开始执行种子数据初始化...");
                Console.WriteLine();

                var allSuccess = true;
                var successCount = 0;
                var failureCount = 0;

                foreach (var seed in _seeds)
                {
                    Console.WriteLine($"📦 执行种子数据: {seed.Name} ({seed.Description})");
                    
                    var success = await seed.SeedAsync(db);
                    if (success)
                    {
                        successCount++;
                        Console.WriteLine($"   ✅ {seed.Name} 执行成功");
                    }
                    else
                    {
                        failureCount++;
                        allSuccess = false;
                        Console.WriteLine($"   ❌ {seed.Name} 执行失败");
                    }
                    
                    Console.WriteLine();
                }

                // 输出总结
                Console.WriteLine("📊 种子数据执行总结:");
                Console.WriteLine($"   ✅ 成功: {successCount}");
                Console.WriteLine($"   ❌ 失败: {failureCount}");
                Console.WriteLine($"   📈 总计: {_seeds.Count}");

                return allSuccess;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 种子数据管理器执行失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取所有种子数据信息
        /// </summary>
        /// <returns>种子数据列表</returns>
        public List<(int Order, string Name, string Description)> GetSeedInfo()
        {
            return _seeds.Select(s => (s.Order, s.Name, s.Description)).ToList();
        }

        /// <summary>
        /// 执行指定的种子数据
        /// </summary>
        /// <param name="db">数据库上下文</param>
        /// <param name="seedName">种子数据名称</param>
        /// <returns>是否成功</returns>
        public async Task<bool> SeedSpecificAsync(ISqlSugarClient db, string seedName)
        {
            try
            {
                var seed = _seeds.FirstOrDefault(s => s.Name == seedName);
                if (seed == null)
                {
                    Console.WriteLine($"❌ 未找到种子数据: {seedName}");
                    return false;
                }

                Console.WriteLine($"📦 执行指定种子数据: {seed.Name}");
                var success = await seed.SeedAsync(db);
                
                if (success)
                {
                    Console.WriteLine($"✅ {seed.Name} 执行成功");
                }
                else
                {
                    Console.WriteLine($"❌ {seed.Name} 执行失败");
                }

                return success;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 执行指定种子数据失败: {ex.Message}");
                return false;
            }
        }
    }
}
