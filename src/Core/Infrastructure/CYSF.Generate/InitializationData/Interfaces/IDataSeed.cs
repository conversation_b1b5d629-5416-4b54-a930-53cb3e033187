using SqlSugar;
using System.Threading.Tasks;

namespace CYSF.Generate.InitializationData.Interfaces
{
    /// <summary>
    /// 数据种子接口
    /// </summary>
    public interface IDataSeed
    {
        /// <summary>
        /// 种子数据的执行顺序（数字越小越先执行）
        /// </summary>
        int Order { get; }

        /// <summary>
        /// 种子数据的名称
        /// </summary>
        string Name { get; }

        /// <summary>
        /// 种子数据的描述
        /// </summary>
        string Description { get; }

        /// <summary>
        /// 执行种子数据初始化
        /// </summary>
        /// <param name="db">数据库上下文</param>
        /// <returns>是否成功</returns>
        Task<bool> SeedAsync(ISqlSugarClient db);
    }
}
