using System;
using System.Threading.Tasks;
using CYSF.Generate.InitializationData.Interfaces;
using CYSF.Models.Entities;
using SqlSugar;

namespace CYSF.Generate.InitializationData.Seeds
{
    /// <summary>
    /// 字典种子数据
    /// </summary>
    public class DictionarySeeds : IDataSeed
    {
        public int Order => 2;
        public string Name => "字典数据";
        public string Description => "创建基础字典类型和字典数据";

        public async Task<bool> SeedAsync(ISqlSugarClient db)
        {
            try
            {
                Console.WriteLine("📚 创建基础字典类型和数据...");

                // 创建字典类型
                var success = await CreateDictionaryTypesAsync(db);
                if (!success) return false;

                // 创建字典数据
                success = await CreateDictionaryDataAsync(db);
                return success;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 创建字典数据失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 创建字典类型
        /// </summary>
        private async Task<bool> CreateDictionaryTypesAsync(ISqlSugarClient db)
        {
            try
            {
                var dicTypes = new[]
                {
                    new DicType { Name = "用户状态" },
                    new DicType { Name = "租户等级" },
                    new DicType { Name = "系统配置" }
                };

                foreach (var dicType in dicTypes)
                {
                    var existing = await db.Queryable<DicType>()
                        .Where(x => x.Name == dicType.Name)
                        .FirstAsync();

                    if (existing == null)
                    {
                        await db.Insertable(dicType).ExecuteReturnIdentityAsync();
                        Console.WriteLine($"   ✅ 字典类型创建: {dicType.Name}");
                    }
                    else
                    {
                        Console.WriteLine($"   ✅ 字典类型已存在: {dicType.Name}");
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 创建字典类型失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 创建字典数据
        /// </summary>
        private async Task<bool> CreateDictionaryDataAsync(ISqlSugarClient db)
        {
            try
            {
                var createdCount = 0;

                // 用户状态字典
                await CreateUserStatusDictionaries(db, ref createdCount);

                // 租户等级字典
                await CreateTenantLevelDictionaries(db, ref createdCount);

                Console.WriteLine($"   ✅ 字典数据创建完成，新增 {createdCount} 条");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 创建字典数据失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 创建用户状态字典
        /// </summary>
        private async Task CreateUserStatusDictionaries(ISqlSugarClient db, ref int createdCount)
        {
            var userStatusType = await db.Queryable<DicType>()
                .Where(x => x.Name == "用户状态")
                .FirstAsync();

            if (userStatusType != null)
            {
                var userStatusDics = new[]
                {
                    new Dic { DicTypeId = userStatusType.Id, Key = "NORMAL", Value = "正常", Sequence = 1, Description = "用户状态正常", IsReadonly = true, IsSystem = true, CreatorId = 0, CreateTime = DateTime.Now, UpdatorId = 0, UpdateTime = DateTime.Now },
                    new Dic { DicTypeId = userStatusType.Id, Key = "DISABLED", Value = "禁用", Sequence = 2, Description = "用户被禁用", IsReadonly = true, IsSystem = true, CreatorId = 0, CreateTime = DateTime.Now, UpdatorId = 0, UpdateTime = DateTime.Now },
                    new Dic { DicTypeId = userStatusType.Id, Key = "LOCKED", Value = "锁定", Sequence = 3, Description = "用户被锁定", IsReadonly = true, IsSystem = true, CreatorId = 0, CreateTime = DateTime.Now, UpdatorId = 0, UpdateTime = DateTime.Now }
                };

                foreach (var dic in userStatusDics)
                {
                    var existing = await db.Queryable<Dic>()
                        .Where(x => x.DicTypeId == dic.DicTypeId && x.Key == dic.Key)
                        .FirstAsync();

                    if (existing == null)
                    {
                        await db.Insertable(dic).ExecuteReturnIdentityAsync();
                        createdCount++;
                    }
                }
            }
        }

        /// <summary>
        /// 创建租户等级字典
        /// </summary>
        private async Task CreateTenantLevelDictionaries(ISqlSugarClient db, ref int createdCount)
        {
            var tenantLevelType = await db.Queryable<DicType>()
                .Where(x => x.Name == "租户等级")
                .FirstAsync();

            if (tenantLevelType != null)
            {
                var tenantLevelDics = new[]
                {
                    new Dic { DicTypeId = tenantLevelType.Id, Key = "STANDARD", Value = "标准版", Sequence = 1, Description = "标准版租户", IsReadonly = true, IsSystem = true, CreatorId = 0, CreateTime = DateTime.Now, UpdatorId = 0, UpdateTime = DateTime.Now },
                    new Dic { DicTypeId = tenantLevelType.Id, Key = "PROFESSIONAL", Value = "专业版", Sequence = 2, Description = "专业版租户", IsReadonly = true, IsSystem = true, CreatorId = 0, CreateTime = DateTime.Now, UpdatorId = 0, UpdateTime = DateTime.Now },
                    new Dic { DicTypeId = tenantLevelType.Id, Key = "ENTERPRISE", Value = "企业版", Sequence = 3, Description = "企业版租户", IsReadonly = true, IsSystem = true, CreatorId = 0, CreateTime = DateTime.Now, UpdatorId = 0, UpdateTime = DateTime.Now }
                };

                foreach (var dic in tenantLevelDics)
                {
                    var existing = await db.Queryable<Dic>()
                        .Where(x => x.DicTypeId == dic.DicTypeId && x.Key == dic.Key)
                        .FirstAsync();

                    if (existing == null)
                    {
                        await db.Insertable(dic).ExecuteReturnIdentityAsync();
                        createdCount++;
                    }
                }
            }
        }
    }
}
