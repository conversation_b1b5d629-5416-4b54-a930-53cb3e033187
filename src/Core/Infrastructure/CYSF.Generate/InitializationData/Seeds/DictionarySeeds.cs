using System;
using System.Threading.Tasks;
using CYSF.Generate.InitializationData.Interfaces;
using CYSF.Models.Entities;
using CYSF.Models.Enum;
using SqlSugar;

namespace CYSF.Generate.InitializationData.Seeds
{
    /// <summary>
    /// 字典种子数据
    /// </summary>
    public class DictionarySeeds : IDataSeed
    {
        public int Order => 2;
        public string Name => "字典数据";
        public string Description => "创建基础字典数据";

        public async Task<bool> SeedAsync(ISqlSugarClient db)
        {
            try
            {
                Console.WriteLine("📚 创建基础字典数据...");
                // 创建字典数据
                var success = await CreateDictionaryDataAsync(db);
                return success;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 创建字典数据失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 创建字典数据
        /// </summary>
        private async Task<bool> CreateDictionaryDataAsync(ISqlSugarClient db)
        {
            try
            {
                var createdCount = 0;

                // 租户等级字典
                createdCount += await CreateTenantLevelDictionaries(db);

                Console.WriteLine($"   ✅ 字典数据创建完成，新增 {createdCount} 条");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 创建字典数据失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 创建租户等级字典
        /// </summary>
        private async Task<int> CreateTenantLevelDictionaries(ISqlSugarClient db)
        {
            var createdCount = 0;
            var tenantLevelDics = new[]
            {
                new Dic { DicType = DicType.TenantLevel, Key = "STANDARD", Value = "标准版", Sequence = 1, Description = "标准版租户", IsReadonly = true, IsSystem = true, CreatorId = 0, CreateTime = DateTime.Now, UpdatorId = 0, UpdateTime = DateTime.Now },
                new Dic { DicType = DicType.TenantLevel, Key = "PROFESSIONAL", Value = "专业版", Sequence = 2, Description = "专业版租户", IsReadonly = true, IsSystem = true, CreatorId = 0, CreateTime = DateTime.Now, UpdatorId = 0, UpdateTime = DateTime.Now },
                new Dic { DicType = DicType.TenantLevel, Key = "ENTERPRISE", Value = "企业版", Sequence = 3, Description = "企业版租户", IsReadonly = true, IsSystem = true, CreatorId = 0, CreateTime = DateTime.Now, UpdatorId = 0, UpdateTime = DateTime.Now }
            };

            foreach (var dic in tenantLevelDics)
            {
                var existing = await db.Queryable<Dic>()
                    .Where(x => x.DicType == dic.DicType && x.Key == dic.Key)
                    .FirstAsync();

                if (existing == null)
                {
                    await db.Insertable(dic).ExecuteReturnIdentityAsync();
                    createdCount++;
                }
            }
            return createdCount;
        }
    }
}
