using System;
using System.Threading.Tasks;
using CYSF.Generate.InitializationData.Interfaces;
using CYSF.Models.Entities;
using SqlSugar;

namespace CYSF.Generate.InitializationData.Seeds
{
    /// <summary>
    /// 角色种子数据
    /// </summary>
    public class TenantRoleSeeds : IDataSeed
    {
        public int Order => 3;
        public string Name => "角色数据";
        public string Description => "创建默认角色";

        public async Task<bool> SeedAsync(ISqlSugarClient db)
        {
            try
            {
                Console.WriteLine("👥 创建默认角色...");

                // 获取默认租户
                var defaultTenant = await db.Queryable<Tenant>()
                    .Where(x => x.Code == "DEFAULT")
                    .FirstAsync();

                if (defaultTenant == null)
                {
                    Console.WriteLine("   ❌ 未找到默认租户，无法创建角色");
                    return false;
                }

                // 检查是否已存在管理员角色
                var existingRole = await db.Queryable<TenantRole>()
                    .Where(x => x.TenantId == defaultTenant.Id && x.Name == "系统管理员")
                    .FirstAsync();

                if (existingRole != null)
                {
                    Console.WriteLine("   ✅ 系统管理员角色已存在");
                    return true;
                }

                // 创建系统管理员角色
                var adminRole = new TenantRole
                {
                    TenantId = defaultTenant.Id,
                    Name = "系统管理员",
                    Description = "系统管理员角色，拥有所有权限",
                    IsSystem = true,
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now
                };

                var roleId = await db.Insertable(adminRole).ExecuteReturnIdentityAsync();
                Console.WriteLine($"   ✅ 系统管理员角色创建成功，ID: {roleId}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 创建默认角色失败: {ex.Message}");
                return false;
            }
        }
    }
}
