using System;
using System.Threading.Tasks;
using CYSF.Generate.InitializationData.Interfaces;
using CYSF.Models.Entities;
using SqlSugar;

namespace CYSF.Generate.InitializationData.Seeds
{
    /// <summary>
    /// 租户种子数据
    /// </summary>
    public class TenantSeeds : IDataSeed
    {
        public int Order => 1;
        public string Name => "租户数据";
        public string Description => "创建默认租户";

        public async Task<bool> SeedAsync(ISqlSugarClient db)
        {
            try
            {
                Console.WriteLine("🏢 创建默认租户...");

                // 检查是否已存在默认租户
                var existingTenant = await db.Queryable<Tenant>()
                    .Where(x => x.Code == "DEFAULT")
                    .FirstAsync();

                if (existingTenant != null)
                {
                    Console.WriteLine("   ✅ 默认租户已存在");
                    return true;
                }

                // 创建默认租户
                var defaultTenant = new Tenant
                {
                    Code = "DEFAULT",
                    Name = "默认租户",
                    CompanyName = "系统默认租户",
                    Level = "STANDARD",
                    Status = CYSF.Models.Enum.TenantStatus.Normal,
                    ExpireTime = DateTime.Now.AddYears(10),
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now
                };

                var tenantId = await db.Insertable(defaultTenant).ExecuteReturnIdentityAsync();
                Console.WriteLine($"   ✅ 默认租户创建成功，ID: {tenantId}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 创建默认租户失败: {ex.Message}");
                return false;
            }
        }
    }
}
