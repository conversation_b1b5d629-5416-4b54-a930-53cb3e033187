using System;
using System.Threading.Tasks;
using CYSF.Generate.InitializationData.Interfaces;
using CYSF.Models.Entities;
using SqlSugar;

namespace CYSF.Generate.InitializationData.Seeds
{
    /// <summary>
    /// 用户种子数据
    /// </summary>
    public class TenantUserSeeds : IDataSeed
    {
        public int Order => 4;
        public string Name => "用户数据";
        public string Description => "创建默认平台管理员用户";

        public async Task<bool> SeedAsync(ISqlSugarClient db)
        {
            try
            {
                Console.WriteLine("👤 创建默认平台管理员用户...");
                var tenantId = 0; // 平台管理员租户Id为0
                var roleId = 0; // 平台管理员角色Id为0
                // 检查是否已存在管理员用户
                var existingUser = await db.Queryable<TenantUser>()
                    .Where(x => x.TenantId == tenantId && x.UserName == "admin")
                    .FirstAsync();

                if (existingUser != null)
                {
                    Console.WriteLine("   ✅ 平台管理员用户已存在");
                    return true;
                }

                // 创建默认管理员用户
                var adminUser = new TenantUser
                {
                    TenantId = tenantId,
                    RoleId = roleId,
                    UserName = "admin",
                    Name = "平台管理员",
                    Mobile = "13800138000",
                    Email = "<EMAIL>",
                    Password = "111111", // 实际项目中应该加密
                    PasswordSalt = Guid.NewGuid().ToString("N")[..8],
                    UserState = CYSF.Models.Enum.UserState.Normal,
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now
                };

                var userId = await db.Insertable(adminUser).ExecuteReturnIdentityAsync();
                Console.WriteLine($"   ✅ 平台管理员用户创建成功，ID: {userId}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 创建平台管理员用户失败: {ex.Message}");
                return false;
            }
        }
    }
}
