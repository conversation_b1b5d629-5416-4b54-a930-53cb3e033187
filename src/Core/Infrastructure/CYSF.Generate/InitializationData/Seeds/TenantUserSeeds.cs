using System;
using System.Threading.Tasks;
using CYSF.Generate.InitializationData.Interfaces;
using CYSF.Models.Entities;
using SqlSugar;

namespace CYSF.Generate.InitializationData.Seeds
{
    /// <summary>
    /// 用户种子数据
    /// </summary>
    public class UserSeeds : IDataSeed
    {
        public int Order => 4;
        public string Name => "用户数据";
        public string Description => "创建默认管理员用户";

        public async Task<bool> SeedAsync(ISqlSugarClient db)
        {
            try
            {
                Console.WriteLine("👤 创建默认管理员用户...");

                // 获取默认租户
                var defaultTenant = await db.Queryable<Tenant>()
                    .Where(x => x.Code == "DEFAULT")
                    .FirstAsync();

                if (defaultTenant == null)
                {
                    Console.WriteLine("   ❌ 未找到默认租户，无法创建用户");
                    return false;
                }

                // 获取管理员角色
                var adminRole = await db.Queryable<TenantRole>()
                    .Where(x => x.TenantId == defaultTenant.Id && x.Name == "系统管理员")
                    .FirstAsync();

                if (adminRole == null)
                {
                    Console.WriteLine("   ❌ 未找到系统管理员角色，无法创建用户");
                    return false;
                }

                // 检查是否已存在管理员用户
                var existingUser = await db.Queryable<TenantUser>()
                    .Where(x => x.TenantId == defaultTenant.Id && x.UserName == "admin")
                    .FirstAsync();

                if (existingUser != null)
                {
                    Console.WriteLine("   ✅ 管理员用户已存在");
                    return true;
                }

                // 创建默认管理员用户
                var adminUser = new TenantUser
                {
                    TenantId = defaultTenant.Id,
                    RoleId = adminRole.Id,
                    UserName = "admin",
                    Name = "系统管理员",
                    Mobile = "13800138000",
                    Email = "<EMAIL>",
                    Password = "123456", // 实际项目中应该加密
                    PasswordSalt = Guid.NewGuid().ToString("N")[..8],
                    UserState = CYSF.Models.Enum.UserState.Normal,
                    CreateTime = DateTime.Now,
                    UpdateTime = DateTime.Now
                };

                var userId = await db.Insertable(adminUser).ExecuteReturnIdentityAsync();
                Console.WriteLine($"   ✅ 管理员用户创建成功，ID: {userId}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 创建默认管理员用户失败: {ex.Message}");
                return false;
            }
        }
    }
}
