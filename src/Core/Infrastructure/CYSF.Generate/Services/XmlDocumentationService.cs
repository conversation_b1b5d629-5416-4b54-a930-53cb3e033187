using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Xml.Linq;
using Microsoft.Extensions.Logging;

namespace CYSF.Generate.Services
{
    /// <summary>
    /// XML文档服务，用于读取实体属性的注释
    /// </summary>
    public class XmlDocumentationService
    {
        private readonly ILogger<XmlDocumentationService> _logger;
        private readonly Dictionary<string, string> _documentationCache;

        public XmlDocumentationService(ILogger<XmlDocumentationService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _documentationCache = new Dictionary<string, string>();
        }

        /// <summary>
        /// 加载XML文档
        /// </summary>
        /// <param name="xmlPath">XML文档路径</param>
        /// <returns>是否加载成功</returns>
        public bool LoadXmlDocumentation(string xmlPath)
        {
            try
            {
                if (!File.Exists(xmlPath))
                {
                    _logger.LogWarning("XML文档文件不存在: {XmlPath}", xmlPath);
                    return false;
                }

                var doc = XDocument.Load(xmlPath);
                var members = doc.Descendants("member");

                foreach (var member in members)
                {
                    var nameAttr = member.Attribute("name");
                    if (nameAttr == null) continue;

                    var summaryElement = member.Element("summary");
                    if (summaryElement == null) continue;

                    var memberName = nameAttr.Value;
                    var summary = summaryElement.Value.Trim();

                    _documentationCache[memberName] = summary;
                }

                _logger.LogInformation("成功加载XML文档，共 {Count} 个成员", _documentationCache.Count);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载XML文档失败: {XmlPath}", xmlPath);
                return false;
            }
        }

        /// <summary>
        /// 获取属性的注释
        /// </summary>
        /// <param name="propertyInfo">属性信息</param>
        /// <returns>属性注释</returns>
        public string GetPropertySummary(PropertyInfo propertyInfo)
        {
            try
            {
                var memberName = $"P:{propertyInfo.DeclaringType?.FullName}.{propertyInfo.Name}";
                return _documentationCache.GetValueOrDefault(memberName, "");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "获取属性注释失败: {PropertyName}", propertyInfo.Name);
                return "";
            }
        }

        /// <summary>
        /// 获取类型的注释
        /// </summary>
        /// <param name="type">类型信息</param>
        /// <returns>类型注释</returns>
        public string GetTypeSummary(Type type)
        {
            try
            {
                var memberName = $"T:{type.FullName}";
                return _documentationCache.GetValueOrDefault(memberName, "");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "获取类型注释失败: {TypeName}", type.Name);
                return "";
            }
        }

        /// <summary>
        /// 获取方法的注释
        /// </summary>
        /// <param name="methodInfo">方法信息</param>
        /// <returns>方法注释</returns>
        public string GetMethodSummary(MethodInfo methodInfo)
        {
            try
            {
                var parameters = methodInfo.GetParameters();
                var parameterTypes = string.Join(",", parameters.Select(p => p.ParameterType.FullName));
                var memberName = $"M:{methodInfo.DeclaringType?.FullName}.{methodInfo.Name}";
                
                if (parameters.Length > 0)
                {
                    memberName += $"({parameterTypes})";
                }

                return _documentationCache.GetValueOrDefault(memberName, "");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "获取方法注释失败: {MethodName}", methodInfo.Name);
                return "";
            }
        }

        /// <summary>
        /// 清空缓存
        /// </summary>
        public void ClearCache()
        {
            _documentationCache.Clear();
        }

        /// <summary>
        /// 获取缓存统计信息
        /// </summary>
        /// <returns>缓存的成员数量</returns>
        public int GetCacheCount()
        {
            return _documentationCache.Count;
        }
    }
}
