using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using System.Xml.Linq;
using CYSF.Generate.Configuration;
using CYSF.Generate.Generator;
using CYSF.Generate.InitializationData;
using CYSF.Models.Entities;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace CYSF.Generate.Services;

/// <summary>
/// Code First 服务 - 根据实体类创建数据库表和初始化数据
/// </summary>
public class CodeFirstService
{
    private readonly ILogger<CodeFirstService> _logger;
    private readonly InteractiveMenuService _menuService;
    private readonly SeedManager _seedManager;
    private readonly IConfiguration _configuration;
    private readonly CodeGenerationConfiguration _codeGenConfig;
    private readonly EntityDependencyConfiguration _dependencyConfig;
    private readonly XmlDocumentationService _xmlDocService;
    private readonly UserPreferencesService _userPreferencesService;

    public CodeFirstService(ILogger<CodeFirstService> logger, InteractiveMenuService menuService, IConfiguration configuration, XmlDocumentationService xmlDocService, UserPreferencesService userPreferencesService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _menuService = menuService ?? throw new ArgumentNullException(nameof(menuService));
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _xmlDocService = xmlDocService ?? throw new ArgumentNullException(nameof(xmlDocService));
        _userPreferencesService = userPreferencesService ?? throw new ArgumentNullException(nameof(userPreferencesService));
        _seedManager = new SeedManager();

        // 绑定静态配置
        _codeGenConfig = new CodeGenerationConfiguration();
        _configuration.GetSection("CodeGeneration").Bind(_codeGenConfig);

        _dependencyConfig = new EntityDependencyConfiguration();
        _configuration.GetSection("EntityDependency").Bind(_dependencyConfig);

        // 加载XML文档
        LoadXmlDocumentation();
    }

    /// <summary>
    /// 创建数据库表
    /// </summary>
    public async Task CreateTablesAsync()
    {
        try
        {
            Console.WriteLine("=== 创建数据库表 ===");
            Console.WriteLine();

            // 获取所有实体类型
            var entityTypes = GetAllEntityTypes();
            if (!entityTypes.Any())
            {
                _menuService.ShowWarning("未找到任何实体类");
                return;
            }

            Console.WriteLine($"发现 {entityTypes.Count} 个实体类:");
            foreach (var entityType in entityTypes)
            {
                var tableName = GetTableName(entityType);
                Console.WriteLine($"   - {entityType.Name} → {tableName}");
            }
            Console.WriteLine();

            // 确认操作
            var confirmed = await _menuService.ShowConfirmationAsync("确定要创建这些表吗？", false);
            if (!confirmed)
            {
                Console.WriteLine("操作已取消");
                return;
            }

            // 检查数据库连接
            using var db = DbContext.Db;
            if (!await CheckDatabaseConnectionAsync(db))
            {
                return;
            }

            // 过滤排除的实体
            var filteredEntityTypes = entityTypes.Where(e =>
                !_codeGenConfig.ExcludedEntitiesFromTableCreation.Contains(e.Name)).ToList();

            if (filteredEntityTypes.Count != entityTypes.Count)
            {
                Console.WriteLine($"排除 {entityTypes.Count - filteredEntityTypes.Count} 个实体的表创建");
            }

            // 按依赖关系排序
            var sortedEntityTypes = SortEntityTypesByDependency(filteredEntityTypes);

            var createdCount = 0;
            var updatedCount = 0;
            var existingCount = 0;
            var errorCount = 0;

            Console.WriteLine("🔨 开始分析和处理表结构...");
            Console.WriteLine();

            for (int i = 0; i < sortedEntityTypes.Count; i++)
            {
                var entityType = sortedEntityTypes[i];
                _menuService.ShowProgress("处理表", i + 1, sortedEntityTypes.Count);

                try
                {
                    var result = await CreateOrUpdateTableAsync(db, entityType);
                    switch (result)
                    {
                        case TableCreationResult.Created:
                            createdCount++;
                            break;
                        case TableCreationResult.Updated:
                            updatedCount++;
                            break;
                        case TableCreationResult.AlreadyExists:
                            existingCount++;
                            break;
                        case TableCreationResult.Error:
                            errorCount++;
                            break;
                    }
                }
                catch (Exception ex)
                {
                    errorCount++;
                    _logger.LogError(ex, "处理表 {EntityType} 失败", entityType.Name);
                }
            }

            Console.WriteLine();
            _menuService.ShowResult("表结构处理", errorCount == 0,
                $"创建: {createdCount}, 更新: {updatedCount}, 已存在: {existingCount}, 错误: {errorCount}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建数据库表失败: {Message}", ex.Message);
            _menuService.ShowError($"创建数据库表失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 初始化基础数据
    /// </summary>
    public async Task InitializeDataAsync()
    {
        try
        {
            Console.WriteLine("=== 初始化基础数据 ===");
            Console.WriteLine();

            // 确认操作
            var confirmed = await _menuService.ShowConfirmationAsync("确定要初始化基础数据吗？", false);
            if (!confirmed)
            {
                Console.WriteLine("操作已取消");
                return;
            }

            using var db = DbContext.Db;
            if (!await CheckDatabaseConnectionAsync(db))
            {
                return;
            }

            // 使用种子数据管理器执行初始化
            var success = await _seedManager.SeedAllAsync(db);

            Console.WriteLine();
            _menuService.ShowResult("基础数据初始化", success);

            if (success)
            {
                Console.WriteLine();
                _menuService.ShowInfoBox("默认账户信息", new[]
                {
                    "用户名: admin",
                    "密码: 123456",
                    "角色: 系统管理员",
                    "租户: DEFAULT"
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "初始化基础数据失败: {Message}", ex.Message);
            _menuService.ShowError($"初始化基础数据失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 创建表并初始化数据
    /// </summary>
    public async Task CreateTablesAndInitializeDataAsync()
    {
        try
        {
            Console.WriteLine("=== 一键创建表并初始化数据 ===");
            Console.WriteLine();

            var confirmed = await _menuService.ShowConfirmationAsync("确定要执行完整的数据库初始化吗？", false);
            if (!confirmed)
            {
                Console.WriteLine("操作已取消");
                return;
            }

            Console.WriteLine("第一步: 创建数据库表");
            Console.WriteLine("─────────────────────");
            await CreateTablesAsync();

            Console.WriteLine();
            Console.WriteLine("第二步: 初始化基础数据");
            Console.WriteLine("─────────────────────");
            await InitializeDataAsync();

            Console.WriteLine();
            _menuService.ShowSuccess("数据库完整初始化完成！");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "完整初始化失败: {Message}", ex.Message);
            _menuService.ShowError($"完整初始化失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 验证表完整性
    /// </summary>
    public async Task ValidateTablesAsync()
    {
        try
        {
            Console.WriteLine("=== 验证表完整性 ===");
            Console.WriteLine();

            using var db = DbContext.Db;
            if (!await CheckDatabaseConnectionAsync(db))
            {
                return;
            }

            var entityTypes = GetAllEntityTypes();
            var missingTables = new List<string>();
            var existingTables = new List<string>();

            Console.WriteLine("🔍 检查表存在性...");
            Console.WriteLine();

            foreach (var entityType in entityTypes)
            {
                var tableName = GetTableName(entityType);
                var exists = db.DbMaintenance.IsAnyTable(tableName, false);

                if (exists)
                {
                    existingTables.Add($"{entityType.Name} → {tableName}");
                    Console.WriteLine($"✅ {tableName} ({entityType.Name})");
                }
                else
                {
                    missingTables.Add($"{entityType.Name} → {tableName}");
                    Console.WriteLine($"❌ {tableName} ({entityType.Name})");
                }
            }

            Console.WriteLine();
            Console.WriteLine($"📊 验证结果:");
            Console.WriteLine($"   总计实体: {entityTypes.Count}");
            Console.WriteLine($"   已存在表: {existingTables.Count}");
            Console.WriteLine($"   缺失表: {missingTables.Count}");

            if (missingTables.Any())
            {
                Console.WriteLine();
                _menuService.ShowWarning("发现缺失的表，建议执行表创建操作");
            }
            else
            {
                Console.WriteLine();
                _menuService.ShowSuccess("所有表都已存在，数据库结构完整！");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证表完整性失败: {Message}", ex.Message);
            _menuService.ShowError($"验证表完整性失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 更新数据库表结构
    /// </summary>
    public async Task UpdateTablesAsync()
    {
        try
        {
            Console.WriteLine("=== 更新数据库表结构 ===");
            Console.WriteLine();

            // 获取所有实体类型
            var entityTypes = GetAllEntityTypes();
            if (!entityTypes.Any())
            {
                _menuService.ShowWarning("未找到任何实体类");
                return;
            }

            Console.WriteLine($"发现 {entityTypes.Count} 个实体类:");
            foreach (var entityType in entityTypes)
            {
                var tableName = GetTableName(entityType);
                Console.WriteLine($"   - {entityType.Name} → {tableName}");
            }
            Console.WriteLine();

            // 确认操作
            var confirmed = await _menuService.ShowConfirmationAsync("确定要更新数据库表结构吗？此操作可能会修改现有表结构。", false);
            if (!confirmed)
            {
                Console.WriteLine("操作已取消");
                return;
            }

            // 检查数据库连接
            using var db = DbContext.Db;
            if (!await CheckDatabaseConnectionAsync(db))
            {
                return;
            }

            var updatedCount = 0;
            var createdCount = 0;
            var errorCount = 0;

            Console.WriteLine("🔄 开始更新数据库表结构...");
            Console.WriteLine();

            for (int i = 0; i < entityTypes.Count; i++)
            {
                var entityType = entityTypes[i];
                _menuService.ShowProgress("更新表结构", i + 1, entityTypes.Count);

                try
                {
                    var tableName = GetTableName(entityType);
                    Console.WriteLine($"处理表: {tableName} ({entityType.Name})");

                    if (db.DbMaintenance.IsAnyTable(tableName))
                    {
                        // 表存在，使用SqlSugar的CodeFirst更新功能
                        CreateTableByType(db, entityType);
                        Console.WriteLine($"   表结构已更新: {tableName}");
                        updatedCount++;
                    }
                    else
                    {
                        // 表不存在，创建新表
                        CreateTableByType(db, entityType);
                        Console.WriteLine($"   新表创建成功: {tableName}");
                        createdCount++;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   处理失败: {entityType.Name} - {ex.Message}");
                    errorCount++;
                    _logger.LogError(ex, "更新表 {EntityType} 失败", entityType.Name);
                }

                Console.WriteLine();
            }

            Console.WriteLine();

            // 检查孤儿表
            await CheckOrphanTablesAsync(db);

            _menuService.ShowResult("表结构更新", errorCount == 0,
                $"更新: {updatedCount}, 创建: {createdCount}, 错误: {errorCount}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新数据库表结构失败: {Message}", ex.Message);
            _menuService.ShowError($"更新数据库表结构失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 创建表并生成代码
    /// </summary>
    public async Task CreateTablesAndGenerateCodeAsync()
    {
        try
        {
            Console.WriteLine("=== 创建表并生成代码 ===");
            Console.WriteLine();

            var confirmed = await _menuService.ShowConfirmationAsync("确定要创建表并生成完整代码吗？", false);
            if (!confirmed)
            {
                Console.WriteLine("操作已取消");
                return;
            }

            Console.WriteLine("第一步: 创建数据库表");
            Console.WriteLine("─────────────────────");
            await CreateTablesAsync();

            Console.WriteLine();
            Console.WriteLine("第二步: 生成代码文件");
            Console.WriteLine("─────────────────────");

            // 生成代码
            await GenerateCodeAsync();

            Console.WriteLine();
            _menuService.ShowSuccess("表创建和代码生成完成！");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建表并生成代码失败: {Message}", ex.Message);
            _menuService.ShowError($"创建表并生成代码失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 创建表、初始化数据并生成代码（一键完成所有操作）
    /// </summary>
    public async Task CreateTablesInitializeAndGenerateCodeAsync()
    {
        try
        {
            Console.WriteLine("=== 一键完成所有操作 ===");
            Console.WriteLine();

            // 确认操作
            var confirmed = await _menuService.ShowConfirmationAsync(
                "此操作将：\n1. 创建数据库表\n2. 初始化基础数据\n3. 生成所有代码文件\n\n确定继续吗？", false);
            if (!confirmed)
            {
                Console.WriteLine("操作已取消");
                return;
            }

            Console.WriteLine("开始执行一键操作...");
            Console.WriteLine();

            // 步骤1：创建表
            Console.WriteLine("步骤 1/3: 创建数据库表...");
            await CreateTablesAsync();
            Console.WriteLine();

            // 步骤2：初始化数据
            Console.WriteLine("步骤 2/3: 初始化基础数据...");
            await InitializeDataAsync();
            Console.WriteLine();

            // 步骤3：生成代码
            Console.WriteLine("步骤 3/3: 生成代码文件...");
            await GenerateCodeAsync();
            Console.WriteLine();

            Console.WriteLine("一键操作完成！");
            _menuService.ShowResult("一键完成所有操作", true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "一键操作失败: {Message}", ex.Message);
            _menuService.ShowError($"一键操作失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 检查数据库连接
    /// </summary>
    private async Task<bool> CheckDatabaseConnectionAsync(ISqlSugarClient db)
    {
        try
        {
            Console.WriteLine("🔗 检查数据库连接...");
            await db.Ado.GetDataTableAsync("SELECT 1");
            Console.WriteLine("✅ 数据库连接正常");
            Console.WriteLine();
            return true;
        }
        catch (Exception ex)
        {
            _menuService.ShowError($"数据库连接失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 检查孤儿表（数据库中存在但没有对应实体类的表）
    /// </summary>
    private async Task CheckOrphanTablesAsync(ISqlSugarClient db)
    {
        try
        {
            Console.WriteLine("🔍 检查孤儿表...");

            // 获取数据库中的所有表
            var dbTables = db.DbMaintenance.GetTableInfoList(false)
                .Select(t => t.Name)
                .ToList();

            // 获取实体对应的表名
            var entityTypes = GetAllEntityTypes();
            var entityTableNames = entityTypes.Select(GetTableName).ToList();

            // 找出孤儿表
            var orphanTables = dbTables.Except(entityTableNames).ToList();

            if (!orphanTables.Any())
            {
                Console.WriteLine("   ✅ 未发现孤儿表");
                return;
            }

            Console.WriteLine($"   ⚠️ 发现 {orphanTables.Count} 个孤儿表:");
            foreach (var table in orphanTables)
            {
                Console.WriteLine($"      - {table}");
            }
            Console.WriteLine();

            // 获取用户偏好设置
            var tablePrefs = _userPreferencesService.GetTableManagementPreferences();

            if (tablePrefs.ConfirmBeforeDelete)
            {
                var confirmed = await _menuService.ShowConfirmationAsync(
                    $"是否删除这 {orphanTables.Count} 个孤儿表？", false);

                if (confirmed)
                {
                    await DeleteOrphanTablesAsync(db, orphanTables);
                }
                else
                {
                    Console.WriteLine("   ⏭️ 跳过删除孤儿表");
                }
            }
            else if (tablePrefs.AutoDeleteOrphanTables)
            {
                await DeleteOrphanTablesAsync(db, orphanTables);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"   ❌ 检查孤儿表失败: {ex.Message}");
            _logger.LogError(ex, "检查孤儿表失败");
        }
    }

    /// <summary>
    /// 删除孤儿表
    /// </summary>
    private async Task DeleteOrphanTablesAsync(ISqlSugarClient db, List<string> orphanTables)
    {
        try
        {
            var deletedCount = 0;
            var errorCount = 0;

            foreach (var tableName in orphanTables)
            {
                try
                {
                    // 备份表（如果用户偏好要求）
                    var tablePrefs = _userPreferencesService.GetTableManagementPreferences();
                    if (tablePrefs.BackupBeforeDelete)
                    {
                        await BackupTableAsync(db, tableName);
                    }

                    // 删除表
                    db.DbMaintenance.DropTable(tableName);
                    Console.WriteLine($"   ✅ 已删除孤儿表: {tableName}");
                    deletedCount++;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ❌ 删除表失败: {tableName} - {ex.Message}");
                    errorCount++;
                    _logger.LogError(ex, "删除孤儿表 {TableName} 失败", tableName);
                }
            }

            Console.WriteLine($"   📊 删除结果: 成功 {deletedCount}, 失败 {errorCount}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"   ❌ 删除孤儿表过程失败: {ex.Message}");
            _logger.LogError(ex, "删除孤儿表过程失败");
        }
    }

    /// <summary>
    /// 备份表
    /// </summary>
    private async Task BackupTableAsync(ISqlSugarClient db, string tableName)
    {
        try
        {
            var backupTableName = $"{tableName}_backup_{DateTime.Now:yyyyMMdd_HHmmss}";
            var sql = $"SELECT * INTO {backupTableName} FROM {tableName}";
            await db.Ado.ExecuteCommandAsync(sql);
            Console.WriteLine($"   💾 已备份表: {tableName} → {backupTableName}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"   ⚠️ 备份表失败: {tableName} - {ex.Message}");
            _logger.LogWarning(ex, "备份表 {TableName} 失败", tableName);
        }
    }

    /// <summary>
    /// 获取所有实体类型（通过反射和配置）
    /// </summary>
    private List<Type> GetAllEntityTypes()
    {
        try
        {
            Console.WriteLine($"🔍 开始扫描实体类...");
            Console.WriteLine($"   目标命名空间: {_codeGenConfig.EntityNamespace}");

            var modelsAssembly = Assembly.GetAssembly(typeof(Tenant));
            if (modelsAssembly == null)
            {
                Console.WriteLine("   ❌ 无法获取Models程序集");
                return new List<Type>();
            }

            Console.WriteLine($"   ✅ 成功加载程序集: {modelsAssembly.GetName().Name}");

            // 获取所有类型进行调试
            var allTypes = modelsAssembly.GetTypes().ToList();
            Console.WriteLine($"   📋 程序集中所有类型数量: {allTypes.Count}");

            // 获取指定命名空间的类型
            var namespaceTypes = allTypes.Where(t => t.Namespace == _codeGenConfig.EntityNamespace).ToList();
            Console.WriteLine($"   📋 命名空间 {_codeGenConfig.EntityNamespace} 中的类型数量: {namespaceTypes.Count}");

            foreach (var type in namespaceTypes)
            {
                var isClass = type.IsClass;
                var isAbstract = type.IsAbstract;
                Console.WriteLine($"      - {type.Name}: IsClass={isClass}, IsAbstract={isAbstract}");
            }

            // 获取所有实体类型（CYSF.Models.Entities命名空间中的所有类都是实体类）
            var allEntityTypes = modelsAssembly.GetTypes()
                .Where(t => t.Namespace == _codeGenConfig.EntityNamespace &&
                           t.IsClass &&
                           !t.IsAbstract)
                .ToList();

            Console.WriteLine($"📋 发现 {allEntityTypes.Count} 个实体类:");
            foreach (var type in allEntityTypes)
            {
                Console.WriteLine($"   - {type.Name}");
            }

            // 根据配置的依赖顺序排序
            var orderedTypes = new List<Type>();

            // 先添加配置中指定顺序的实体
            foreach (var entityName in _dependencyConfig.DependencyOrder)
            {
                var entityType = allEntityTypes.FirstOrDefault(t => t.Name == entityName);
                if (entityType != null)
                {
                    orderedTypes.Add(entityType);
                }
            }

            // 再添加配置中未指定的实体
            var remainingTypes = allEntityTypes.Except(orderedTypes).ToList();
            orderedTypes.AddRange(remainingTypes);

            return orderedTypes;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取实体类型失败: {Message}", ex.Message);
            return new List<Type>();
        }
    }



    /// <summary>
    /// 按依赖关系排序实体类型
    /// </summary>
    private List<Type> SortEntityTypesByDependency(List<Type> entityTypes)
    {
        var priorityOrder = new Dictionary<string, int>
        {
            { nameof(Tenant), 1 },
            { nameof(Dic), 2 },
            { nameof(TenantUser), 3 },
            { nameof(UserLoginRecord), 4 }
        };

        return entityTypes
            .OrderBy(t => priorityOrder.ContainsKey(t.Name) ? priorityOrder[t.Name] : 999)
            .ThenBy(t => t.Name)
            .ToList();
    }

    /// <summary>
    /// 获取表名
    /// </summary>
    private string GetTableName(Type entityType)
    {
        try
        {
            var tableAttr = entityType.GetCustomAttributes()
                .FirstOrDefault(attr => attr.GetType().Name == "SugarTableAttribute");

            if (tableAttr != null)
            {
                var tableNameProperty = tableAttr.GetType().GetProperty("TableName");
                if (tableNameProperty != null)
                {
                    var tableName = tableNameProperty.GetValue(tableAttr) as string;
                    if (!string.IsNullOrEmpty(tableName))
                    {
                        return tableName;
                    }
                }
            }

            return entityType.Name;
        }
        catch
        {
            return entityType.Name;
        }
    }

    /// <summary>
    /// 使用Type动态创建表
    /// </summary>
    private void CreateTableByType(ISqlSugarClient db, Type entityType)
    {
        try
        {
            // 使用SqlSugar的EntityInfo方式创建表，避免泛型方法重载问题
            var entityInfo = db.EntityMaintenance.GetEntityInfo(entityType);
            var tableName = entityInfo.DbTableName;

            // 检查表是否已存在
            if (!db.DbMaintenance.IsAnyTable(tableName, false))
            {
                // 使用DbMaintenance.CreateTable方法创建表
                var columns = db.EntityMaintenance.GetEntityInfo(entityType).Columns
                    .Select(c => new DbColumnInfo
                    {
                        DbColumnName = c.DbColumnName,
                        DataType = GetSqlDataType(c.PropertyInfo.PropertyType, db.CurrentConnectionConfig.DbType),
                        Length = c.Length,
                        DecimalDigits = c.DecimalDigits,
                        IsNullable = c.IsNullable,
                        IsPrimarykey = c.IsPrimarykey,
                        IsIdentity = c.IsIdentity,
                        ColumnDescription = c.ColumnDescription,
                        DefaultValue = c.DefaultValue
                    }).ToList();

                db.DbMaintenance.CreateTable(tableName, columns);
            }
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"创建表 {entityType.Name} 失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 根据.NET类型获取对应的SQL数据类型
    /// </summary>
    private string GetSqlDataType(Type propertyType, DbType dbType)
    {
        // 处理可空类型
        var underlyingType = Nullable.GetUnderlyingType(propertyType) ?? propertyType;

        return underlyingType.Name switch
        {
            nameof(Int32) => dbType == DbType.SqlServer ? "int" : "INTEGER",
            nameof(Int64) => dbType == DbType.SqlServer ? "bigint" : "BIGINT",
            nameof(String) => dbType == DbType.SqlServer ? "nvarchar" : "TEXT",
            nameof(DateTime) => dbType == DbType.SqlServer ? "datetime" : "DATETIME",
            nameof(Boolean) => dbType == DbType.SqlServer ? "bit" : "BOOLEAN",
            nameof(Decimal) => dbType == DbType.SqlServer ? "decimal" : "DECIMAL",
            nameof(Double) => dbType == DbType.SqlServer ? "float" : "REAL",
            nameof(Single) => dbType == DbType.SqlServer ? "real" : "REAL",
            nameof(Byte) => dbType == DbType.SqlServer ? "tinyint" : "INTEGER",
            nameof(Int16) => dbType == DbType.SqlServer ? "smallint" : "INTEGER",
            _ when underlyingType.IsEnum => dbType == DbType.SqlServer ? "int" : "INTEGER",
            _ => dbType == DbType.SqlServer ? "nvarchar(max)" : "TEXT"
        };
    }

    /// <summary>
    /// 生成PageListReq文件
    /// </summary>
    private async Task GeneratePageListReqAsync(List<Type> entityTypes, TemplateEngine templateEngine, string projectRoot)
    {
        var templatePath = Path.Combine(projectRoot, "Template", "Request", "PageListRequestTemplate.txt");
        var template = await templateEngine.ReadTemplateAsync(templatePath);

        foreach (var entityType in entityTypes)
        {
            try
            {
                // 检查是否在排除列表中
                if (_codeGenConfig.ExcludedEntitiesFromCodeGeneration.Contains(entityType.Name))
                {
                    continue;
                }

                var variables = templateEngine.GenerateVariables(entityType);
                variables["FILTER_PROPERTIES"] = templateEngine.GenerateFilterProperties(entityType);
                var requestCode = templateEngine.ReplaceVariables(template, variables);

                var requestDir = Path.Combine(_codeGenConfig.ApplicationOutputPath, "..", "..", "Model", "CYSF.Models", "Request", entityType.Name);
                var outputPath = Path.Combine(requestDir, $"{entityType.Name}PageListReq.cs");
                var fullPath = Path.GetFullPath(Path.Combine(projectRoot, outputPath));

                if (await templateEngine.WriteFileAsync(fullPath, requestCode, false))
                {
                    Console.WriteLine($"   生成PageListReq: {entityType.Name}PageListReq.cs");
                }
                else
                {
                    Console.WriteLine($"   跳过PageListReq: {entityType.Name}PageListReq.cs (文件已存在)");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   生成PageListReq失败: {entityType.Name} - {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 生成Dto文件
    /// </summary>
    private async Task GenerateDtoAsync(List<Type> entityTypes, TemplateEngine templateEngine, string projectRoot)
    {
        var templatePath = Path.Combine(projectRoot, "Template", "Response", "DtoTemplate.txt");
        var template = await templateEngine.ReadTemplateAsync(templatePath);

        foreach (var entityType in entityTypes)
        {
            try
            {
                // 检查是否在排除列表中
                if (_codeGenConfig.ExcludedEntitiesFromCodeGeneration.Contains(entityType.Name))
                {
                    continue;
                }

                var variables = templateEngine.GenerateVariables(entityType);
                variables["DTO_PROPERTIES"] = templateEngine.GenerateDtoProperties(entityType);
                var dtoCode = templateEngine.ReplaceVariables(template, variables);

                var responseDir = Path.Combine(_codeGenConfig.ApplicationOutputPath, "..", "..", "Model", "CYSF.Models", "Response");
                var outputPath = Path.Combine(responseDir, $"{entityType.Name}Dto.cs");
                var fullPath = Path.GetFullPath(Path.Combine(projectRoot, outputPath));

                if (await templateEngine.WriteFileAsync(fullPath, dtoCode, false))
                {
                    Console.WriteLine($"   生成Dto: {entityType.Name}Dto.cs");
                }
                else
                {
                    Console.WriteLine($"   跳过Dto: {entityType.Name}Dto.cs (文件已存在)");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   生成Dto失败: {entityType.Name} - {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 获取项目根目录
    /// </summary>
    private string GetProjectRoot()
    {
        var currentDirectory = Directory.GetCurrentDirectory();

        // 如果当前目录包含Template文件夹，直接返回
        if (Directory.Exists(Path.Combine(currentDirectory, "Template")))
        {
            return currentDirectory;
        }

        // 否则查找包含CYSF.Generate.csproj的目录
        var directory = new DirectoryInfo(currentDirectory);
        while (directory != null)
        {
            if (File.Exists(Path.Combine(directory.FullName, "CYSF.Generate.csproj")))
            {
                return directory.FullName;
            }
            directory = directory.Parent;
        }

        // 如果找不到，使用当前目录
        return currentDirectory;
    }

    /// <summary>
    /// 创建或更新表
    /// </summary>
    private async Task<TableCreationResult> CreateOrUpdateTableAsync(ISqlSugarClient db, Type entityType)
    {
        try
        {
            var tableName = GetTableName(entityType);

            // 检查表是否存在
            if (!db.DbMaintenance.IsAnyTable(tableName))
            {
                // 表不存在，创建新表
                CreateTableByType(db, entityType);
                Console.WriteLine($"   新表创建成功: {tableName}");
                return TableCreationResult.Created;
            }
            else
            {
                // 表存在，检查是否需要更新结构
                if (await NeedTableStructureUpdateAsync(db, entityType))
                {
                    // 备份表数据（如果需要）
                    await BackupTableDataIfNeededAsync(db, tableName);

                    // 更新表结构
                    CreateTableByType(db, entityType);
                    Console.WriteLine($"   表结构已更新: {tableName}");
                    return TableCreationResult.Updated;
                }
                else
                {
                    Console.WriteLine($"   表结构无需更新: {tableName}");
                    return TableCreationResult.AlreadyExists;
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"   处理表失败: {entityType.Name} - {ex.Message}");
            return TableCreationResult.Error;
        }
    }

    /// <summary>
    /// 检查表结构是否需要更新
    /// </summary>
    private Task<bool> NeedTableStructureUpdateAsync(ISqlSugarClient db, Type entityType)
    {
        try
        {
            var tableName = GetTableName(entityType);
            var entityInfo = db.EntityMaintenance.GetEntityInfo(entityType);
            var dbColumns = db.DbMaintenance.GetColumnInfosByTableName(tableName, false);

            // 检查列数量是否一致
            if (entityInfo.Columns.Count != dbColumns.Count)
            {
                return true;
            }

            // 检查每个列的结构
            foreach (var entityColumn in entityInfo.Columns)
            {
                var dbColumn = dbColumns.FirstOrDefault(c =>
                    c.DbColumnName.Equals(entityColumn.DbColumnName, StringComparison.OrdinalIgnoreCase));

                if (dbColumn == null)
                {
                    // 新增列
                    return true;
                }

                // 检查列属性是否有变化
                if (HasColumnChanged(entityColumn, dbColumn))
                {
                    return true;
                }
            }

            return Task.FromResult(false);
        }
        catch
        {
            // 如果检查失败，默认需要更新
            return Task.FromResult(true);
        }
    }

    /// <summary>
    /// 检查列是否有变化
    /// </summary>
    private bool HasColumnChanged(EntityColumnInfo entityColumn, DbColumnInfo dbColumn)
    {
        // 检查数据类型
        var expectedDataType = GetSqlDataType(entityColumn.PropertyInfo.PropertyType, DbType.SqlServer);
        if (!dbColumn.DataType.Equals(expectedDataType, StringComparison.OrdinalIgnoreCase))
        {
            return true;
        }

        // 检查长度
        if (entityColumn.Length != dbColumn.Length)
        {
            return true;
        }

        // 检查是否可空
        if (entityColumn.IsNullable != dbColumn.IsNullable)
        {
            return true;
        }

        // 检查是否主键
        if (entityColumn.IsPrimarykey != dbColumn.IsPrimarykey)
        {
            return true;
        }

        return false;
    }

    /// <summary>
    /// 备份表数据（如果需要）
    /// </summary>
    private async Task BackupTableDataIfNeededAsync(ISqlSugarClient db, string tableName)
    {
        try
        {
            // 检查表是否有数据
            var hasData = await db.Queryable<object>().AS(tableName).AnyAsync();
            if (hasData)
            {
                var backupTableName = $"{tableName}_Backup_{DateTime.Now:yyyyMMddHHmmss}";

                // 创建备份表
                var createBackupSql = $"SELECT * INTO {backupTableName} FROM {tableName}";
                await db.Ado.ExecuteCommandAsync(createBackupSql);

                Console.WriteLine($"   数据已备份到: {backupTableName}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"   备份表数据失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 管理备份表
    /// </summary>
    public async Task ManageBackupTablesAsync()
    {
        try
        {
            using var db = _dbProvider.GetDatabase();

            // 获取所有表
            var allTables = db.DbMaintenance.GetTableInfoList(false);

            // 筛选备份表（包含_Backup_的表）
            var backupTables = allTables.Where(t => t.Name.Contains("_Backup_")).ToList();

            if (backupTables.Count == 0)
            {
                Console.WriteLine("未找到任何备份表。");
                return;
            }

            Console.WriteLine($"找到 {backupTables.Count} 个备份表：");
            Console.WriteLine();

            for (int i = 0; i < backupTables.Count; i++)
            {
                var table = backupTables[i];
                var recordCount = await db.Queryable<object>().AS(table.Name).CountAsync();
                Console.WriteLine($"{i + 1}. {table.Name} (记录数: {recordCount})");
            }

            Console.WriteLine();
            Console.WriteLine("操作选项：");
            Console.WriteLine("1. 删除所有备份表");
            Console.WriteLine("2. 选择性删除备份表");
            Console.WriteLine("0. 返回");
            Console.WriteLine();

            Console.Write("请选择操作 (输入数字): ");
            var choice = Console.ReadLine()?.Trim();

            switch (choice)
            {
                case "1":
                    await DeleteAllBackupTablesAsync(db, backupTables);
                    break;
                case "2":
                    await DeleteSelectedBackupTablesAsync(db, backupTables);
                    break;
                case "0":
                    return;
                default:
                    Console.WriteLine("无效的选择");
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "管理备份表失败: {Message}", ex.Message);
            Console.WriteLine($"管理备份表失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 删除所有备份表
    /// </summary>
    private async Task DeleteAllBackupTablesAsync(ISqlSugarClient db, List<DbTableInfo> backupTables)
    {
        Console.WriteLine();
        Console.Write($"确定要删除所有 {backupTables.Count} 个备份表吗？(y/N): ");
        var confirm = Console.ReadLine()?.Trim().ToLowerInvariant();

        if (confirm != "y" && confirm != "yes")
        {
            Console.WriteLine("操作已取消");
            return;
        }

        int deletedCount = 0;
        foreach (var table in backupTables)
        {
            try
            {
                db.DbMaintenance.DropTable(table.Name);
                Console.WriteLine($"已删除: {table.Name}");
                deletedCount++;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除失败: {table.Name} - {ex.Message}");
            }
        }

        Console.WriteLine();
        Console.WriteLine($"删除完成，共删除 {deletedCount} 个备份表");
    }

    /// <summary>
    /// 选择性删除备份表
    /// </summary>
    private async Task DeleteSelectedBackupTablesAsync(ISqlSugarClient db, List<DbTableInfo> backupTables)
    {
        Console.WriteLine();
        Console.WriteLine("请输入要删除的表编号（用逗号分隔，如：1,3,5）：");
        Console.Write("编号: ");
        var input = Console.ReadLine()?.Trim();

        if (string.IsNullOrEmpty(input))
        {
            Console.WriteLine("操作已取消");
            return;
        }

        var indices = input.Split(',')
            .Select(s => s.Trim())
            .Where(s => int.TryParse(s, out _))
            .Select(int.Parse)
            .Where(i => i >= 1 && i <= backupTables.Count)
            .Select(i => i - 1)
            .ToList();

        if (indices.Count == 0)
        {
            Console.WriteLine("未选择有效的表");
            return;
        }

        Console.WriteLine();
        Console.WriteLine("将删除以下表：");
        foreach (var index in indices)
        {
            Console.WriteLine($"- {backupTables[index].Name}");
        }

        Console.Write("确定删除吗？(y/N): ");
        var confirm = Console.ReadLine()?.Trim().ToLowerInvariant();

        if (confirm != "y" && confirm != "yes")
        {
            Console.WriteLine("操作已取消");
            return;
        }

        int deletedCount = 0;
        foreach (var index in indices)
        {
            try
            {
                var tableName = backupTables[index].Name;
                db.DbMaintenance.DropTable(tableName);
                Console.WriteLine($"已删除: {tableName}");
                deletedCount++;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除失败: {backupTables[index].Name} - {ex.Message}");
            }
        }

        Console.WriteLine();
        Console.WriteLine($"删除完成，共删除 {deletedCount} 个备份表");
    }

    /// <summary>
    /// 表创建结果枚举
    /// </summary>
    private enum TableCreationResult
    {
        Created,
        Updated,
        AlreadyExists,
        Error
    }

    /// <summary>
    /// 如果表不存在则创建表
    /// </summary>
    private Task<TableCreationResult> CreateTableIfNotExistsAsync(ISqlSugarClient db, Type entityType)
    {
        try
        {
            var tableName = GetTableName(entityType);

            if (db.DbMaintenance.IsAnyTable(tableName, false))
            {
                return Task.FromResult(TableCreationResult.AlreadyExists);
            }

            // 使用统一的方法创建表
            CreateTableByType(db, entityType);

            // 验证表是否创建成功
            if (db.DbMaintenance.IsAnyTable(tableName, false))
            {
                return Task.FromResult(TableCreationResult.Created);
            }
            else
            {
                return Task.FromResult(TableCreationResult.Error);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建表 {EntityType} 失败: {Message}", entityType.Name, ex.Message);
            return Task.FromResult(TableCreationResult.Error);
        }
    }

    /// <summary>
    /// 加载XML文档
    /// </summary>
    private void LoadXmlDocumentation()
    {
        try
        {
            var xmlPath = Path.GetFullPath(_codeGenConfig.EntityXmlDocPath);
            if (File.Exists(xmlPath))
            {
                _xmlDocService.LoadXmlDocumentation(xmlPath);
                Console.WriteLine($"✅ XML文档加载成功: {xmlPath}");
            }
            else
            {
                Console.WriteLine($"⚠️ XML文档文件不存在: {xmlPath}");
                Console.WriteLine("   请确保CYSF.Models项目已编译并生成了XML文档");
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "加载XML文档失败");
            Console.WriteLine($"⚠️ 加载XML文档失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 生成代码
    /// </summary>
    public async Task GenerateCodeAsync()
    {
        try
        {
            Console.WriteLine("=== 生成代码文件 ===");
            Console.WriteLine("注意: 若目标文件已存在，则跳过生成该文件");
            Console.WriteLine();

            // 显示当前配置并询问是否要修改
            await ShowAndConfigureCodeGenerationOptionsAsync();

            var confirmed = await _menuService.ShowConfirmationAsync("确定要生成代码文件吗？", false);
            if (!confirmed)
            {
                Console.WriteLine("操作已取消");
                return;
            }

            // 获取实体类型
            var entityTypes = GetAllEntityTypes();
            if (!entityTypes.Any())
            {
                _menuService.ShowWarning("未找到任何实体类");
                return;
            }

            Console.WriteLine($"将为 {entityTypes.Count} 个实体生成代码:");
            foreach (var entityType in entityTypes)
            {
                Console.WriteLine($"   - {entityType.Name}");
            }
            Console.WriteLine();

            var stats = new GenerationStats();
            var codeGenPrefs = _userPreferencesService.GetCodeGenerationPreferences();

            // 生成Service
            if (codeGenPrefs.GenerateService)
            {
                Console.WriteLine("生成Service层代码...");
                stats.ServicesGenerated = await GenerateServicesAsync(entityTypes);
            }

            // 生成Repository
            if (codeGenPrefs.GenerateRepository)
            {
                Console.WriteLine("生成Repository层代码...");
                stats.RepositoriesGenerated = await GenerateRepositoriesAsync(entityTypes);
            }

            // 生成Application
            if (codeGenPrefs.GenerateApplication)
            {
                Console.WriteLine("生成Application层代码...");
                stats.ApplicationsGenerated = await GenerateApplicationsAsync(entityTypes);
            }

            // 生成Controller
            if (codeGenPrefs.GenerateController)
            {
                Console.WriteLine("生成Controller层代码...");
                stats.ControllersGenerated = await GenerateControllersAsync(entityTypes);
            }

            // 输出统计信息
            Console.WriteLine();
            Console.WriteLine("代码生成统计:");
            if (codeGenPrefs.GenerateService)
                Console.WriteLine($"   Service: {stats.ServicesGenerated}");
            if (codeGenPrefs.GenerateRepository)
                Console.WriteLine($"   Repository: {stats.RepositoriesGenerated}");
            if (codeGenPrefs.GenerateApplication)
                Console.WriteLine($"   Application: {stats.ApplicationsGenerated}");
            if (codeGenPrefs.GenerateController)
                Console.WriteLine($"   Controller: {stats.ControllersGenerated}");

            var total = stats.ServicesGenerated + stats.RepositoriesGenerated +
                       stats.ApplicationsGenerated + stats.ControllersGenerated;
            Console.WriteLine($"   总计: {total}");

            _menuService.ShowSuccess("代码生成完成！");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成代码失败: {Message}", ex.Message);
            _menuService.ShowError($"生成代码失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 生成Service层代码
    /// </summary>
    private async Task<int> GenerateServicesAsync(List<Type> entityTypes)
    {
        var templateEngine = new TemplateEngine();
        var projectRoot = GetProjectRoot();
        var templatePath = Path.Combine(projectRoot, "Template", "Service", "ServiceTemplate.txt");
        var template = await templateEngine.ReadTemplateAsync(templatePath);

        int generatedCount = 0;

        foreach (var entityType in entityTypes)
        {
            try
            {
                // 检查是否在排除列表中
                if (_codeGenConfig.ExcludedEntitiesFromCodeGeneration.Contains(entityType.Name))
                {
                    Console.WriteLine($"   跳过Service: {entityType.Name}Service.cs (已排除)");
                    continue;
                }

                var variables = templateEngine.GenerateVariables(entityType);
                var serviceCode = templateEngine.ReplaceVariables(template, variables);

                var outputPath = Path.Combine(_codeGenConfig.ServiceOutputPath, $"{entityType.Name}Service.cs");
                var fullPath = Path.GetFullPath(Path.Combine(projectRoot, outputPath));

                if (await templateEngine.WriteFileAsync(fullPath, serviceCode, false))
                {
                    Console.WriteLine($"   生成Service: {entityType.Name}Service.cs");
                    generatedCount++;
                }
                else
                {
                    Console.WriteLine($"   跳过Service: {entityType.Name}Service.cs (文件已存在)");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   生成Service失败: {entityType.Name} - {ex.Message}");
            }
        }

        Console.WriteLine("   Service层代码生成完成");
        return generatedCount;
    }

    /// <summary>
    /// 生成Repository层代码
    /// </summary>
    private async Task<int> GenerateRepositoriesAsync(List<Type> entityTypes)
    {
        var templateEngine = new TemplateEngine();
        var projectRoot = GetProjectRoot();
        var templatePath = Path.Combine(projectRoot, "Template", "Repository", "RepositoryTemplate.txt");
        var template = await templateEngine.ReadTemplateAsync(templatePath);

        int generatedCount = 0;

        foreach (var entityType in entityTypes)
        {
            try
            {
                // 检查是否在排除列表中
                if (_codeGenConfig.ExcludedEntitiesFromCodeGeneration.Contains(entityType.Name))
                {
                    Console.WriteLine($"   跳过Repository: {entityType.Name}Repository.cs (已排除)");
                    continue;
                }

                var variables = templateEngine.GenerateVariables(entityType);
                // 添加动态查询条件
                variables["QUERY_CONDITIONS"] = templateEngine.GenerateRepositoryQueryConditions(entityType);
                var repositoryCode = templateEngine.ReplaceVariables(template, variables);

                var outputPath = Path.Combine(_codeGenConfig.RepositoryOutputPath, $"{entityType.Name}Repository.cs");
                var fullPath = Path.GetFullPath(Path.Combine(projectRoot, outputPath));

                if (await templateEngine.WriteFileAsync(fullPath, repositoryCode, false))
                {
                    Console.WriteLine($"   生成Repository: {entityType.Name}Repository.cs");
                    generatedCount++;
                }
                else
                {
                    Console.WriteLine($"   跳过Repository: {entityType.Name}Repository.cs (文件已存在)");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   生成Repository失败: {entityType.Name} - {ex.Message}");
            }
        }

        Console.WriteLine("   Repository层代码生成完成");

        // 生成PageListReq文件
        await GeneratePageListReqAsync(entityTypes, templateEngine, projectRoot);

        // 生成Dto文件
        await GenerateDtoAsync(entityTypes, templateEngine, projectRoot);

        return generatedCount;
    }

    /// <summary>
    /// 生成Application层代码
    /// </summary>
    private async Task<int> GenerateApplicationsAsync(List<Type> entityTypes)
    {
        var templateEngine = new TemplateEngine();
        var projectRoot = GetProjectRoot();
        var templatePath = Path.Combine(projectRoot, "Template", "Application", "ApplicationTemplate.txt");
        var template = await templateEngine.ReadTemplateAsync(templatePath);

        int generatedCount = 0;

        foreach (var entityType in entityTypes)
        {
            try
            {
                var variables = templateEngine.GenerateVariables(entityType);
                var applicationCode = templateEngine.ReplaceVariables(template, variables);

                var outputPath = Path.Combine(_codeGenConfig.ApplicationOutputPath, $"{entityType.Name}App.cs");
                var fullPath = Path.GetFullPath(Path.Combine(projectRoot, outputPath));

                if (await templateEngine.WriteFileAsync(fullPath, applicationCode))
                {
                    Console.WriteLine($"   生成Application: {entityType.Name}App.cs");
                    generatedCount++;
                }
                else
                {
                    Console.WriteLine($"   生成Application失败: {entityType.Name}App.cs");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   生成Application失败: {entityType.Name} - {ex.Message}");
            }
        }

        Console.WriteLine("   Application层代码生成完成");
        return generatedCount;
    }

    /// <summary>
    /// 生成Controller层代码
    /// </summary>
    private async Task<int> GenerateControllersAsync(List<Type> entityTypes)
    {
        var templateEngine = new TemplateEngine();
        var projectRoot = GetProjectRoot();
        var templatePath = Path.Combine(projectRoot, "Template", "Controller", "ControllerTemplate.txt");
        var template = await templateEngine.ReadTemplateAsync(templatePath);

        int generatedCount = 0;

        foreach (var entityType in entityTypes)
        {
            try
            {
                var variables = templateEngine.GenerateVariables(entityType);
                var controllerCode = templateEngine.ReplaceVariables(template, variables);

                var outputPath = Path.Combine(_codeGenConfig.ControllerOutputPath, $"{entityType.Name}Controller.cs");
                var fullPath = Path.GetFullPath(Path.Combine(projectRoot, outputPath));

                if (await templateEngine.WriteFileAsync(fullPath, controllerCode))
                {
                    Console.WriteLine($"   生成Controller: {entityType.Name}Controller.cs");
                    generatedCount++;
                }
                else
                {
                    Console.WriteLine($"   生成Controller失败: {entityType.Name}Controller.cs");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   生成Controller失败: {entityType.Name} - {ex.Message}");
            }
        }

        Console.WriteLine("   Controller层代码生成完成");
        return generatedCount;
    }

    /// <summary>
    /// 显示并配置代码生成选项
    /// </summary>
    private async Task ShowAndConfigureCodeGenerationOptionsAsync()
    {
        var prefs = _userPreferencesService.GetCodeGenerationPreferences();

        Console.WriteLine("当前代码生成配置:");
        Console.WriteLine($"   Service: {(prefs.GenerateService ? "是" : "否")}");
        Console.WriteLine($"   Repository: {(prefs.GenerateRepository ? "是" : "否")}");
        Console.WriteLine($"   Application: {(prefs.GenerateApplication ? "是" : "否")}");
        Console.WriteLine($"   Controller: {(prefs.GenerateController ? "是" : "否")}");
        Console.WriteLine();

        var modify = await _menuService.ShowConfirmationAsync("是否要修改代码生成配置？", false);
        if (modify)
        {
            Console.WriteLine("请选择要生成的代码类型（输入 y/n）:");

            Console.Write($"生成Service层代码 (当前: {(prefs.GenerateService ? "y" : "n")})? ");
            var serviceInput = Console.ReadLine()?.Trim().ToLower();
            if (!string.IsNullOrEmpty(serviceInput))
            {
                prefs.GenerateService = serviceInput == "y" || serviceInput == "yes" || serviceInput == "是";
            }

            Console.Write($"生成Repository层代码 (当前: {(prefs.GenerateRepository ? "y" : "n")})? ");
            var repoInput = Console.ReadLine()?.Trim().ToLower();
            if (!string.IsNullOrEmpty(repoInput))
            {
                prefs.GenerateRepository = repoInput == "y" || repoInput == "yes" || repoInput == "是";
            }

            Console.Write($"生成Application层代码 (当前: {(prefs.GenerateApplication ? "y" : "n")})? ");
            var appInput = Console.ReadLine()?.Trim().ToLower();
            if (!string.IsNullOrEmpty(appInput))
            {
                prefs.GenerateApplication = appInput == "y" || appInput == "yes" || appInput == "是";
            }

            Console.Write($"生成Controller层代码 (当前: {(prefs.GenerateController ? "y" : "n")})? ");
            var ctrlInput = Console.ReadLine()?.Trim().ToLower();
            if (!string.IsNullOrEmpty(ctrlInput))
            {
                prefs.GenerateController = ctrlInput == "y" || ctrlInput == "yes" || ctrlInput == "是";
            }

            // 保存用户偏好设置
            await _userPreferencesService.SaveCodeGenerationPreferencesAsync(prefs);

            Console.WriteLine();
            Console.WriteLine("配置已保存");
            Console.WriteLine();
        }
    }

    /// <summary>
    /// 代码生成统计
    /// </summary>
    private class GenerationStats
    {
        public int ServicesGenerated { get; set; }
        public int RepositoriesGenerated { get; set; }
        public int ApplicationsGenerated { get; set; }
        public int ControllersGenerated { get; set; }
    }
}
