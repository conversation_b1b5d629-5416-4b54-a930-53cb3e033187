using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CYSF.Core.Helpers;
using CYSF.Generate.Generator;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace CYSF.Generate.Services
{
    /// <summary>
    /// 实体同步服务 - 解决Code First和Database First之间的一致性问题
    /// </summary>
    public class EntitySyncService
    {
        private readonly ILogger<EntitySyncService> _logger;
        private readonly InteractiveMenuService _menuService;

        public EntitySyncService(ILogger<EntitySyncService> logger, InteractiveMenuService menuService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _menuService = menuService ?? throw new ArgumentNullException(nameof(menuService));
        }

        /// <summary>
        /// 同步数据库表结构到实体类
        /// 从数据库读取完整的表结构信息（包括索引、注释、精度等），更新对应的实体类
        /// </summary>
        public async Task SyncDatabaseToEntitiesAsync()
        {
            try
            {
                Console.WriteLine("🔄 === 同步数据库表结构到实体类 ===");
                Console.WriteLine();

                // 确认操作
                var confirmed = await _menuService.ShowConfirmationAsync(
                    "此操作将根据数据库表结构更新实体类，包括索引、注释、字段精度等信息。确定继续吗？", false);
                if (!confirmed)
                {
                    Console.WriteLine("操作已取消");
                    return;
                }

                using var db = DbContext.Db;
                if (!await CheckDatabaseConnectionAsync(db))
                {
                    return;
                }

                // 获取数据库中的所有表
                var tables = GetDatabaseTables(db);
                Console.WriteLine($"找到 {tables.Count} 个数据库表");
                Console.WriteLine();

                var syncedCount = 0;
                var skippedCount = 0;
                var errorCount = 0;

                foreach (var table in tables)
                {
                    Console.WriteLine($"🔄 同步表: {table.Name} -> {table.EntityName}");

                    try
                    {
                        // 获取表的完整结构信息
                        var tableStructure = await GetCompleteTableStructureAsync(db, table.Name);
                        
                        // 检查实体类是否存在
                        var entityPath = GetEntityFilePath(table.EntityName);
                        if (File.Exists(entityPath))
                        {
                            // 更新现有实体类
                            await UpdateEntityFileAsync(entityPath, table, tableStructure);
                            syncedCount++;
                            Console.WriteLine($"   ✅ 实体类已更新: {table.EntityName}.cs");
                        }
                        else
                        {
                            // 创建新的实体类
                            await CreateEntityFileAsync(table, tableStructure);
                            syncedCount++;
                            Console.WriteLine($"   ✅ 实体类已创建: {table.EntityName}.cs");
                        }
                    }
                    catch (Exception ex)
                    {
                        errorCount++;
                        Console.WriteLine($"   ❌ 同步失败: {ex.Message}");
                        _logger.LogError(ex, "同步表 {TableName} 失败", table.Name);
                    }

                    Console.WriteLine();
                }

                // 输出总结
                Console.WriteLine("📊 同步结果总结:");
                Console.WriteLine($"   ✅ 成功同步: {syncedCount}");
                Console.WriteLine($"   ⏭️ 跳过: {skippedCount}");
                Console.WriteLine($"   ❌ 错误: {errorCount}");
                Console.WriteLine($"   📈 总计: {tables.Count}");

                _menuService.ShowResult("数据库表结构同步", errorCount == 0);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "同步数据库表结构失败: {Message}", ex.Message);
                _menuService.ShowError($"同步数据库表结构失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查数据库连接
        /// </summary>
        private async Task<bool> CheckDatabaseConnectionAsync(ISqlSugarClient db)
        {
            try
            {
                await db.Ado.GetDataTableAsync("SELECT 1");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 数据库连接失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取数据库表列表
        /// </summary>
        private List<DbTableInfo> GetDatabaseTables(ISqlSugarClient db)
        {
            return db.DbMaintenance.GetTableInfoList(false)
                .Select(t => new DbTableInfo
                {
                    Name = t.Name,
                    Comment = t.Description ?? "",
                    EntityName = ConvertToEntityName(t.Name)
                }).ToList();
        }

        /// <summary>
        /// 将表名转换为实体名
        /// </summary>
        private string ConvertToEntityName(string tableName)
        {
            // 移除表前缀
            var prefixes = new[] { "t_", "r_", "rec_", "log_", "tmp_" };
            var entityName = tableName;
            
            foreach (var prefix in prefixes)
            {
                if (entityName.StartsWith(prefix, StringComparison.OrdinalIgnoreCase))
                {
                    entityName = entityName.Substring(prefix.Length);
                    break;
                }
            }

            // 转换为PascalCase
            return ToPascalCase(entityName);
        }

        /// <summary>
        /// 转换为PascalCase
        /// </summary>
        private string ToPascalCase(string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            var parts = input.Split('_', StringSplitOptions.RemoveEmptyEntries);
            var result = new StringBuilder();

            foreach (var part in parts)
            {
                if (part.Length > 0)
                {
                    result.Append(char.ToUpper(part[0]));
                    if (part.Length > 1)
                    {
                        result.Append(part.Substring(1).ToLower());
                    }
                }
            }

            return result.ToString();
        }

        /// <summary>
        /// 获取实体文件路径
        /// </summary>
        private string GetEntityFilePath(string entityName)
        {
            var basePath = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location);
            var solutionRoot = FindSolutionRoot(basePath);
            return Path.Combine(solutionRoot, "src", "Core", "Model", "CYSF.Models", "Entities", $"{entityName}.cs");
        }

        /// <summary>
        /// 查找解决方案根目录
        /// </summary>
        private string FindSolutionRoot(string startPath)
        {
            var current = new DirectoryInfo(startPath);
            while (current != null)
            {
                if (current.GetFiles("*.sln").Any())
                {
                    return current.FullName;
                }
                current = current.Parent;
            }
            return startPath; // 如果找不到，返回起始路径
        }

        /// <summary>
        /// 获取表的完整结构信息
        /// </summary>
        private async Task<TableStructureInfo> GetCompleteTableStructureAsync(ISqlSugarClient db, string tableName)
        {
            var structure = new TableStructureInfo
            {
                TableName = tableName,
                Columns = new List<EnhancedColumnInfo>(),
                Indexes = new List<IndexInfo>(),
                ForeignKeys = new List<ForeignKeyInfo>()
            };

            // 获取列信息
            var columns = db.DbMaintenance.GetColumnInfosByTableName(tableName, false);
            foreach (var col in columns)
            {
                structure.Columns.Add(new EnhancedColumnInfo
                {
                    ColumnName = col.DbColumnName,
                    DataType = col.DataType,
                    Length = col.Length,
                    Precision = col.DecimalDigits,
                    Scale = col.Scale,
                    IsNullable = col.IsNullable,
                    IsPrimaryKey = col.IsPrimarykey,
                    IsIdentity = col.IsIdentity,
                    DefaultValue = col.DefaultValue,
                    Description = col.ColumnDescription ?? ""
                });
            }

            // TODO: 获取索引信息
            // TODO: 获取外键信息

            return structure;
        }

        /// <summary>
        /// 更新现有实体文件
        /// </summary>
        private async Task UpdateEntityFileAsync(string filePath, DbTableInfo table, TableStructureInfo structure)
        {
            // TODO: 实现实体文件更新逻辑
            // 这里需要解析现有的C#文件，更新SqlSugar特性
            await Task.CompletedTask;
        }

        /// <summary>
        /// 创建新的实体文件
        /// </summary>
        private async Task CreateEntityFileAsync(DbTableInfo table, TableStructureInfo structure)
        {
            // TODO: 使用增强的模板创建实体文件
            await Task.CompletedTask;
        }
    }

    /// <summary>
    /// 数据库表信息
    /// </summary>
    public class DbTableInfo
    {
        public string Name { get; set; } = "";
        public string Comment { get; set; } = "";
        public string EntityName { get; set; } = "";
    }

    /// <summary>
    /// 表结构信息
    /// </summary>
    public class TableStructureInfo
    {
        public string TableName { get; set; } = "";
        public List<EnhancedColumnInfo> Columns { get; set; } = new();
        public List<IndexInfo> Indexes { get; set; } = new();
        public List<ForeignKeyInfo> ForeignKeys { get; set; } = new();
    }

    /// <summary>
    /// 增强的列信息
    /// </summary>
    public class EnhancedColumnInfo
    {
        public string ColumnName { get; set; } = "";
        public string DataType { get; set; } = "";
        public int Length { get; set; }
        public int Precision { get; set; }
        public int Scale { get; set; }
        public bool IsNullable { get; set; }
        public bool IsPrimaryKey { get; set; }
        public bool IsIdentity { get; set; }
        public string DefaultValue { get; set; } = "";
        public string Description { get; set; } = "";
    }

    /// <summary>
    /// 索引信息
    /// </summary>
    public class IndexInfo
    {
        public string IndexName { get; set; } = "";
        public List<string> Columns { get; set; } = new();
        public bool IsUnique { get; set; }
        public bool IsPrimaryKey { get; set; }
    }

    /// <summary>
    /// 外键信息
    /// </summary>
    public class ForeignKeyInfo
    {
        public string ForeignKeyName { get; set; } = "";
        public string ColumnName { get; set; } = "";
        public string ReferencedTable { get; set; } = "";
        public string ReferencedColumn { get; set; } = "";
    }
}
