using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace CYSF.Generate.Services;

/// <summary>
/// 主服务类 - 提供交互式菜单和功能调度
/// </summary>
public class MainService
{
    private readonly ILogger<MainService> _logger;
    private readonly InteractiveMenuService _menuService;
    private readonly CodeFirstService _codeFirstService;
    private readonly DatabaseInitializationService _initializationService;

    public MainService(
        ILogger<MainService> logger,
        InteractiveMenuService menuService,
        CodeFirstService codeFirstService,
        DatabaseInitializationService initializationService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _menuService = menuService ?? throw new ArgumentNullException(nameof(menuService));
        _codeFirstService = codeFirstService ?? throw new ArgumentNullException(nameof(codeFirstService));
        _initializationService = initializationService ?? throw new ArgumentNullException(nameof(initializationService));
    }

    /// <summary>
    /// 运行主程序
    /// </summary>
    public async Task RunAsync()
    {
        try
        {
            _logger.LogInformation("🚀 CYSF开发工具集启动");

            // 显示欢迎信息
            ShowWelcomeMessage();

            // 主循环
            while (true)
            {
                try
                {
                    // 显示主菜单
                    var choice = await _menuService.ShowMainMenuAsync();

                    // 处理用户选择
                    var shouldExit = await HandleUserChoiceAsync(choice);
                    if (shouldExit)
                    {
                        break;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "处理用户选择时发生错误: {Message}", ex.Message);
                    Console.WriteLine($"❌ 操作失败: {ex.Message}");
                    Console.WriteLine("按任意键继续...");
                    Console.ReadKey();
                }
            }

            _logger.LogInformation("👋 程序退出");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "主程序运行失败: {Message}", ex.Message);
            throw;
        }
    }

    /// <summary>
    /// 显示欢迎信息
    /// </summary>
    private void ShowWelcomeMessage()
    {
        Console.Clear();
        Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
        Console.WriteLine("║                    CYSF 开发工具集                          ║");
        Console.WriteLine("║                                                              ║");
        Console.WriteLine("║  🔧 Code First   - 根据实体类创建数据库表和初始化数据        ║");
        Console.WriteLine("║  📊 Database First - 根据数据库表生成代码文件               ║");
        Console.WriteLine("║  🗄️ 数据库管理   - 数据库初始化和状态检查                   ║");
        Console.WriteLine("║                                                              ║");
        Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
        Console.WriteLine();
    }

    /// <summary>
    /// 处理用户选择
    /// </summary>
    private async Task<bool> HandleUserChoiceAsync(MenuChoice choice)
    {
        switch (choice)
        {
            case MenuChoice.CodeFirst:
                await HandleCodeFirstAsync();
                break;

            case MenuChoice.GenerateCode:
                await HandleGenerateCodeAsync();
                break;

            case MenuChoice.DatabaseManagement:
                await HandleDatabaseManagementAsync();
                break;

            case MenuChoice.Exit:
                return true;

            default:
                Console.WriteLine("❌ 无效的选择，请重试");
                break;
        }

        return false;
    }

    /// <summary>
    /// 处理Code First操作
    /// </summary>
    private async Task HandleCodeFirstAsync()
    {
        try
        {
            Console.Clear();
            Console.WriteLine("🔧 === Code First 模式 ===");
            Console.WriteLine();

            var subChoice = await _menuService.ShowCodeFirstMenuAsync();

            switch (subChoice)
            {
                case CodeFirstChoice.CreateTables:
                    await _codeFirstService.CreateTablesAsync();
                    break;

                case CodeFirstChoice.UpdateTables:
                    await _codeFirstService.UpdateTablesAsync();
                    break;

                case CodeFirstChoice.InitializeData:
                    await _codeFirstService.InitializeDataAsync();
                    break;

                case CodeFirstChoice.CreateAndInitialize:
                    await _codeFirstService.CreateTablesAndInitializeDataAsync();
                    break;

                case CodeFirstChoice.CreateAndGenerateCode:
                    await _codeFirstService.CreateTablesAndGenerateCodeAsync();
                    break;

                case CodeFirstChoice.ValidateTables:
                    await _codeFirstService.ValidateTablesAsync();
                    break;

                case CodeFirstChoice.Back:
                    return;
            }

            Console.WriteLine();
            Console.WriteLine("按任意键返回主菜单...");
            Console.ReadKey();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Code First操作失败: {Message}", ex.Message);
            Console.WriteLine($"❌ Code First操作失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 处理代码生成操作
    /// </summary>
    private async Task HandleGenerateCodeAsync()
    {
        try
        {
            Console.Clear();
            await _codeFirstService.GenerateCodeAsync();

            Console.WriteLine();
            Console.WriteLine("按任意键返回主菜单...");
            Console.ReadKey();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "代码生成操作失败: {Message}", ex.Message);
            Console.WriteLine($"❌ 操作失败: {ex.Message}");
            Console.WriteLine("按任意键返回主菜单...");
            Console.ReadKey();
        }
    }

    /// <summary>
    /// 处理数据库管理操作
    /// </summary>
    private async Task HandleDatabaseManagementAsync()
    {
        try
        {
            Console.Clear();
            Console.WriteLine("🗄️ === 数据库管理 ===");
            Console.WriteLine();

            var subChoice = await _menuService.ShowDatabaseManagementMenuAsync();

            switch (subChoice)
            {
                case DatabaseManagementChoice.CheckConnection:
                    await _initializationService.CheckDatabaseConnectionAsync();
                    break;

                case DatabaseManagementChoice.ShowStatus:
                    await _initializationService.ShowDatabaseStatusAsync();
                    break;

                case DatabaseManagementChoice.ShowTables:
                    await _initializationService.ShowDatabaseTablesAsync();
                    break;

                case DatabaseManagementChoice.ShowEntityMappings:
                    await _initializationService.ShowEntityTableMappingsAsync();
                    break;

                case DatabaseManagementChoice.GenerateReport:
                    await _initializationService.GenerateDetailedReportAsync();
                    break;

                case DatabaseManagementChoice.Back:
                    return;
            }

            Console.WriteLine();
            Console.WriteLine("按任意键返回主菜单...");
            Console.ReadKey();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "数据库管理操作失败: {Message}", ex.Message);
            Console.WriteLine($"❌ 数据库管理操作失败: {ex.Message}");
        }
    }
}

/// <summary>
/// 主菜单选择
/// </summary>
public enum MenuChoice
{
    CodeFirst = 1,
    GenerateCode = 2,
    DatabaseManagement = 3,
    Exit = 0
}

/// <summary>
/// Code First子菜单选择
/// </summary>
public enum CodeFirstChoice
{
    CreateTables = 1,
    UpdateTables = 2,
    InitializeData = 3,
    CreateAndInitialize = 4,
    CreateAndGenerateCode = 5,
    ValidateTables = 6,
    Back = 0
}

/// <summary>
/// 数据库管理子菜单选择
/// </summary>
public enum DatabaseManagementChoice
{
    CheckConnection = 1,
    ShowStatus = 2,
    ShowTables = 3,
    ShowEntityMappings = 4,
    GenerateReport = 5,
    Back = 0
}
