using System;
using System.IO;
using System.Threading.Tasks;
using CYSF.Generate.Generator;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace CYSF.Generate.Services;

/// <summary>
/// Database First 服务 - 根据数据库表生成代码文件
/// </summary>
public class DatabaseFirstService
{
    private readonly ILogger<DatabaseFirstService> _logger;
    private readonly InteractiveMenuService _menuService;
    private const string SOLUTION_NAME = "CYSF";

    public DatabaseFirstService(ILogger<DatabaseFirstService> logger, InteractiveMenuService menuService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _menuService = menuService ?? throw new ArgumentNullException(nameof(menuService));
    }

    /// <summary>
    /// 生成所有代码文件
    /// </summary>
    public async Task GenerateAllAsync()
    {
        try
        {
            Console.WriteLine("🔄 === 生成所有代码文件 ===");
            Console.WriteLine();

            // 获取项目路径
            var path = GetProjectPath();
            if (path == null)
            {
                _menuService.ShowError("无法找到项目路径");
                return;
            }

            Console.WriteLine($"📁 项目路径: {path}");
            Console.WriteLine();

            // 检查数据库连接
            if (!await CheckDatabaseConnectionAsync())
            {
                return;
            }

            // 获取数据库表信息
            using var db = DbContext.Db;
            var tables = db.DbMaintenance.GetTableInfoList(false);
            if (tables.Count == 0)
            {
                _menuService.ShowWarning("数据库中没有找到任何表");
                return;
            }

            Console.WriteLine($"📋 发现 {tables.Count} 个数据库表:");
            foreach (var table in tables)
            {
                Console.WriteLine($"   - {table.Name} ({table.Description ?? "无描述"})");
            }
            Console.WriteLine();

            // 确认操作
            var confirmed = await _menuService.ShowConfirmationAsync("确定要生成所有代码文件吗？", false);
            if (!confirmed)
            {
                Console.WriteLine("操作已取消");
                return;
            }

            Console.WriteLine("🔄 开始生成代码文件...");
            Console.WriteLine();

            // 调用原有的生成器
            Generator.Generate.GenerateAll(SOLUTION_NAME, path, false, false);

            Console.WriteLine();
            _menuService.ShowSuccess("所有代码文件生成完成！");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成所有代码文件失败: {Message}", ex.Message);
            _menuService.ShowError($"生成所有代码文件失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 仅生成实体类
    /// </summary>
    public async Task GenerateEntitiesAsync()
    {
        try
        {
            Console.WriteLine("📝 === 生成实体类 ===");
            Console.WriteLine();

            var path = GetProjectPath();
            if (path == null)
            {
                _menuService.ShowError("无法找到项目路径");
                return;
            }

            if (!await CheckDatabaseConnectionAsync())
            {
                return;
            }

            using var db = DbContext.Db;
            var tables = db.DbMaintenance.GetTableInfoList(false);
            if (tables.Count == 0)
            {
                _menuService.ShowWarning("数据库中没有找到任何表");
                return;
            }

            var confirmed = await _menuService.ShowConfirmationAsync($"确定要生成 {tables.Count} 个实体类吗？", false);
            if (!confirmed)
            {
                Console.WriteLine("操作已取消");
                return;
            }

            Console.WriteLine("📝 开始生成增强的实体类...");

            // 使用增强的生成器，生成包含完整SqlSugar特性的实体类
            Generator.Generate.GenerateEntitiesOnly(SOLUTION_NAME, path);

            _menuService.ShowSuccess("增强的实体类生成完成！");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成实体类失败: {Message}", ex.Message);
            _menuService.ShowError($"生成实体类失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 仅生成仓储类
    /// </summary>
    public async Task GenerateRepositoriesAsync()
    {
        try
        {
            Console.WriteLine("🗃️ === 生成仓储类 ===");
            Console.WriteLine();

            var path = GetProjectPath();
            if (path == null)
            {
                _menuService.ShowError("无法找到项目路径");
                return;
            }

            if (!await CheckDatabaseConnectionAsync())
            {
                return;
            }

            using var db = DbContext.Db;
            var tables = db.DbMaintenance.GetTableInfoList(false);
            if (tables.Count == 0)
            {
                _menuService.ShowWarning("数据库中没有找到任何表");
                return;
            }

            var confirmed = await _menuService.ShowConfirmationAsync($"确定要生成 {tables.Count} 个仓储类吗？", false);
            if (!confirmed)
            {
                Console.WriteLine("操作已取消");
                return;
            }

            Console.WriteLine("🗃️ 开始生成仓储类...");

            // 调用生成器，这里暂时使用完整生成
            Generator.Generate.GenerateAll(SOLUTION_NAME, path, false, false);

            _menuService.ShowSuccess("仓储类生成完成！");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成仓储类失败: {Message}", ex.Message);
            _menuService.ShowError($"生成仓储类失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 仅生成服务类
    /// </summary>
    public async Task GenerateServicesAsync()
    {
        try
        {
            Console.WriteLine("⚙️ === 生成服务类 ===");
            Console.WriteLine();

            var path = GetProjectPath();
            if (path == null)
            {
                _menuService.ShowError("无法找到项目路径");
                return;
            }

            if (!await CheckDatabaseConnectionAsync())
            {
                return;
            }

            using var db = DbContext.Db;
            var tables = db.DbMaintenance.GetTableInfoList(false);
            if (tables.Count == 0)
            {
                _menuService.ShowWarning("数据库中没有找到任何表");
                return;
            }

            var confirmed = await _menuService.ShowConfirmationAsync($"确定要生成 {tables.Count} 个服务类吗？", false);
            if (!confirmed)
            {
                Console.WriteLine("操作已取消");
                return;
            }

            Console.WriteLine("⚙️ 开始生成服务类...");

            Generator.Generate.GenerateAll(SOLUTION_NAME, path, false, false);

            _menuService.ShowSuccess("服务类生成完成！");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成服务类失败: {Message}", ex.Message);
            _menuService.ShowError($"生成服务类失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 仅生成控制器
    /// </summary>
    public async Task GenerateControllersAsync()
    {
        try
        {
            Console.WriteLine("🎮 === 生成控制器 ===");
            Console.WriteLine();

            var path = GetProjectPath();
            if (path == null)
            {
                _menuService.ShowError("无法找到项目路径");
                return;
            }

            if (!await CheckDatabaseConnectionAsync())
            {
                return;
            }

            using var db = DbContext.Db;
            var tables = db.DbMaintenance.GetTableInfoList(false);
            if (tables.Count == 0)
            {
                _menuService.ShowWarning("数据库中没有找到任何表");
                return;
            }

            var confirmed = await _menuService.ShowConfirmationAsync($"确定要生成 {tables.Count} 个控制器吗？", false);
            if (!confirmed)
            {
                Console.WriteLine("操作已取消");
                return;
            }

            Console.WriteLine("🎮 开始生成控制器...");

            // 启用控制器生成
            Generator.Generate.GenerateAll(SOLUTION_NAME, path, true, false);

            _menuService.ShowSuccess("控制器生成完成！");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成控制器失败: {Message}", ex.Message);
            _menuService.ShowError($"生成控制器失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 检查数据库连接
    /// </summary>
    private async Task<bool> CheckDatabaseConnectionAsync()
    {
        try
        {
            Console.WriteLine("🔗 检查数据库连接...");
            using var db = DbContext.Db;
            await db.Ado.GetDataTableAsync("SELECT 1");
            Console.WriteLine("✅ 数据库连接正常");
            Console.WriteLine();
            return true;
        }
        catch (Exception ex)
        {
            _menuService.ShowError($"数据库连接失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 获取项目路径
    /// </summary>
    private string GetProjectPath()
    {
        try
        {
            var currentDirectory = Directory.GetCurrentDirectory();

            // 方式1: 相对路径查找
            var pathOption1 = Path.Combine(currentDirectory, "..", "..", "..", "..");
            if (Directory.Exists(pathOption1))
            {
                return Path.GetFullPath(pathOption1);
            }

            // 方式2: 向上查找项目目录
            var searchDir = new DirectoryInfo(currentDirectory);
            while (searchDir != null && searchDir.Name != SOLUTION_NAME)
            {
                searchDir = searchDir.Parent;
            }
            if (searchDir != null)
            {
                return Path.Combine(searchDir.FullName, "src", "Core");
            }

            // 方式3: 从根目录查找
            var rootDir = Directory.GetDirectoryRoot(currentDirectory);
            var pathOption3 = Path.Combine(rootDir, $"Solution/{SOLUTION_NAME}/src/Core");
            if (Directory.Exists(pathOption3))
            {
                return pathOption3;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取项目路径失败: {Message}", ex.Message);
            return null;
        }
    }
}
