using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using CYSF.Models.Entities;
using SqlSugar;

namespace CYSF.Generate.Services;

/// <summary>
/// 模板引擎服务
/// </summary>
public class TemplateEngine
{
    /// <summary>
    /// 模板变量替换
    /// </summary>
    /// <param name="template">模板内容</param>
    /// <param name="variables">变量字典</param>
    /// <returns>替换后的内容</returns>
    public string ReplaceVariables(string template, Dictionary<string, string> variables)
    {
        var result = template;
        
        foreach (var variable in variables)
        {
            result = result.Replace($"{{{variable.Key}}}", variable.Value);
        }
        
        return result;
    }

    /// <summary>
    /// 从实体类型生成变量字典
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <returns>变量字典</returns>
    public Dictionary<string, string> GenerateVariables(Type entityType)
    {
        var entityName = entityType.Name;
        var lowerEntityName = char.ToLower(entityName[0]) + entityName.Substring(1);
        
        // 获取表注释
        var tableComment = GetTableComment(entityType);
        
        var variables = new Dictionary<string, string>
        {
            ["ENTITY_NAME"] = entityName,
            ["LOWER_ENTITY_NAME"] = lowerEntityName,
            ["TABLE_COMMENT"] = tableComment,
            ["NAMESPACE"] = entityType.Namespace ?? "CYSF.Models.Entities"
        };

        return variables;
    }

    /// <summary>
    /// 生成过滤属性（用于PageListReq）
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <returns>过滤属性代码</returns>
    public string GenerateFilterProperties(Type entityType)
    {
        var sb = new StringBuilder();
        var properties = entityType.GetProperties(BindingFlags.Public | BindingFlags.Instance);
        bool hasCustomProperties = false;

        foreach (var prop in properties)
        {
            // 跳过基础字段
            if (IsBaseProperty(prop.Name))
                continue;

            var propType = prop.PropertyType;
            var propName = prop.Name;
            var comment = GetPropertyComment(prop);

            // 根据属性类型生成不同的过滤条件
            if (propType == typeof(string))
            {
                sb.AppendLine($"    /// <summary>");
                sb.AppendLine($"    /// {comment}");
                sb.AppendLine($"    /// </summary>");
                sb.AppendLine($"    public string? {propName} {{ get; set; }}");
                sb.AppendLine();
                hasCustomProperties = true;
            }
            else if (IsNumericType(propType))
            {
                sb.AppendLine($"    /// <summary>");
                sb.AppendLine($"    /// {comment}");
                sb.AppendLine($"    /// </summary>");
                sb.AppendLine($"    public {GetNullableTypeName(propType)}? {propName} {{ get; set; }}");
                sb.AppendLine();
                hasCustomProperties = true;
            }
            else if (propType == typeof(DateTime) || propType == typeof(DateTime?))
            {
                // DateTime类型生成范围查询
                sb.AppendLine($"    /// <summary>");
                sb.AppendLine($"    /// {comment}范围");
                sb.AppendLine($"    /// </summary>");
                sb.AppendLine($"    public List<DateTime>? {propName} {{ get; set; }}");
                sb.AppendLine();
                hasCustomProperties = true;
            }
        }

        // 如果没有自定义属性，至少添加主键作为查询条件
        if (!hasCustomProperties)
        {
            sb.AppendLine($"    /// <summary>");
            sb.AppendLine($"    /// 主键ID");
            sb.AppendLine($"    /// </summary>");
            sb.AppendLine($"    public int? Id {{ get; set; }}");
            sb.AppendLine();
        }

        return sb.ToString().TrimEnd();
    }

    /// <summary>
    /// 生成Dto属性（用于响应模型）
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <returns>Dto属性代码</returns>
    public string GenerateDtoProperties(Type entityType)
    {
        var sb = new StringBuilder();

        // 这里可以添加一些额外的属性，比如关联表的名称等
        // 目前先返回空，表示直接继承Entity的所有属性
        sb.AppendLine("    // 继承Entity的所有属性");
        sb.AppendLine("    // 可以在这里添加额外的显示属性");

        return sb.ToString().TrimEnd();
    }

    /// <summary>
    /// 生成Repository查询条件（用于GetPageListAsync）
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <returns>查询条件代码</returns>
    public string GenerateRepositoryQueryConditions(Type entityType)
    {
        var sb = new StringBuilder();
        var properties = entityType.GetProperties(BindingFlags.Public | BindingFlags.Instance);
        bool hasCustomConditions = false;

        foreach (var prop in properties)
        {
            // 跳过基础字段
            if (IsBaseProperty(prop.Name))
                continue;

            var propType = prop.PropertyType;
            var propName = prop.Name;

            // 根据属性类型生成不同的查询条件
            if (propType == typeof(string))
            {
                sb.AppendLine($"                .WhereIF(!string.IsNullOrEmpty(req.Filters.{propName}), t => t.{propName}.Contains(req.Filters.{propName}))");
                hasCustomConditions = true;
            }
            else if (IsNumericType(propType))
            {
                sb.AppendLine($"                .WhereIF(req.Filters.{propName}.HasValue, t => t.{propName} == req.Filters.{propName}.Value)");
                hasCustomConditions = true;
            }
            else if (propType == typeof(DateTime) || propType == typeof(DateTime?))
            {
                // DateTime类型生成范围查询
                sb.AppendLine($"                .WhereIF(req.Filters.{propName} != null && req.Filters.{propName}.Count > 0, t => t.{propName} >= req.Filters.{propName}[0])");
                sb.AppendLine($"                .WhereIF(req.Filters.{propName} != null && req.Filters.{propName}.Count > 1, t => t.{propName} <= req.Filters.{propName}[1])");
                hasCustomConditions = true;
            }
        }

        // 如果没有自定义条件，至少添加主键查询
        if (!hasCustomConditions)
        {
            sb.AppendLine($"                .WhereIF(req.Filters.Id.HasValue, t => t.Id == req.Filters.Id.Value)");
        }

        return sb.ToString().TrimEnd();
    }

    /// <summary>
    /// 获取表注释
    /// </summary>
    /// <param name="entityType">实体类型</param>
    /// <returns>表注释</returns>
    private string GetTableComment(Type entityType)
    {
        // 尝试从SugarTable特性获取注释
        var sugarTableAttr = entityType.GetCustomAttributes(false)
            .FirstOrDefault(attr => attr.GetType().Name == "SugarTableAttribute");
        
        if (sugarTableAttr != null)
        {
            var tableNameProp = sugarTableAttr.GetType().GetProperty("TableDescription");
            if (tableNameProp != null)
            {
                var description = tableNameProp.GetValue(sugarTableAttr)?.ToString();
                if (!string.IsNullOrEmpty(description))
                    return description;
            }
        }

        // 如果没有找到注释，使用实体名称
        return entityType.Name;
    }

    /// <summary>
    /// 获取属性注释
    /// </summary>
    /// <param name="property">属性信息</param>
    /// <returns>属性注释</returns>
    private string GetPropertyComment(PropertyInfo property)
    {
        // 尝试从SugarColumn特性获取注释
        var sugarColumnAttr = property.GetCustomAttributes(false)
            .FirstOrDefault(attr => attr.GetType().Name == "SugarColumnAttribute");
        
        if (sugarColumnAttr != null)
        {
            var columnDescriptionProp = sugarColumnAttr.GetType().GetProperty("ColumnDescription");
            if (columnDescriptionProp != null)
            {
                var description = columnDescriptionProp.GetValue(sugarColumnAttr)?.ToString();
                if (!string.IsNullOrEmpty(description))
                    return description;
            }
        }

        // 如果没有找到注释，使用属性名称
        return property.Name;
    }

    /// <summary>
    /// 判断是否为基础属性（Id, CreateTime等）
    /// </summary>
    /// <param name="propertyName">属性名称</param>
    /// <returns>是否为基础属性</returns>
    private bool IsBaseProperty(string propertyName)
    {
        var baseProperties = new[] { "Id", "CreateTime", "UpdateTime", "CreatorId", "IsDeleted" };
        return baseProperties.Contains(propertyName);
    }

    /// <summary>
    /// 判断是否为数值类型
    /// </summary>
    /// <param name="type">类型</param>
    /// <returns>是否为数值类型</returns>
    private bool IsNumericType(Type type)
    {
        var numericTypes = new[]
        {
            typeof(int), typeof(long), typeof(short), typeof(byte),
            typeof(uint), typeof(ulong), typeof(ushort), typeof(sbyte),
            typeof(decimal), typeof(double), typeof(float),
            typeof(int?), typeof(long?), typeof(short?), typeof(byte?),
            typeof(uint?), typeof(ulong?), typeof(ushort?), typeof(sbyte?),
            typeof(decimal?), typeof(double?), typeof(float?)
        };
        
        return numericTypes.Contains(type);
    }

    /// <summary>
    /// 获取可空类型名称
    /// </summary>
    /// <param name="type">类型</param>
    /// <returns>类型名称</returns>
    private string GetNullableTypeName(Type type)
    {
        if (type == typeof(int) || type == typeof(int?)) return "int";
        if (type == typeof(long) || type == typeof(long?)) return "long";
        if (type == typeof(short) || type == typeof(short?)) return "short";
        if (type == typeof(byte) || type == typeof(byte?)) return "byte";
        if (type == typeof(decimal) || type == typeof(decimal?)) return "decimal";
        if (type == typeof(double) || type == typeof(double?)) return "double";
        if (type == typeof(float) || type == typeof(float?)) return "float";
        
        return type.Name;
    }

    /// <summary>
    /// 读取模板文件
    /// </summary>
    /// <param name="templatePath">模板文件路径</param>
    /// <returns>模板内容</returns>
    public async Task<string> ReadTemplateAsync(string templatePath)
    {
        if (!File.Exists(templatePath))
            throw new FileNotFoundException($"模板文件不存在: {templatePath}");
            
        return await File.ReadAllTextAsync(templatePath, Encoding.UTF8);
    }

    /// <summary>
    /// 写入生成的文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="content">文件内容</param>
    /// <param name="overwrite">是否覆盖现有文件</param>
    /// <returns>是否成功</returns>
    public async Task<bool> WriteFileAsync(string filePath, string content, bool overwrite = false)
    {
        try
        {
            // 检查文件是否已存在
            if (File.Exists(filePath) && !overwrite)
            {
                return false; // 文件已存在且不允许覆盖
            }

            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            await File.WriteAllTextAsync(filePath, content, Encoding.UTF8);
            return true;
        }
        catch
        {
            return false;
        }
    }
}
