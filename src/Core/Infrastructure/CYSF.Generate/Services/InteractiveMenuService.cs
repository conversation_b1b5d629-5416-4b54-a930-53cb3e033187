using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace CYSF.Generate.Services;

/// <summary>
/// 交互式菜单服务
/// </summary>
public class InteractiveMenuService
{
    private readonly ILogger<InteractiveMenuService> _logger;

    public InteractiveMenuService(ILogger<InteractiveMenuService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 显示主菜单
    /// </summary>
    public async Task<MenuChoice> ShowMainMenuAsync()
    {
        Console.WriteLine("┌─────────────────────────────────────────────────────────────┐");
        Console.WriteLine("│                        主菜单                              │");
        Console.WriteLine("├─────────────────────────────────────────────────────────────┤");
        Console.WriteLine("│  1. 🔧 Code First      - 根据实体类创建表和生成代码        │");
        Console.WriteLine("│  2. 📝 代码生成        - 从实体类生成Service/Repository等  │");
        Console.WriteLine("│  3. 🗄️ 数据库管理      - 数据库状态检查和管理              │");
        Console.WriteLine("│  0. 🚪 退出程序                                            │");
        Console.WriteLine("└─────────────────────────────────────────────────────────────┘");
        Console.WriteLine();

        return await GetUserChoiceAsync<MenuChoice>("请选择操作");
    }

    /// <summary>
    /// 显示Code First子菜单
    /// </summary>
    public async Task<CodeFirstChoice> ShowCodeFirstMenuAsync()
    {
        Console.WriteLine("┌─────────────────────────────────────────────────────────────┐");
        Console.WriteLine("│                    Code First 菜单                         │");
        Console.WriteLine("├─────────────────────────────────────────────────────────────┤");
        Console.WriteLine("│  1. 🔨 创建数据库表    - 根据实体类创建表结构               │");
        Console.WriteLine("│  2. 🔄 更新表结构      - 根据实体类更新现有表结构           │");
        Console.WriteLine("│  3. 🌱 初始化基础数据  - 创建默认租户、用户等基础数据       │");
        Console.WriteLine("│  4. 🚀 创建表+初始化   - 一键完成表创建和数据初始化         │");
        Console.WriteLine("│  5. 🎯 创建表+生成代码 - 创建表并生成完整代码               │");
        Console.WriteLine("│  6. ✅ 验证表完整性    - 检查所有实体对应的表是否存在       │");
        Console.WriteLine("│  0. ⬅️ 返回主菜单                                          │");
        Console.WriteLine("└─────────────────────────────────────────────────────────────┘");
        Console.WriteLine();

        return await GetUserChoiceAsync<CodeFirstChoice>("请选择操作");
    }

    /// <summary>
    /// 显示数据库管理子菜单
    /// </summary>
    public async Task<DatabaseManagementChoice> ShowDatabaseManagementMenuAsync()
    {
        Console.WriteLine("┌─────────────────────────────────────────────────────────────┐");
        Console.WriteLine("│                    数据库管理菜单                          │");
        Console.WriteLine("├─────────────────────────────────────────────────────────────┤");
        Console.WriteLine("│  1. 🔗 检查数据库连接  - 测试数据库连接状态                 │");
        Console.WriteLine("│  2. 📊 显示数据库状态  - 查看数据库初始化状态               │");
        Console.WriteLine("│  3. 📋 显示数据库表    - 列出数据库中的所有表               │");
        Console.WriteLine("│  4. 🔗 显示实体映射    - 查看实体类与表的映射关系           │");
        Console.WriteLine("│  5. 📝 生成详细报告    - 生成完整的数据库状态报告           │");
        Console.WriteLine("│  0. ⬅️ 返回主菜单                                          │");
        Console.WriteLine("└─────────────────────────────────────────────────────────────┘");
        Console.WriteLine();

        return await GetUserChoiceAsync<DatabaseManagementChoice>("请选择操作");
    }

    /// <summary>
    /// 获取用户选择
    /// </summary>
    private Task<T> GetUserChoiceAsync<T>(string prompt) where T : Enum
    {
        while (true)
        {
            Console.Write($"{prompt} (输入数字): ");
            var input = Console.ReadLine();

            if (string.IsNullOrWhiteSpace(input))
            {
                Console.WriteLine("❌ 输入不能为空，请重试");
                continue;
            }

            if (int.TryParse(input.Trim(), out var choice))
            {
                if (Enum.IsDefined(typeof(T), choice))
                {
                    Console.WriteLine();
                    return Task.FromResult((T)Enum.ToObject(typeof(T), choice));
                }
            }

            Console.WriteLine("❌ 无效的选择，请输入正确的数字");
        }
    }

    /// <summary>
    /// 显示确认对话框
    /// </summary>
    public async Task<bool> ShowConfirmationAsync(string message, bool defaultValue = false)
    {
        var defaultText = defaultValue ? "Y/n" : "y/N";
        Console.Write($"{message} ({defaultText}): ");
        
        var input = Console.ReadLine()?.Trim().ToLowerInvariant();
        
        if (string.IsNullOrEmpty(input))
        {
            return defaultValue;
        }

        return input == "y" || input == "yes" || input == "是";
    }

    /// <summary>
    /// 显示进度信息
    /// </summary>
    public void ShowProgress(string message, int current, int total)
    {
        var percentage = (double)current / total * 100;
        var progressBar = new string('█', (int)(percentage / 5));
        var emptyBar = new string('░', 20 - progressBar.Length);
        
        Console.Write($"\r{message} [{progressBar}{emptyBar}] {percentage:F1}% ({current}/{total})");
        
        if (current == total)
        {
            Console.WriteLine();
        }
    }

    /// <summary>
    /// 显示操作结果
    /// </summary>
    public void ShowResult(string operation, bool success, string message = null)
    {
        var icon = success ? "✅" : "❌";
        var status = success ? "成功" : "失败";
        
        Console.WriteLine($"{icon} {operation} {status}");
        
        if (!string.IsNullOrEmpty(message))
        {
            Console.WriteLine($"   {message}");
        }
    }

    /// <summary>
    /// 显示信息框
    /// </summary>
    public void ShowInfoBox(string title, string[] lines)
    {
        var maxLength = Math.Max(title.Length, lines.Max(l => l.Length)) + 4;
        var border = new string('─', maxLength);
        
        Console.WriteLine($"┌{border}┐");
        Console.WriteLine($"│ {title.PadRight(maxLength - 2)} │");
        Console.WriteLine($"├{border}┤");
        
        foreach (var line in lines)
        {
            Console.WriteLine($"│ {line.PadRight(maxLength - 2)} │");
        }
        
        Console.WriteLine($"└{border}┘");
    }

    /// <summary>
    /// 显示警告信息
    /// </summary>
    public void ShowWarning(string message)
    {
        Console.ForegroundColor = ConsoleColor.Yellow;
        Console.WriteLine($"⚠️ 警告: {message}");
        Console.ResetColor();
    }

    /// <summary>
    /// 显示错误信息
    /// </summary>
    public void ShowError(string message)
    {
        Console.ForegroundColor = ConsoleColor.Red;
        Console.WriteLine($"❌ 错误: {message}");
        Console.ResetColor();
    }

    /// <summary>
    /// 显示成功信息
    /// </summary>
    public void ShowSuccess(string message)
    {
        Console.ForegroundColor = ConsoleColor.Green;
        Console.WriteLine($"✅ {message}");
        Console.ResetColor();
    }

    /// <summary>
    /// 等待用户按键
    /// </summary>
    public void WaitForKeyPress(string message = "按任意键继续...")
    {
        Console.WriteLine();
        Console.WriteLine(message);
        Console.ReadKey();
    }
}
