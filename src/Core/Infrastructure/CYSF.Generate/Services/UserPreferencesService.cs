using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace CYSF.Generate.Services
{
    /// <summary>
    /// 用户偏好设置服务 - 保存用户最后一次的交互选择
    /// </summary>
    public class UserPreferencesService
    {
        private readonly ILogger<UserPreferencesService> _logger;
        private readonly string _preferencesFilePath;
        private UserPreferences _preferences;

        public UserPreferencesService(ILogger<UserPreferencesService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _preferencesFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "user-preferences.json");
            _preferences = new UserPreferences();
            LoadPreferences();
        }

        /// <summary>
        /// 获取代码生成偏好设置
        /// </summary>
        public CodeGenerationPreferences GetCodeGenerationPreferences()
        {
            return _preferences.CodeGeneration;
        }

        /// <summary>
        /// 保存代码生成偏好设置
        /// </summary>
        public async Task SaveCodeGenerationPreferencesAsync(CodeGenerationPreferences preferences)
        {
            _preferences.CodeGeneration = preferences;
            await SavePreferencesAsync();
        }

        /// <summary>
        /// 获取表管理偏好设置
        /// </summary>
        public TableManagementPreferences GetTableManagementPreferences()
        {
            return _preferences.TableManagement;
        }

        /// <summary>
        /// 保存表管理偏好设置
        /// </summary>
        public async Task SaveTableManagementPreferencesAsync(TableManagementPreferences preferences)
        {
            _preferences.TableManagement = preferences;
            await SavePreferencesAsync();
        }

        /// <summary>
        /// 加载偏好设置
        /// </summary>
        private void LoadPreferences()
        {
            try
            {
                if (File.Exists(_preferencesFilePath))
                {
                    var json = File.ReadAllText(_preferencesFilePath);
                    var preferences = JsonSerializer.Deserialize<UserPreferences>(json);
                    if (preferences != null)
                    {
                        _preferences = preferences;
                        _logger.LogInformation("用户偏好设置加载成功");
                    }
                }
                else
                {
                    _logger.LogInformation("偏好设置文件不存在，使用默认设置");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "加载用户偏好设置失败，使用默认设置");
                _preferences = new UserPreferences();
            }
        }

        /// <summary>
        /// 保存偏好设置
        /// </summary>
        private async Task SavePreferencesAsync()
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };

                var json = JsonSerializer.Serialize(_preferences, options);
                await File.WriteAllTextAsync(_preferencesFilePath, json);
                _logger.LogInformation("用户偏好设置保存成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存用户偏好设置失败");
            }
        }

        /// <summary>
        /// 重置偏好设置为默认值
        /// </summary>
        public async Task ResetPreferencesAsync()
        {
            _preferences = new UserPreferences();
            await SavePreferencesAsync();
            _logger.LogInformation("用户偏好设置已重置为默认值");
        }
    }

    /// <summary>
    /// 用户偏好设置
    /// </summary>
    public class UserPreferences
    {
        /// <summary>
        /// 代码生成偏好设置
        /// </summary>
        public CodeGenerationPreferences CodeGeneration { get; set; } = new();

        /// <summary>
        /// 表管理偏好设置
        /// </summary>
        public TableManagementPreferences TableManagement { get; set; } = new();
    }

    /// <summary>
    /// 代码生成偏好设置
    /// </summary>
    public class CodeGenerationPreferences
    {
        /// <summary>
        /// 是否生成Service
        /// </summary>
        public bool GenerateService { get; set; } = true;

        /// <summary>
        /// 是否生成Repository
        /// </summary>
        public bool GenerateRepository { get; set; } = true;

        /// <summary>
        /// 是否生成Application
        /// </summary>
        public bool GenerateApplication { get; set; } = true;

        /// <summary>
        /// 是否生成Controller
        /// </summary>
        public bool GenerateController { get; set; } = true;

        /// <summary>
        /// 最后选择的实体列表（用于记住用户选择）
        /// </summary>
        public string[] SelectedEntities { get; set; } = Array.Empty<string>();
    }

    /// <summary>
    /// 表管理偏好设置
    /// </summary>
    public class TableManagementPreferences
    {
        /// <summary>
        /// 是否自动删除孤儿表
        /// </summary>
        public bool AutoDeleteOrphanTables { get; set; } = false;

        /// <summary>
        /// 删除前确认
        /// </summary>
        public bool ConfirmBeforeDelete { get; set; } = true;

        /// <summary>
        /// 删除前备份
        /// </summary>
        public bool BackupBeforeDelete { get; set; } = true;
    }
}
