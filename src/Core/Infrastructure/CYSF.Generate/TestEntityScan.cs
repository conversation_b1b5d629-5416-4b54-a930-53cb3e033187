using System;
using System.Linq;
using System.Reflection;
using CYSF.Models.Entities;
using SqlSugar;

namespace CYSF.Generate
{
    /// <summary>
    /// 测试实体类扫描
    /// </summary>
    public class TestEntityScan
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("=== 实体类扫描测试 ===");
            Console.WriteLine();

            try
            {
                // 获取Models程序集
                var modelsAssembly = Assembly.GetAssembly(typeof(Tenant));
                Console.WriteLine($"✅ 成功加载程序集: {modelsAssembly?.FullName}");
                Console.WriteLine();

                if (modelsAssembly == null)
                {
                    Console.WriteLine("❌ 无法获取Models程序集");
                    return;
                }

                // 获取所有类型
                var allTypes = modelsAssembly.GetTypes();
                Console.WriteLine($"📋 程序集中所有类型数量: {allTypes.Length}");
                Console.WriteLine();

                // 获取CYSF.Models命名空间下的类型
                var modelsTypes = allTypes
                    .Where(t => t.Namespace?.StartsWith("CYSF.Models") == true)
                    .ToList();

                Console.WriteLine($"📋 CYSF.Models命名空间下的类型 ({modelsTypes.Count}):");
                foreach (var type in modelsTypes)
                {
                    Console.WriteLine($"   - {type.FullName}");
                }
                Console.WriteLine();

                // 获取实体类型
                var entityTypes = allTypes
                    .Where(t => t.Namespace == "CYSF.Models.Entities" &&
                               t.IsClass &&
                               !t.IsAbstract)
                    .ToList();

                Console.WriteLine($"📋 CYSF.Models.Entities命名空间下的类 ({entityTypes.Count}):");
                foreach (var type in entityTypes)
                {
                    var hasSugarTable = type.GetCustomAttribute<SugarTableAttribute>() != null;
                    Console.WriteLine($"   - {type.Name} (SugarTable: {(hasSugarTable ? "✅" : "❌")})");
                }
                Console.WriteLine();

                // 获取有SugarTable特性的实体
                var sugarEntityTypes = entityTypes
                    .Where(t => t.GetCustomAttribute<SugarTableAttribute>() != null)
                    .ToList();

                Console.WriteLine($"📋 有SugarTable特性的实体类 ({sugarEntityTypes.Count}):");
                foreach (var type in sugarEntityTypes)
                {
                    var attr = type.GetCustomAttribute<SugarTableAttribute>();
                    Console.WriteLine($"   - {type.Name} → 表名: {attr?.TableName}");
                }

            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 扫描失败: {ex.Message}");
                Console.WriteLine($"   详细信息: {ex}");
            }

            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
