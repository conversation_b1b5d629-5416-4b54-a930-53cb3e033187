using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using CYSF.Core.Extensions;
using CYSF.Core.Helpers;

namespace CYSF.Generate.Generator
{
    /// <summary>
    /// 统一代码生成器
    /// 基于数据库表结构自动生成实体类、仓储类、服务类和枚举类
    /// 采用单次循环优化性能，避免多次查询数据库
    /// </summary>
    /// <remarks>
    /// 生成策略：
    /// - 实体类：每次都删除重新生成，确保与数据库结构同步
    /// - 仓储类：仅在文件不存在时生成，保护自定义代码
    /// - 服务类：仅在文件不存在时生成，保护业务逻辑
    /// - 枚举类：基于字段注释，仅在文件不存在时生成
    /// </remarks>
    public static class Generate
    {
        #region 公共方法

        /// <summary>
        /// 生成所有代码文件的主入口方法
        /// </summary>
        /// <param name="solutionName">解决方案名称，用于命名空间和路径构建</param>
        /// <param name="path">生成文件的根路径</param>
        /// <param name="generateControllers">是否生成Controller</param>
        /// <param name="generateApplications">是否生成Application</param>
        /// <exception cref="Exception">数据库连接失败或模板文件缺失时抛出异常</exception>
        public static void GenerateAll(string solutionName, string path = "Generated", bool generateControllers = false, bool generateApplications = false)
        {
            try
            {
                Console.WriteLine("正在获取数据库表信息...");

                // 获取数据库表信息
                var tables = GetDatabaseTables();
                Console.WriteLine($"找到 {tables.Count} 个数据库表");

                // 加载所有模板文件
                var templates = LoadTemplates();
                Console.WriteLine("模板文件加载完成");

                // 删除所有现有的实体文件（实体总是重新生成）
                DeleteAllEntities(path, solutionName);

                // 初始化统计信息
                var stats = new GenerationStats();
                var generatedEnums = new List<string>(); // 防止重复生成相同枚举

                Console.WriteLine("开始生成代码文件...");
                Console.WriteLine();

                // 一次循环处理所有表，优化性能
                foreach (var table in tables)
                {
                    Console.WriteLine($"处理表: {table.Name} -> {table.EntityName}");

                    // 获取表的列信息（包含字段类型、注释等详细信息）
                    var columns = GetTableColumns(table.Name);

                    // 1. 生成实体类（总是重新生成）
                    GenerateEntity(table, columns, templates.EntityTemplate, path, solutionName);
                    stats.EntitiesGenerated++;

                    // 2. 生成仓储类（文件不存在时生成，保护自定义代码）
                    if (GenerateRepository(table, templates.RepositoryTemplate, path, solutionName))
                        stats.RepositoriesGenerated++;
                    else
                        stats.RepositoriesSkipped++;

                    // 3. 生成服务类（文件不存在时生成，保护业务逻辑）
                    if (GenerateService(table, templates.ServiceTemplate, path, solutionName))
                        stats.ServicesGenerated++;
                    else
                        stats.ServicesSkipped++;

                    // 4. 生成枚举类（基于列注释，文件不存在时生成）
                    var enumsGenerated = GenerateEnums(table, columns, templates.EnumTemplate, path, solutionName, generatedEnums);
                    stats.EnumsGenerated += enumsGenerated;

                    // 5. 生成Controller（文件不存在时生成，保护自定义代码）
                    if (generateControllers)
                    {
                        if (GenerateController(table, templates.ControllerTemplate, path, solutionName))
                            stats.ControllersGenerated++;
                        else
                            stats.ControllersSkipped++;
                    }

                    // 6. 生成Application（文件不存在时生成，保护业务逻辑）
                    if (generateApplications)
                    {
                        if (GenerateApplication(table, templates.ApplicationTemplate, path, solutionName))
                            stats.ApplicationsGenerated++;
                        else
                            stats.ApplicationsSkipped++;
                    }

                    Console.WriteLine(); // 添加空行分隔
                }

                // 输出最终统计信息
                PrintGenerationStats(stats);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"代码生成过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细错误信息: {ex}");
                throw;
            }
        }

        /// <summary>
        /// 仅生成实体类的方法
        /// </summary>
        /// <param name="solutionName">解决方案名称</param>
        /// <param name="path">生成文件的根路径</param>
        public static void GenerateEntitiesOnly(string solutionName, string path = "Generated")
        {
            try
            {
                Console.WriteLine("正在获取数据库表信息...");

                // 获取数据库表信息
                var tables = GetDatabaseTables();
                Console.WriteLine($"找到 {tables.Count} 个数据库表");

                // 加载实体模板
                var entityTemplate = LoadTemplate("Entity.txt");
                Console.WriteLine("实体模板加载完成");

                // 删除所有现有的实体文件（实体总是重新生成）
                DeleteAllEntities(path, solutionName);

                var stats = new GenerationStats();

                // 生成实体类
                foreach (var table in tables)
                {
                    Console.WriteLine($"处理表: {table.Name} -> {table.EntityName}");

                    // 获取表的列信息（包含字段类型、注释等详细信息）
                    var columns = GetTableColumns(table.Name);

                    // 生成实体类（总是重新生成）
                    GenerateEntity(table, columns, entityTemplate, path, solutionName);
                    stats.EntitiesGenerated++;

                    Console.WriteLine(); // 添加空行分隔
                }

                // 输出统计信息
                Console.WriteLine("📊 实体生成统计:");
                Console.WriteLine($"   ✅ 实体类: {stats.EntitiesGenerated}");
                Console.WriteLine($"   📈 总计: {stats.EntitiesGenerated}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 生成实体类时发生错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
                throw;
            }
        }

        #endregion

        #region 数据库操作方法

        /// <summary>
        /// 获取数据库表信息
        /// </summary>
        /// <returns>数据库表信息列表</returns>
        /// <exception cref="Exception">数据库连接失败时抛出异常</exception>
        private static List<DbTableInfo> GetDatabaseTables()
        {
            try
            {
                return DbContext.Db.DbMaintenance.GetTableInfoList(false)
                    .Select(r => new DbTableInfo
                    {
                        Name = r.Name,
                        Description = r.Description
                    }).Where(w=> w.Name != "s_seed_data_records").ToList();
            }
            catch (Exception ex)
            {
                throw new Exception($"获取数据库表信息失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取指定表的列信息
        /// </summary>
        /// <param name="tableName">表名</param>
        /// <returns>列信息列表，包含字段类型、长度、注释等详细信息</returns>
        /// <exception cref="Exception">获取列信息失败时抛出异常</exception>
        private static List<DbColumnInfo> GetTableColumns(string tableName)
        {
            try
            {
                return DbContext.Db.DbMaintenance.GetColumnInfosByTableName(tableName, false)
                    .Select(r => new DbColumnInfo
                    {
                        TableName = r.TableName,
                        DbColumnName = r.DbColumnName,
                        DataType = r.DataType,
                        Length = r.Length,
                        ColumnDescription = r.ColumnDescription ?? "", // 确保注释不为null
                        DefaultValue = r.DefaultValue,
                        IsNullable = r.IsNullable,
                        IsIdentity = r.IsIdentity,
                        IsPrimaryKey = r.IsPrimarykey,
                        Precision = r.DecimalDigits,
                        Scale = r.Scale
                    }).ToList();
            }
            catch (Exception ex)
            {
                throw new Exception($"获取表 {tableName} 的列信息失败: {ex.Message}", ex);
            }
        }

        #endregion

        #region 模板操作方法

        /// <summary>
        /// 加载所有模板文件
        /// </summary>
        /// <returns>包含所有模板内容的对象</returns>
        /// <exception cref="FileNotFoundException">模板文件不存在时抛出异常</exception>
        private static Templates LoadTemplates()
        {
            try
            {
                // 获取模板文件的基础路径
                var templateBasePath = GetTemplateBasePath();

                return new Templates
                {
                    EntityTemplate = LoadTemplateFile(Path.Combine(templateBasePath, "Entity.txt"), "实体模板"),
                    RepositoryTemplate = LoadTemplateFile(Path.Combine(templateBasePath, "Repository.txt"), "仓储模板"),
                    ServiceTemplate = LoadTemplateFile(Path.Combine(templateBasePath, "Service.txt"), "服务模板"),
                    EnumTemplate = LoadTemplateFile(Path.Combine(templateBasePath, "Enum.txt"), "枚举模板"),
                    ControllerTemplate = LoadTemplateFile(Path.Combine(templateBasePath, "Controller.txt"), "控制器模板"),
                    ApplicationTemplate = LoadTemplateFile(Path.Combine(templateBasePath, "Application.txt"), "应用模板")
                };
            } 
            catch (Exception ex)
            {
                throw new Exception($"加载模板文件失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取模板文件的基础路径
        /// 自动查找Template目录
        /// </summary>
        /// <returns>模板文件基础路径</returns>
        /// <exception cref="DirectoryNotFoundException">找不到Template目录时抛出异常</exception>
        private static string GetTemplateBasePath()
        {
            // 获取当前执行程序的目录
            var currentDirectory = Directory.GetCurrentDirectory();
            var searchDirectory = new DirectoryInfo(currentDirectory);

            // 可能的模板路径
            var possiblePaths = new[]
            {
                Path.Combine(currentDirectory, "Template"),                    // 当前目录下的Template
                Path.Combine(currentDirectory, "src", "Core", "Infrastructure", "CYSF.Generate", "Template"), // 完整路径
                Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Template"), // 程序基础目录
            };

            // 向上查找Template目录
            while (searchDirectory != null)
            {
                var templatePath = Path.Combine(searchDirectory.FullName, "Template");
                if (Directory.Exists(templatePath))
                {
                    Console.WriteLine($"找到模板目录: {templatePath}");
                    return templatePath;
                }

                // 查找CYSF.Generate项目目录下的Template
                var generatePath = Path.Combine(searchDirectory.FullName, "src", "Core", "Infrastructure", "CYSF.Generate", "Template");
                if (Directory.Exists(generatePath))
                {
                    Console.WriteLine($"找到模板目录: {generatePath}");
                    return generatePath;
                }

                searchDirectory = searchDirectory.Parent;
            }

            // 检查预定义的可能路径
            foreach (var path in possiblePaths)
            {
                if (Directory.Exists(path))
                {
                    Console.WriteLine($"找到模板目录: {path}");
                    return path;
                }
            }

            throw new DirectoryNotFoundException($"无法找到Template目录。当前工作目录: {currentDirectory}");
        }

        /// <summary>
        /// 加载单个模板文件
        /// </summary>
        /// <param name="filePath">模板文件路径</param>
        /// <param name="templateName">模板名称（用于错误提示）</param>
        /// <returns>模板文件内容</returns>
        /// <exception cref="FileNotFoundException">模板文件不存在时抛出异常</exception>
        private static string LoadTemplateFile(string filePath, string templateName)
        {
            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"{templateName}文件不存在: {filePath}");
            }

            return File.ReadAllText(filePath, Encoding.UTF8);
        }

        #endregion

        #region 代码生成方法

        /// <summary>
        /// 生成实体类文件
        /// 根据数据库表结构生成对应的C#实体类，包含完整的字段映射和注释
        /// </summary>
        /// <param name="table">数据库表信息</param>
        /// <param name="columns">表的列信息</param>
        /// <param name="template">实体类模板</param>
        /// <param name="path">生成文件的根路径</param>
        /// <param name="solutionName">解决方案名称</param>
        private static void GenerateEntity(DbTableInfo table, List<DbColumnInfo> columns, string template, string path, string solutionName)
        {
            try
            {
                // 生成所有属性的代码
                var propertyContent = GenerateEntityProperties(columns);

                // 替换模板中的占位符
                var classContent = template
                    .Replace("{TABLE_COMMENT}", table.Comment ?? "数据表")
                    .Replace("{TABLE_NAME}", table.Name)
                    .Replace("{ENTITY_NAME}", table.EntityName)
                    .Replace("{TABLE_COLUMNS}", propertyContent)
                    .Replace("{SOLUTION_NAME}", solutionName);

                // 确保目录存在并生成文件
                var fileName = $"{path}/Model/{solutionName}.Models/Entities/{table.EntityName}.cs";
                FileHelper.CreateFile(fileName, classContent, Encoding.UTF8);
                Console.WriteLine($"  生成实体: {table.EntityName}.cs");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  生成实体 {table.EntityName} 失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 生成仓储类文件
        /// 仅在文件不存在时生成，避免覆盖已有的自定义仓储代码
        /// </summary>
        /// <param name="table">数据库表信息</param>
        /// <param name="template">仓储类模板</param>
        /// <param name="path">生成文件的根路径</param>
        /// <param name="solutionName">解决方案名称</param>
        /// <returns>是否成功生成文件</returns>
        private static bool GenerateRepository(DbTableInfo table, string template, string path, string solutionName)
        {
            var fileName = $"{path}/Repository/{solutionName}.Repositories/Implement/{table.EntityName}Repository.cs";

            // 检查文件是否已存在，避免覆盖自定义代码
            if (!FileHelper.IsExistFile(fileName))
            {
                try
                {
                    var classContent = template
                        .Replace("{ENTITY_NAME}", table.EntityName)
                        .Replace("{TABLE_COMMENT}", table.Comment ?? "数据表")
                        .Replace("{SOLUTION_NAME}", solutionName);

                    FileHelper.CreateFile(fileName, classContent, Encoding.UTF8);
                    Console.WriteLine($"  生成仓储: {table.EntityName}Repository.cs");
                    return true;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"  生成仓储 {table.EntityName}Repository 失败: {ex.Message}");
                    return false;
                }
            }
            else
            {
                Console.WriteLine($"  跳过仓储: {table.EntityName}Repository.cs (文件已存在)");
                return false;
            }
        }

        /// <summary>
        /// 生成服务类文件
        /// 仅在文件不存在时生成，避免覆盖已有的业务逻辑代码
        /// </summary>
        /// <param name="table">数据库表信息</param>
        /// <param name="template">服务类模板</param>
        /// <param name="path">生成文件的根路径</param>
        /// <param name="solutionName">解决方案名称</param>
        /// <returns>是否成功生成文件</returns>
        private static bool GenerateService(DbTableInfo table, string template, string path, string solutionName)
        {
            var fileName = $"{path}/Service/{solutionName}.Services/{table.EntityName}Service.cs";

            // 检查文件是否已存在，避免覆盖业务逻辑代码
            if (!FileHelper.IsExistFile(fileName))
            {
                try
                {
                    // 生成小写开头的实体名称（用于变量命名）
                    var lowerEntityName = table.EntityName.Substring(0, 1).ToLower() +
                                         (table.EntityName.Length > 1 ? table.EntityName.Substring(1) : "");

                    var classContent = template
                        .Replace("{ENTITY_NAME}", table.EntityName)
                        .Replace("{LOWER_ENTITY_NAME}", lowerEntityName)
                        .Replace("{TABLE_COMMENT}", table.Comment ?? "数据表")
                        .Replace("{SOLUTION_NAME}", solutionName);

                    FileHelper.CreateFile(fileName, classContent, Encoding.UTF8);
                    Console.WriteLine($"  生成服务: {table.EntityName}Service.cs");
                    return true;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"  生成服务 {table.EntityName}Service 失败: {ex.Message}");
                    return false;
                }
            }
            else
            {
                Console.WriteLine($"  跳过服务: {table.EntityName}Service.cs (文件已存在)");
                return false;
            }
        }

        /// <summary>
        /// 生成枚举类文件
        /// 基于数据库字段注释自动识别枚举类型，仅在文件不存在时生成
        /// </summary>
        /// <param name="table">数据库表信息</param>
        /// <param name="columns">表的列信息</param>
        /// <param name="template">枚举类模板</param>
        /// <param name="path">生成文件的根路径</param>
        /// <param name="solutionName">解决方案名称</param>
        /// <param name="generatedEnums">已生成的枚举列表，防止重复生成</param>
        /// <returns>本次生成的枚举文件数量</returns>
        private static int GenerateEnums(DbTableInfo table, List<DbColumnInfo> columns, string template, string path, string solutionName, List<string> generatedEnums)
        {
            int count = 0;

            foreach (var column in columns)
            {
                // 检查字段注释是否以"枚举"结尾，且未重复生成
                if (column.ColumnDescription.EndsWith("枚举") && !generatedEnums.Contains(column.DbColumnName))
                {
                    var fileName = $"{path}/Model/{solutionName}.Models/Enum/{column.DbColumnName}.cs";

                    // 检查文件是否已存在
                    if (!FileHelper.IsExistFile(fileName))
                    {
                        try
                        {
                            // 清理注释内容，移除"枚举"后缀
                            var cleanComment = column.ColumnDescription.Replace("枚举", string.Empty).Trim();
                            if (string.IsNullOrEmpty(cleanComment))
                            {
                                cleanComment = $"{column.DbColumnName}枚举";
                            }

                            var classContent = template
                                .Replace("{COLUMN_NAME}", column.DbColumnName)
                                .Replace("{COLUMN_COMMENT}", cleanComment)
                                .Replace("{SOLUTION_NAME}", solutionName);

                            FileHelper.CreateFile(fileName, classContent, Encoding.UTF8);
                            Console.WriteLine($"  生成枚举: {column.DbColumnName}.cs");
                            count++;
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"  生成枚举 {column.DbColumnName} 失败: {ex.Message}");
                        }
                    }
                    else
                    {
                        Console.WriteLine($"  跳过枚举: {column.DbColumnName}.cs (文件已存在)");
                    }

                    // 记录已处理的枚举，防止重复生成
                    generatedEnums.Add(column.DbColumnName);
                }
            }

            return count;
        }

        /// <summary>
        /// 生成Controller类文件
        /// 根据数据库表信息生成对应的Controller类，包含标准的CRUD操作
        /// </summary>
        /// <param name="table">数据库表信息</param>
        /// <param name="template">Controller模板</param>
        /// <param name="path">生成文件的根路径</param>
        /// <param name="solutionName">解决方案名称</param>
        /// <returns>是否成功生成</returns>
        private static bool GenerateController(DbTableInfo table, string template, string path, string solutionName)
        {
            var controllersDir = $"{path}/Api/Api.Backend/Controllers";
            FileHelper.CreateDirectory(controllersDir);

            var fileName = Path.Combine(controllersDir, $"{table.EntityName}Controller.cs");

            // 检查文件是否已存在，避免覆盖自定义代码
            if (!FileHelper.IsExistFile(fileName))
            {
                try
                {
                    // 生成小写开头的实体名称（用于变量命名）
                    var lowerEntityName = table.EntityName.Substring(0, 1).ToLower() +
                                         (table.EntityName.Length > 1 ? table.EntityName.Substring(1) : "");

                    var classContent = template
                        .Replace("{ENTITY_NAME}", table.EntityName)
                        .Replace("{LOWER_ENTITY_NAME}", lowerEntityName)
                        .Replace("{TABLE_COMMENT}", table.Comment ?? "数据表")
                        .Replace("{SOLUTION_NAME}", solutionName);

                    FileHelper.CreateFile(fileName, classContent, Encoding.UTF8);
                    Console.WriteLine($"  生成控制器: {table.EntityName}Controller.cs");
                    return true;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"  生成控制器 {table.EntityName}Controller 失败: {ex.Message}");
                    return false;
                }
            }
            else
            {
                Console.WriteLine($"  跳过控制器: {table.EntityName}Controller.cs (文件已存在)");
                return false;
            }
        }

        /// <summary>
        /// 生成Application类文件
        /// 根据数据库表信息生成对应的Application类，包含业务逻辑处理
        /// </summary>
        /// <param name="table">数据库表信息</param>
        /// <param name="template">Application模板</param>
        /// <param name="path">生成文件的根路径</param>
        /// <param name="solutionName">解决方案名称</param>
        /// <returns>是否成功生成</returns>
        private static bool GenerateApplication(DbTableInfo table, string template, string path, string solutionName)
        {
            var applicationsDir = $"{path}/Application/{solutionName}.Application";
            FileHelper.CreateDirectory(applicationsDir);

            var fileName = Path.Combine(applicationsDir, $"{table.EntityName}App.cs");

            // 检查文件是否已存在，避免覆盖业务逻辑代码
            if (!FileHelper.IsExistFile(fileName))
            {
                try
                {
                    // 生成小写开头的实体名称（用于变量命名）
                    var lowerEntityName = table.EntityName.Substring(0, 1).ToLower() +
                                         (table.EntityName.Length > 1 ? table.EntityName.Substring(1) : "");

                    var classContent = template
                        .Replace("{ENTITY_NAME}", table.EntityName)
                        .Replace("{LOWER_ENTITY_NAME}", lowerEntityName)
                        .Replace("{TABLE_COMMENT}", table.Comment ?? "数据表")
                        .Replace("{SOLUTION_NAME}", solutionName);

                    FileHelper.CreateFile(fileName, classContent, Encoding.UTF8);
                    Console.WriteLine($"  生成应用服务: {table.EntityName}App.cs");
                    return true;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"  生成应用服务 {table.EntityName}App 失败: {ex.Message}");
                    return false;
                }
            }
            else
            {
                Console.WriteLine($"  跳过应用服务: {table.EntityName}App.cs (文件已存在)");
                return false;
            }
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 删除所有现有实体文件
        /// 确保实体类与数据库结构完全同步
        /// </summary>
        /// <param name="path">生成文件的根路径</param>
        /// <param name="solutionName">解决方案名称</param>
        private static void DeleteAllEntities(string path, string solutionName)
        {
            var entitiesDir = $"{path}/Model/{solutionName}.Models/Entities";

            if (Directory.Exists(entitiesDir))
            {
                try
                {
                    var files = Directory.GetFiles(entitiesDir, "*.cs");
                    if (files.Length > 0)
                    {
                        Console.WriteLine($"正在删除 {files.Length} 个现有实体文件...");
                        foreach (var file in files)
                        {
                            File.Delete(file);
                            Console.WriteLine($"  删除: {Path.GetFileName(file)}");
                        }
                        Console.WriteLine();
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"删除实体文件时发生错误: {ex.Message}");
                    throw;
                }
            }
            else
            {
                Console.WriteLine("实体目录不存在，将创建新目录");
            }
        }

        /// <summary>
        /// 生成实体属性代码
        /// </summary>
        private static string GenerateEntityProperties(List<DbColumnInfo> columns)
        {
            var propertyBuilder = new StringBuilder();

            for (int i = 0; i < columns.Count; i++)
            {
                var column = columns[i];
                var isLastColumn = i == columns.Count - 1;

                propertyBuilder.AppendLine(GenerateColumnProperty(column, isLastColumn));
            }

            return propertyBuilder.ToString();
        }

        /// <summary>
        /// 生成单个列的属性代码
        /// </summary>
        private static string GenerateColumnProperty(DbColumnInfo columnInfo, bool isLastColumn)
        {
            var propertyBuilder = new StringBuilder();
            var propertySpace = "        ";

            // 获取C#类型
            var csharpType = GetCSharpType(columnInfo);

            // 生成注释
            propertyBuilder.AppendLine($"{propertySpace}/// <summary>");
            propertyBuilder.AppendLine($"{propertySpace}/// {columnInfo.ColumnDescription}");

            // 添加长度信息
            if (IsStringType(columnInfo.DataType) && columnInfo.Length > 0)
            {
                propertyBuilder.AppendLine($"{propertySpace}/// 长度:{columnInfo.Length}");
            }
            else if (IsDecimalType(columnInfo.DataType))
            {
                propertyBuilder.AppendLine($"{propertySpace}/// 精度:{columnInfo.Precision},小数位:{columnInfo.Scale}");
            }

            propertyBuilder.AppendLine($"{propertySpace}/// </summary>");

            // 生成SqlSugar特性
            var attributes = GenerateSqlSugarAttributes(columnInfo);
            if (!string.IsNullOrEmpty(attributes))
            {
                propertyBuilder.AppendLine($"{propertySpace}{attributes}");
            }

            // 生成属性
            var propertyName = columnInfo.DbColumnName;
            var defaultValue = GetDefaultValue(columnInfo);

            if (!string.IsNullOrEmpty(defaultValue))
            {
                propertyBuilder.AppendLine($"{propertySpace}public {csharpType} {propertyName} {{ get; set; }} = {defaultValue};");
            }
            else
            {
                propertyBuilder.AppendLine($"{propertySpace}public {csharpType} {propertyName} {{ get; set; }}");
            }

            // 如果不是最后一列，添加空行
            if (!isLastColumn)
            {
                propertyBuilder.AppendLine();
            }

            return propertyBuilder.ToString();
        }

        /// <summary>
        /// 获取C#类型
        /// </summary>
        private static string GetCSharpType(DbColumnInfo columnInfo)
        {
            var dataType = columnInfo.DataType.ToLower().Replace(" unsigned", "");
            var isNullable = columnInfo.IsNullable && !columnInfo.IsPrimaryKey;

            string baseType = dataType switch
            {
                "tinyint" => "int",
                "smallint" => "int",
                "int" => "int",
                "bigint" => "long",
                "bit" => "bool",
                "decimal" => "decimal",
                "numeric" => "decimal",
                "money" => "decimal",
                "smallmoney" => "decimal",
                "float" => "double",
                "real" => "float",
                "datetime" => "DateTime",
                "datetime2" => "DateTime",
                "smalldatetime" => "DateTime",
                "date" => "DateTime",
                "time" => "TimeSpan",
                "datetimeoffset" => "DateTimeOffset",
                "timestamp" => "byte[]",
                "binary" => "byte[]",
                "varbinary" => "byte[]",
                "image" => "byte[]",
                "char" => "string",
                "varchar" => "string",
                "text" => "string",
                "nchar" => "string",
                "nvarchar" => "string",
                "ntext" => "string",
                "uniqueidentifier" => "Guid",
                "xml" => "string",
                "sql_variant" => "object",
                _ => "string" // 默认为string
            };

            // 处理可空类型
            if (isNullable && IsValueType(baseType))
            {
                return $"{baseType}?";
            }
            
            // 处理枚举
            if (columnInfo.ColumnDescription.EndsWith("枚举"))
                baseType = columnInfo.DbColumnName;
            if (columnInfo.ColumnDescription.EndsWith("枚举") && columnInfo.IsNullable)
                baseType = $"{columnInfo.DbColumnName}?";

            return baseType;
        }

        /// <summary>
        /// 生成SqlSugar特性
        /// </summary>
        private static string GenerateSqlSugarAttributes(DbColumnInfo columnInfo)
        {
            var attributeParts = new List<string>();

            // 主键
            if (columnInfo.IsPrimaryKey)
            {
                attributeParts.Add("IsPrimaryKey = true");
            }

            // 自增
            if (columnInfo.IsIdentity)
            {
                attributeParts.Add("IsIdentity = true");
            }

            // 可空性
            if (!columnInfo.IsNullable && !columnInfo.IsPrimaryKey)
            {
                attributeParts.Add("IsNullable = false");
            }

            // 字段长度（字符串类型）
            if (columnInfo.Length > 0 && IsStringType(columnInfo.DataType))
            {
                attributeParts.Add($"Length = {columnInfo.Length}");
            }

            // 数值精度（decimal类型）
            if (columnInfo.Precision > 0 && IsDecimalType(columnInfo.DataType))
            {
                if (columnInfo.Scale > 0)
                {
                    attributeParts.Add($"DecimalDigits = {columnInfo.Precision}");
                    attributeParts.Add($"Scale = {columnInfo.Scale}");
                }
                else
                {
                    attributeParts.Add($"DecimalDigits = {columnInfo.Precision}");
                }
            }

            // 列注释
            if (!string.IsNullOrEmpty(columnInfo.ColumnDescription))
            {
                var cleanDescription = columnInfo.ColumnDescription.Replace("\"", "\\\"");
                attributeParts.Add($"ColumnDescription = \"{cleanDescription}\"");
            }

            // 默认值
            if (!string.IsNullOrEmpty(columnInfo.DefaultValue) &&
                columnInfo.DefaultValue != "NULL" &&
                !columnInfo.IsIdentity)
            {
                var defaultValue = FormatDefaultValue(columnInfo.DefaultValue, columnInfo.DataType);
                if (!string.IsNullOrEmpty(defaultValue))
                {
                    attributeParts.Add($"DefaultValue = \"{defaultValue}\"");
                }
            }

            if (attributeParts.Count > 0)
            {
                return $"[SugarColumn({string.Join(", ", attributeParts)})]";
            }

            return "";
        }

        /// <summary>
        /// 获取默认值
        /// </summary>
        private static string GetDefaultValue(DbColumnInfo columnInfo)
        {
            var csharpType = GetCSharpType(columnInfo);

            // 字符串类型默认为空字符串
            if (csharpType == "string")
            {
                return "string.Empty";
            }

            return "";
        }

        /// <summary>
        /// 判断是否为字符串类型
        /// </summary>
        private static bool IsStringType(string dataType)
        {
            var stringTypes = new[] { "char", "varchar", "text", "nchar", "nvarchar", "ntext", "xml" };
            return stringTypes.Contains(dataType.ToLower());
        }

        /// <summary>
        /// 判断是否为小数类型
        /// </summary>
        private static bool IsDecimalType(string dataType)
        {
            var decimalTypes = new[] { "decimal", "numeric", "money", "smallmoney" };
            return decimalTypes.Contains(dataType.ToLower());
        }

        /// <summary>
        /// 判断是否为值类型
        /// </summary>
        private static bool IsValueType(string csharpType)
        {
            var valueTypes = new[] { "int", "long", "bool", "decimal", "double", "float", "DateTime", "TimeSpan", "DateTimeOffset", "Guid" };
            return valueTypes.Contains(csharpType);
        }

        /// <summary>
        /// 格式化默认值
        /// </summary>
        private static string FormatDefaultValue(string defaultValue, string dataType)
        {
            if (string.IsNullOrEmpty(defaultValue) || defaultValue == "NULL")
                return "";

            // 移除括号
            defaultValue = defaultValue.Trim('(', ')');

            // 字符串类型默认值
            if (IsStringType(dataType))
            {
                return defaultValue.Trim('\'', '"');
            }

            // 数值类型默认值
            if (dataType.ToLower().Contains("int") || dataType.ToLower().Contains("decimal") ||
                dataType.ToLower().Contains("float") || dataType.ToLower().Contains("money"))
            {
                return defaultValue;
            }

            // 布尔类型默认值
            if (dataType.ToLower().Contains("bit"))
            {
                return defaultValue == "1" ? "true" : "false";
            }

            // 日期类型默认值
            if (dataType.ToLower().Contains("datetime"))
            {
                if (defaultValue.ToLower().Contains("getdate") || defaultValue.ToLower().Contains("now"))
                {
                    return ""; // 不设置默认值，由代码处理
                }
            }

            return defaultValue;
        }

        /// <summary>
        /// 输出生成统计信息
        /// 显示本次代码生成的详细统计结果
        /// </summary>
        /// <param name="stats">生成统计信息</param>
        private static void PrintGenerationStats(GenerationStats stats)
        {
            Console.WriteLine();
            Console.WriteLine("=== 生成统计 ===");
            Console.WriteLine($"实体类: {stats.EntitiesGenerated} 个 (全部重新生成)");
            Console.WriteLine($"仓储类: {stats.RepositoriesGenerated} 个生成, {stats.RepositoriesSkipped} 个跳过");
            Console.WriteLine($"服务类: {stats.ServicesGenerated} 个生成, {stats.ServicesSkipped} 个跳过");
            Console.WriteLine($"枚举类: {stats.EnumsGenerated} 个生成");

            if (stats.ControllersGenerated > 0 || stats.ControllersSkipped > 0)
                Console.WriteLine($"控制器: {stats.ControllersGenerated} 个生成, {stats.ControllersSkipped} 个跳过");

            if (stats.ApplicationsGenerated > 0 || stats.ApplicationsSkipped > 0)
                Console.WriteLine($"应用服务: {stats.ApplicationsGenerated} 个生成, {stats.ApplicationsSkipped} 个跳过");

            var totalGenerated = stats.EntitiesGenerated + stats.RepositoriesGenerated + stats.ServicesGenerated +
                               stats.EnumsGenerated + stats.ControllersGenerated + stats.ApplicationsGenerated;
            Console.WriteLine($"总计: {totalGenerated} 个文件生成");
            Console.WriteLine("代码生成完成！");
        }

        #endregion
    }

    /// <summary>
    /// 模板容器
    /// </summary>
    public class Templates
    {
        public string EntityTemplate { get; set; }
        public string RepositoryTemplate { get; set; }
        public string ServiceTemplate { get; set; }
        public string EnumTemplate { get; set; }
        public string ControllerTemplate { get; set; }
        public string ApplicationTemplate { get; set; }
    }

    /// <summary>
    /// 生成统计信息
    /// </summary>
    public class GenerationStats
    {
        public int EntitiesGenerated { get; set; }
        public int RepositoriesGenerated { get; set; }
        public int RepositoriesSkipped { get; set; }
        public int ServicesGenerated { get; set; }
        public int ServicesSkipped { get; set; }
        public int EnumsGenerated { get; set; }
        public int ControllersGenerated { get; set; }
        public int ControllersSkipped { get; set; }
        public int ApplicationsGenerated { get; set; }
        public int ApplicationsSkipped { get; set; }
    }
}
