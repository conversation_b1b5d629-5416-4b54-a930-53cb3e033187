{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "Database": {"ConnectionString": "Server=localhost,1433;Database=CYSF_Dev;User Id=sa;Password=**********;TrustServerCertificate=true;Encrypt=false;MultipleActiveResultSets=true;ConnectRetryCount=3;ConnectRetryInterval=10;Command Timeout=30;"}, "CodeGeneration": {"SolutionName": "CYSF", "OutputPath": "Generated", "EntityNamespace": "CYSF.Models.Entities", "EntityAssemblyPath": "../../../Core/Model/CYSF.Models/bin/Debug/net8.0/CYSF.Models.dll", "EntityXmlDocPath": "../../../Core/Model/CYSF.Models/bin/Debug/net8.0/CYSF.Models.xml", "ServiceOutputPath": "../../../Core/Service/CYSF.Service", "RepositoryOutputPath": "../../../Core/Repository/CYSF.Repository", "ApplicationOutputPath": "../../../Core/Application/CYSF.Application", "ControllerOutputPath": "../../../Backend/Api.Backend/Controllers"}, "EntityDependency": {"DependencyOrder": ["Tenant", "DicType", "Dic", "TenantRole", "TenantUser", "UserTenantRela", "UserLoginRecord"]}}