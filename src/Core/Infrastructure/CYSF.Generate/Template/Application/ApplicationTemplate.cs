using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Mapster;
using CYSF.Services;
using CYSF.Models.Request.{ENTITY_NAME};
using CYSF.Models.Response;
using CYSF.Core.Cache;
using CYSF.Models.Entities;
using CYSF.Core.Exceptions;
using CYSF.Core.Models;
using CYSF.Models.Const;
using CYSF.Models.Request;
using CYSF.Core.Helpers;
using CYSF.Core.Extensions;
using CYSF.Core.HttpContextUser;

namespace CYSF.Application;

/// <summary>
/// {TABLE_COMMENT}应用服务
/// </summary>
public class {ENTITY_NAME}App
{
    private readonly {ENTITY_NAME}Service _{LOWER_ENTITY_NAME}Service;
    private readonly ILogger<{ENTITY_NAME}App> _logger;
    private readonly IContextUser _contextUser;
    private readonly CacheHelper _cacheHelper;

    public {ENTITY_NAME}App(
        {ENTITY_NAME}Service {LOWER_ENTITY_NAME}Service,
        ILogger<{ENTITY_NAME}App> logger,
        IContextUser contextUser,
        CacheHelper cacheHelper)
    {
        _{LOWER_ENTITY_NAME}Service = {LOWER_ENTITY_NAME}Service;
        _logger = logger;
        _contextUser = contextUser;
        _cacheHelper = cacheHelper;
    }

    /// <summary>
    /// 根据ID获取{TABLE_COMMENT}
    /// </summary>
    /// <param name="id">{TABLE_COMMENT}ID</param>
    /// <returns>{TABLE_COMMENT}实体</returns>
    public async Task<{ENTITY_NAME}> GetByIdAsync(int id)
    {
        if (id <= 0)
            throw new ApiException("{TABLE_COMMENT}ID无效");

        var entity = await _{LOWER_ENTITY_NAME}Service.GetByIdAsync(id);
        if (entity == null)
            throw new ApiException("{TABLE_COMMENT}不存在");

        return entity;
    }

    /// <summary>
    /// 获取{TABLE_COMMENT}列表
    /// </summary>
    /// <param name="req">查询请求</param>
    /// <returns>{TABLE_COMMENT}列表</returns>
    public async Task<ListRes<{ENTITY_NAME}Dto>> GetPageListAsync(PageListReq<{ENTITY_NAME}PageListReq> req)
    {
        if (req.IsSortEmpty)
            req.Sorts = [new PageSort(nameof({ENTITY_NAME}.UpdateTime), true)];

        var res = await _{LOWER_ENTITY_NAME}Service.GetPageListAsync(req);
        return res;
    }

    /// <summary>
    /// 创建{TABLE_COMMENT}
    /// </summary>
    /// <param name="req">创建请求</param>
    /// <returns>创建的{TABLE_COMMENT}</returns>
    public async Task<{ENTITY_NAME}> CreateAsync(Create{ENTITY_NAME}Req req)
    {
        // 使用Mapster映射
        var entity = req.Adapt<{ENTITY_NAME}>();
        entity.CreateTime = DateTime.Now;
        entity.CreatorId = _contextUser.UserId;

        var created{ENTITY_NAME} = await _{LOWER_ENTITY_NAME}Service.CreateAsync(entity);

        _logger.LogInformation("创建{TABLE_COMMENT}成功，ID: {Id}", created{ENTITY_NAME}.Id);

        return created{ENTITY_NAME};
    }

    /// <summary>
    /// 更新{TABLE_COMMENT}
    /// </summary>
    /// <param name="req">更新请求</param>
    /// <returns>更新结果</returns>
    public async Task<bool> UpdateAsync(Update{ENTITY_NAME}Req req)
    {
        // 检查{TABLE_COMMENT}是否存在
        var existing{ENTITY_NAME} = await _{LOWER_ENTITY_NAME}Service.GetByIdAsync(req.Id);
        if (existing{ENTITY_NAME} == null)
            throw new ApiException("{TABLE_COMMENT}不存在");

        // 使用Mapster映射更新字段
        req.Adapt(existing{ENTITY_NAME});
        existing{ENTITY_NAME}.UpdateTime = DateTime.Now;

        var result = await _{LOWER_ENTITY_NAME}Service.UpdateAsync(existing{ENTITY_NAME});

        _logger.LogInformation("更新{TABLE_COMMENT}成功，ID: {Id}", req.Id);

        return result;
    }

    /// <summary>
    /// 删除{TABLE_COMMENT}
    /// </summary>
    /// <param name="id">{TABLE_COMMENT}ID</param>
    /// <returns>删除结果</returns>
    public async Task<bool> DeleteAsync(int id)
    {
        if (id <= 0)
            throw new ApiException("{TABLE_COMMENT}ID无效");

        // 检查{TABLE_COMMENT}是否存在
        var entity = await _{LOWER_ENTITY_NAME}Service.GetByIdAsync(id);
        if (entity == null)
            throw new ApiException("{TABLE_COMMENT}不存在");

        var result = await _{LOWER_ENTITY_NAME}Service.DeleteAsync(id);

        _logger.LogInformation("删除{TABLE_COMMENT}成功，ID: {Id}", id);

        return result;
    }

    /// <summary>
    /// 批量删除{TABLE_COMMENT}
    /// </summary>
    /// <param name="ids">{TABLE_COMMENT}ID列表</param>
    /// <returns>删除结果</returns>
    public async Task<bool> DeleteBatchAsync(List<int> ids)
    {
        if (ids == null || ids.Count == 0)
            throw new ApiException("请选择要删除的{TABLE_COMMENT}");

        var result = await _{LOWER_ENTITY_NAME}Service.DeleteBatchAsync(ids);

        _logger.LogInformation("批量删除{TABLE_COMMENT}成功，数量: {Count}", ids.Count);

        return result;
    }

    /// <summary>
    /// 检查{TABLE_COMMENT}是否存在
    /// </summary>
    /// <param name="id">{TABLE_COMMENT}ID</param>
    /// <returns>是否存在</returns>
    public async Task<bool> ExistsAsync(int id)
    {
        return await _{LOWER_ENTITY_NAME}Service.ExistsAsync(id);
    }
}
