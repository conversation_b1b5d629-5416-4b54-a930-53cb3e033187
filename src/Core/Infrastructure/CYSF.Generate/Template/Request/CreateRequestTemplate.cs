using FluentValidation;
using CYSF.Models.Validators.Common;

namespace CYSF.Models.Request.{ENTITY_NAME};

/// <summary>
/// 创建{TABLE_COMMENT}请求模型
/// </summary>
public class Create{ENTITY_NAME}Req
{
{PROPERTIES}
}

/// <summary>
/// 创建{TABLE_COMMENT}请求验证器 - 继承基础验证器，自动支持快速失败模式
/// </summary>
public class Create{ENTITY_NAME}ReqValidator : BaseValidator<Create{ENTITY_NAME}Req>
{
    public Create{ENTITY_NAME}ReqValidator()
    {
{VALIDATIONS}
    }
}
