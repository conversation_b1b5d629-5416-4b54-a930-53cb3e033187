using System;
using System.Linq;
using System.Threading.Tasks;
using CYSF.Core.Extensions;
using CYSF.Repositories.Extensions;
using CYSF.Core.Providers;
using CYSF.Models.Entities;
using CYSF.Models.Request;
using CYSF.Models.Request.{ENTITY_NAME};
using CYSF.Models.Response;
using CYSF.Repositories.Base;
using Mapster;
using SqlSugar;

namespace CYSF.Repositories;

/// <summary>
/// {TABLE_COMMENT}仓储
/// </summary>
public class {ENTITY_NAME}Repository(ISqlSugarClient db) : BaseRepository<{ENTITY_NAME}>(db)
{
    /// <summary>
    /// 获取{TABLE_COMMENT}分页列表
    /// </summary>
    /// <param name="req">分页查询请求</param>
    /// <returns>{TABLE_COMMENT}分页列表</returns>
    public async Task<ListRes<{ENTITY_NAME}Dto>> GetPageListAsync(PageListReq<{ENTITY_NAME}PageListReq> req)
    {
        // 构建查询
        var query = Context.Queryable<{ENTITY_NAME}>()
                .WhereIF(req.Filters.Id.HasValue, t => t.Id == req.Filters.Id.Value)
                .WhereIF(req.Filters.CreateTime != null && req.Filters.CreateTime.Count > 0, t => t.CreateTime >= req.Filters.CreateTime[0])
                .WhereIF(req.Filters.CreateTime != null && req.Filters.CreateTime.Count > 1, t => t.CreateTime <= req.Filters.CreateTime[1])
                .WhereIF(req.Filters.UpdateTime != null && req.Filters.UpdateTime.Count > 0, t => t.UpdateTime >= req.Filters.UpdateTime[0])
                .WhereIF(req.Filters.UpdateTime != null && req.Filters.UpdateTime.Count > 1, t => t.UpdateTime <= req.Filters.UpdateTime[1])
                .Select(t => new {ENTITY_NAME}Dto(), true)  // true表示自动映射其他字段
                .SortBy(req.Sorts);

        // 执行分页查询
        RefAsync<int> totalCount = 0;
        var list = await query.ToPageListAsync(req.Pagination.PageIndex, req.Pagination.PageSize, totalCount);

        return new ListRes<{ENTITY_NAME}Dto>
        {
            Data = list,
            Total = totalCount.Value
        };
    }

    /// <summary>
    /// 根据条件获取{TABLE_COMMENT}列表
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>{TABLE_COMMENT}列表</returns>
    public async Task<List<{ENTITY_NAME}>> GetListByConditionAsync(System.Linq.Expressions.Expression<Func<{ENTITY_NAME}, bool>> predicate)
    {
        return await Context.Queryable<{ENTITY_NAME}>()
            .Where(predicate)
            .ToListAsync();
    }

    /// <summary>
    /// 根据条件获取{TABLE_COMMENT}数量
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>{TABLE_COMMENT}数量</returns>
    public async Task<int> GetCountByConditionAsync(System.Linq.Expressions.Expression<Func<{ENTITY_NAME}, bool>> predicate)
    {
        return await Context.Queryable<{ENTITY_NAME}>()
            .Where(predicate)
            .CountAsync();
    }

    /// <summary>
    /// 检查{TABLE_COMMENT}是否存在
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>是否存在</returns>
    public async Task<bool> ExistsAsync(System.Linq.Expressions.Expression<Func<{ENTITY_NAME}, bool>> predicate)
    {
        return await Context.Queryable<{ENTITY_NAME}>()
            .Where(predicate)
            .AnyAsync();
    }

    /// <summary>
    /// 获取{TABLE_COMMENT}的第一条记录
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>{TABLE_COMMENT}实体</returns>
    public async Task<{ENTITY_NAME}> GetFirstOrDefaultAsync(System.Linq.Expressions.Expression<Func<{ENTITY_NAME}, bool>> predicate)
    {
        return await Context.Queryable<{ENTITY_NAME}>()
            .Where(predicate)
            .FirstAsync();
    }

    /// <summary>
    /// 批量插入{TABLE_COMMENT}
    /// </summary>
    /// <param name="entities">{TABLE_COMMENT}实体列表</param>
    /// <returns>插入结果</returns>
    public async Task<bool> InsertBatchAsync(List<{ENTITY_NAME}> entities)
    {
        return await Context.Insertable(entities).ExecuteCommandAsync() > 0;
    }

    /// <summary>
    /// 批量更新{TABLE_COMMENT}
    /// </summary>
    /// <param name="entities">{TABLE_COMMENT}实体列表</param>
    /// <returns>更新结果</returns>
    public async Task<bool> UpdateBatchAsync(List<{ENTITY_NAME}> entities)
    {
        return await Context.Updateable(entities).ExecuteCommandAsync() > 0;
    }
}
