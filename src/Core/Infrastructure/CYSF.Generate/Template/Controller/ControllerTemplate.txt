using System;
using System.Collections.Generic;
using System.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using {ROOT_NAMESPACE}.Application;
using {ROOT_NAMESPACE}.Core.Attributes;
using {ROOT_NAMESPACE}.Models.Request;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Api.Backend.Controllers;

/// <summary>
/// {TABLE_COMMENT}管理控制器
/// </summary>
[Route("api/[controller]/[action]")]
[ApiController]
[Authorize]
public class {ENTITY_NAME}Controller : ControllerBase
{
    private readonly {ENTITY_NAME}App _{LOWER_ENTITY_NAME}App;
    private readonly ILogger<{ENTITY_NAME}Controller> _logger;

    public {ENTITY_NAME}Controller({ENTITY_NAME}App {LOWER_ENTITY_NAME}App, ILogger<{ENTITY_NAME}Controller> logger)
    {
        _{LOWER_ENTITY_NAME}App = {LOWER_ENTITY_NAME}App;
        _logger = logger;
    }

    /// <summary>
    /// 获取{TABLE_COMMENT}列表
    /// </summary>
    /// <param name="req">查询条件</param>
    /// <returns>{TABLE_COMMENT}列表</returns>
    [HttpPost]
    public async Task<IActionResult> GetPageList([FromBody] PageListReq<object> req)
    {
        var result = await _{LOWER_ENTITY_NAME}App.GetPageListAsync(req);
        return Ok(result);
    }

    /// <summary>
    /// 根据ID获取{TABLE_COMMENT}详情
    /// </summary>
    /// <param name="id">{TABLE_COMMENT}ID</param>
    /// <returns>{TABLE_COMMENT}详情</returns>
    [HttpGet]
    public async Task<IActionResult> GetById([FromQuery] int id)
    {
        var result = await _{LOWER_ENTITY_NAME}App.GetByIdAsync(id);
        return Ok(result);
    }

    /// <summary>
    /// 创建{TABLE_COMMENT}
    /// </summary>
    /// <param name="entity">{TABLE_COMMENT}实体</param>
    /// <returns>创建的{TABLE_COMMENT}信息</returns>
    [HttpPost]
    public async Task<IActionResult> Create([FromBody] {ENTITY_NAME} entity)
    {
        var result = await _{LOWER_ENTITY_NAME}App.CreateAsync(entity);
        return Ok(result);
    }

    /// <summary>
    /// 更新{TABLE_COMMENT}
    /// </summary>
    /// <param name="entity">{TABLE_COMMENT}实体</param>
    /// <returns>更新结果</returns>
    [HttpPost]
    public async Task<IActionResult> Update([FromBody] {ENTITY_NAME} entity)
    {
        var result = await _{LOWER_ENTITY_NAME}App.UpdateAsync(entity);
        return Ok(result);
    }

    /// <summary>
    /// 删除{TABLE_COMMENT}
    /// </summary>
    /// <param name="id">{TABLE_COMMENT}ID</param>
    /// <returns>删除结果</returns>
    [HttpPost]
    public async Task<IActionResult> Delete([FromQuery] int id)
    {
        var result = await _{LOWER_ENTITY_NAME}App.DeleteAsync(id);
        return Ok(result);
    }


}
