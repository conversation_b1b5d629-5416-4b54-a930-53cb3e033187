using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CYSF.Core.Enums;
using Microsoft.Extensions.Logging;
using Mapster;
using {SOLUTION_NAME}.Services;
using {SOLUTION_NAME}.Models.Request.{ENTITY_NAME};
using {SOLUTION_NAME}.Models.Entities;
using {SOLUTION_NAME}.Core.Exceptions;
using {SOLUTION_NAME}.Core.Models;
using {SOLUTION_NAME}.Models.Const;
using {SOLUTION_NAME}.Models.Request;
using {SOLUTION_NAME}.Models.Response;
using {SOLUTION_NAME}.Core.Cache;
using {SOLUTION_NAME}.Core.HttpContextUser;

namespace {SOLUTION_NAME}.Application;

/// <summary>
/// {TABLE_COMMENT}应用服务
/// </summary>
public class {ENTITY_NAME}App
{
    private readonly {ENTITY_NAME}Service _{LOWER_ENTITY_NAME}Service;
    private readonly ILogger<{ENTITY_NAME}App> _logger;
    private readonly CacheHelper _cacheHelper;
    private readonly IContextUser _contextUser;

    public {ENTITY_NAME}App(
        {ENTITY_NAME}Service {LOWER_ENTITY_NAME}Service,
        ILogger<{ENTITY_NAME}App> logger,
        CacheHelper cacheHelper,
        IContextUser contextUser)
    {
        _{LOWER_ENTITY_NAME}Service = {LOWER_ENTITY_NAME}Service;
        _logger = logger;
        _cacheHelper = cacheHelper;
        _contextUser = contextUser;
    }

    /// <summary>
    /// 根据ID获取{TABLE_COMMENT}
    /// </summary>
    /// <param name="id">{TABLE_COMMENT}ID</param>
    /// <returns>{TABLE_COMMENT}实体</returns>
    public async Task<{ENTITY_NAME}> Get(int id)
    {
        return await _{LOWER_ENTITY_NAME}Service.GetByIdAsync(id);
    }

    /// <summary>
    /// 获取{TABLE_COMMENT}列表
    /// </summary>
    /// <param name="req">查询请求</param>
    /// <returns>{TABLE_COMMENT}列表</returns>
    public async Task<ListRes<{ENTITY_NAME}>> GetPageListAsync(PageListReq<{ENTITY_NAME}PageListReq> req)
    {
        if(req.IsSortEmpty)
            req.Sorts = [new PageSort(nameof({ENTITY_NAME}.UpdateTime), true)];

        var res = await _{LOWER_ENTITY_NAME}Service.GetPageListAsync(req);
        return res;
    }

    /// <summary>
    /// 根据ID获取{TABLE_COMMENT}详情
    /// </summary>
    /// <param name="id">{TABLE_COMMENT}ID</param>
    /// <returns>{TABLE_COMMENT}详情</returns>
    public async Task<{ENTITY_NAME}> GetByIdAsync(int id)
    {
        if (id <= 0)
            throw new ApiException("{TABLE_COMMENT}ID无效");

        var entity = await _{LOWER_ENTITY_NAME}Service.GetByIdAsync(id);
        if (entity == null)
            throw new ApiException("{TABLE_COMMENT}不存在");

        return entity;
    }

    /// <summary>
    /// 创建{TABLE_COMMENT}
    /// </summary>
    /// <param name="req">创建请求</param>
    /// <returns>创建的{TABLE_COMMENT}</returns>
    public async Task<{ENTITY_NAME}> CreateAsync(Create{ENTITY_NAME}Req req)
    {
        var entity = req.Adapt<{ENTITY_NAME}>();
        entity.CreateTime = DateTime.Now;
        entity.CreatorId = _contextUser.UserId;

        var created = await _{LOWER_ENTITY_NAME}Service.CreateAsync(entity);

        _logger.LogInformation("创建{TABLE_COMMENT}成功，ID: {Id}", created.Id);

        return created;
    }

    /// <summary>
    /// 更新{TABLE_COMMENT}
    /// </summary>
    /// <param name="req">更新请求</param>
    /// <returns>更新结果</returns>
    public async Task<bool> UpdateAsync(Update{ENTITY_NAME}Req req)
    {
        if (req.Id <= 0)
            throw new ApiException("{TABLE_COMMENT}ID无效");

        var entity = await _{LOWER_ENTITY_NAME}Service.GetByIdAsync(req.Id);
        if (entity == null)
            throw new ApiException("{TABLE_COMMENT}不存在");

        // 映射更新字段
        req.Adapt(entity);
        entity.UpdateTime = DateTime.Now;
        entity.UpdatorId = _contextUser.UserId;

        var result = await _{LOWER_ENTITY_NAME}Service.UpdateAsync(entity);

        if (result)
            _logger.LogInformation("更新{TABLE_COMMENT}成功，ID: {Id}", req.Id);

        return result;
    }

    /// <summary>
    /// 删除{TABLE_COMMENT}
    /// </summary>
    /// <param name="id">{TABLE_COMMENT}ID</param>
    /// <returns>删除结果</returns>
    public async Task<bool> DeleteAsync(int id)
    {
        if (id <= 0)
            throw new ApiException("{TABLE_COMMENT}ID无效");

        var entity = await _{LOWER_ENTITY_NAME}Service.GetByIdAsync(id);
        if (entity == null)
            throw new ApiException("{TABLE_COMMENT}不存在");

        var result = await _{LOWER_ENTITY_NAME}Service.DeleteAsync(id);

        if (result)
            _logger.LogInformation("删除{TABLE_COMMENT}成功，ID: {Id}", id);

        return result;
    }
}
