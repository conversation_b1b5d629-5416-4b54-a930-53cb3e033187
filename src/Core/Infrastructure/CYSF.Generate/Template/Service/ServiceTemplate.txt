using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using {ROOT_NAMESPACE}.Models.Entities;
using {ROOT_NAMESPACE}.Models.Request;
using {ROOT_NAMESPACE}.Models.Response;
using {ROOT_NAMESPACE}.Repositories.Base;
using SqlSugar;

namespace {ROOT_NAMESPACE}.Services
{
    /// <summary>
    /// {TABLE_COMMENT}服务
    /// </summary>
    public class {ENTITY_NAME}Service
    {
        private readonly BaseRepository<{ENTITY_NAME}> _{LOWER_ENTITY_NAME}Repo;

        public {ENTITY_NAME}Service(BaseRepository<{ENTITY_NAME}> {LOWER_ENTITY_NAME}Repo)
        {
            _{LOWER_ENTITY_NAME}Repo = {LOWER_ENTITY_NAME}Repo;
        }

        /// <summary>
        /// 根据ID获取{TABLE_COMMENT}
        /// </summary>
        /// <param name="id">{TABLE_COMMENT}ID</param>
        /// <returns>{TABLE_COMMENT}实体</returns>
        public async Task<{ENTITY_NAME}> GetByIdAsync(int id)
        {
            return await _{LOWER_ENTITY_NAME}Repo.GetFirstAsync(r => r.Id == id);
        }

        /// <summary>
        /// 获取{TABLE_COMMENT}分页列表
        /// </summary>
        /// <param name="req">分页查询请求</param>
        /// <returns>{TABLE_COMMENT}分页列表</returns>
        public async Task<ListRes<{ENTITY_NAME}>> GetPageListAsync<T>(PageListReq<T> req) where T : class
        {
            return await _{LOWER_ENTITY_NAME}Repo.GetPageListAsync(req.Pagination, req.Sorts, null);
        }



        /// <summary>
        /// 创建{TABLE_COMMENT}
        /// </summary>
        /// <param name="{LOWER_ENTITY_NAME}">{TABLE_COMMENT}实体</param>
        /// <returns>创建的{TABLE_COMMENT}</returns>
        public async Task<{ENTITY_NAME}> CreateAsync({ENTITY_NAME} {LOWER_ENTITY_NAME})
        {
            return await _{LOWER_ENTITY_NAME}Repo.InsertReturnEntityAsync({LOWER_ENTITY_NAME});
        }

        /// <summary>
        /// 更新{TABLE_COMMENT}
        /// </summary>
        /// <param name="{LOWER_ENTITY_NAME}">{TABLE_COMMENT}实体</param>
        /// <returns>更新结果</returns>
        public async Task<bool> UpdateAsync({ENTITY_NAME} {LOWER_ENTITY_NAME})
        {
            return await _{LOWER_ENTITY_NAME}Repo.UpdateAsync({LOWER_ENTITY_NAME});
        }

        /// <summary>
        /// 删除{TABLE_COMMENT}
        /// </summary>
        /// <param name="id">{TABLE_COMMENT}ID</param>
        /// <returns>删除结果</returns>
        public async Task<bool> DeleteAsync(int id)
        {
            return await _{LOWER_ENTITY_NAME}Repo.DeleteAsync({LOWER_ENTITY_NAME} => {LOWER_ENTITY_NAME}.Id == id);
        }



        /// <summary>
        /// 获取{TABLE_COMMENT}总数
        /// </summary>
        /// <returns>{TABLE_COMMENT}总数</returns>
        public async Task<int> GetCountAsync()
        {
            return await _{LOWER_ENTITY_NAME}Repo.CountAsync();
        }

        /// <summary>
        /// 获取所有{TABLE_COMMENT}列表
        /// </summary>
        /// <returns>{TABLE_COMMENT}列表</returns>
        public async Task<List<{ENTITY_NAME}>> GetAllAsync()
        {
            return await _{LOWER_ENTITY_NAME}Repo.GetListAsync();
        }
    }
}
