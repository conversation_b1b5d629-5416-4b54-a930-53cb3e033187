using System.Collections.Generic;

namespace CYSF.Generate.Configuration
{
    /// <summary>
    /// 代码生成配置（静态配置，来自appsettings.json）
    /// </summary>
    public class CodeGenerationConfiguration
    {
        /// <summary>
        /// 解决方案名称
        /// </summary>
        public string SolutionName { get; set; } = "CYSF";

        /// <summary>
        /// 输出路径
        /// </summary>
        public string OutputPath { get; set; } = "Generated";

        /// <summary>
        /// 实体命名空间
        /// </summary>
        public string EntityNamespace { get; set; } = "CYSF.Models.Entities";

        /// <summary>
        /// 实体程序集路径
        /// </summary>
        public string EntityAssemblyPath { get; set; } = "../../../Core/Model/CYSF.Models/bin/Debug/net8.0/CYSF.Models.dll";

        /// <summary>
        /// 实体XML文档路径
        /// </summary>
        public string EntityXmlDocPath { get; set; } = "../../../Core/Model/CYSF.Models/bin/Debug/net8.0/CYSF.Models.xml";

        /// <summary>
        /// Service输出路径
        /// </summary>
        public string ServiceOutputPath { get; set; } = "../../../Core/Service/CYSF.Services";

        /// <summary>
        /// Repository输出路径
        /// </summary>
        public string RepositoryOutputPath { get; set; } = "../../../Core/Repository/CYSF.Repositories";

        /// <summary>
        /// Application输出路径
        /// </summary>
        public string ApplicationOutputPath { get; set; } = "../../../Core/Application/CYSF.Application";

        /// <summary>
        /// Controller输出路径
        /// </summary>
        public string ControllerOutputPath { get; set; } = "../../../Core/Api/Api.Backend/Controllers";

        /// <summary>
        /// 排除创建表的实体列表
        /// </summary>
        public List<string> ExcludedEntitiesFromTableCreation { get; set; } = new();

        /// <summary>
        /// 排除代码生成的实体列表
        /// </summary>
        public List<string> ExcludedEntitiesFromCodeGeneration { get; set; } = new();
    }

    /// <summary>
    /// 实体依赖配置
    /// </summary>
    public class EntityDependencyConfiguration
    {
        /// <summary>
        /// 依赖顺序
        /// </summary>
        public List<string> DependencyOrder { get; set; } = new List<string>();
    }



    /// <summary>
    /// 数据库配置
    /// </summary>
    public class DatabaseConfiguration
    {
        /// <summary>
        /// 连接字符串
        /// </summary>
        public string ConnectionString { get; set; } = "";
    }
}
