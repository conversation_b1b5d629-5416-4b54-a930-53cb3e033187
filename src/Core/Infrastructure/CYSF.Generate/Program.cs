using System;
using System.IO;
using System.Threading.Tasks;
using CYSF.Core.Helpers;
using CYSF.Generate.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace CYSF.Generate
{
    internal class Program
    {
        private static async Task Main()
        {
            // 设置控制台编码
            Console.OutputEncoding = System.Text.Encoding.UTF8;
            Console.InputEncoding = System.Text.Encoding.UTF8;

            try
            {
                // 配置服务
                var services = ConfigureServices();
                using var serviceProvider = services.BuildServiceProvider();

                // 从配置读取解决方案名称
                var configuration = serviceProvider.GetRequiredService<IConfiguration>();
                var solutionName = configuration.GetSection("CodeGeneration:SolutionName").Value ?? "CYSF";

                Console.WriteLine($"=== {solutionName} 开发工具集 ===");
                Console.WriteLine($"开始时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                Console.WriteLine();

                // 获取主服务
                var mainService = serviceProvider.GetRequiredService<MainService>();

                // 运行主程序
                await mainService.RunAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 程序运行过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
            }

            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 配置依赖注入服务
        /// </summary>
        private static IServiceCollection ConfigureServices()
        {
            var services = new ServiceCollection();

            // 配置Configuration
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();

            services.AddSingleton<IConfiguration>(configuration);

            // 配置日志
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.SetMinimumLevel(LogLevel.Information);
            });

            // 注册服务
            services.AddSingleton<MainService>();
            services.AddSingleton<CodeFirstService>();
            services.AddSingleton<DatabaseInitializationService>();
            services.AddSingleton<XmlDocumentationService>();
            services.AddSingleton<UserPreferencesService>();
            services.AddSingleton<InteractiveMenuService>();

            return services;
        }
    }
}