using CYSF.Publish.Models;
using CYSF.Publish.Utils;
using System.Diagnostics;

namespace CYSF.Publish.Services
{
    public class DockerService
    {
        private readonly ConfigService _configService;

        public DockerService()
        {
            _configService = new ConfigService();
        }

        public async Task<bool> DeployAsync(DeploymentConfig config)
        {
            try
            {
                ConsoleHelper.WriteTitle("================================================================");
                ConsoleHelper.WriteTitle($"           CYSF API Docker 部署 - {config.Environment} 环境");
                ConsoleHelper.WriteTitle("================================================================");

                // 检查 Docker 环境
                if (!await CheckDockerEnvironmentAsync())
                {
                    return false;
                }

                // 验证和修复 TLS 配置
                ConsoleHelper.WriteInfo("验证 Docker TLS 配置...");
                var tlsValid = await _configService.ValidateDockerTlsConfigurationAsync();
                if (!tlsValid)
                {
                    ConsoleHelper.WriteWarning("检测到 TLS 配置问题，正在自动修复...");
                    await _configService.FixDockerTlsIssueAsync();
                    ConsoleHelper.WriteSuccess("TLS 配置已修复");
                }

                // 更新配置文件
                ConsoleHelper.WriteInfo("更新应用配置...");
                if (!await _configService.UpdateAppSettingsAsync(config))
                {
                    return false;
                }

                // 创建输出目录
                if (!CreateOutputDirectory(config))
                {
                    return false;
                }

                // 发布项目
                if (!await PublishProjectAsync(config))
                {
                    return false;
                }

                // 创建 Docker 文件
                if (!await CreateDockerFilesAsync(config))
                {
                    return false;
                }

                // 停止现有容器
                await StopExistingContainersAsync(config);

                // 构建并启动容器
                if (!await BuildAndStartContainersAsync(config))
                {
                    return false;
                }

                // 验证部署
                if (!await VerifyDeploymentAsync(config))
                {
                    return false;
                }

                ShowDeploymentInfo(config);

                // 清理临时发布文件夹
                await CleanupPublishDirectoryAsync(config);

                return true;
            }
            catch (Exception ex)
            {
                ConsoleHelper.WriteError($"❌ Docker 部署失败: {ex.Message}");

                // 检查是否是TLS相关错误
                if (ex.Message.Contains("TLS") || ex.Message.Contains("SSL") || ex.Message.Contains("握手"))
                {
                    ConsoleHelper.WriteWarning("💡 检测到可能的 TLS 握手问题，建议运行以下命令修复:");
                    ConsoleHelper.WriteInfo("   选择主菜单 -> 快速修复 -> 修复 Docker TLS 握手问题");
                }

                // 即使部署失败也尝试清理临时文件
                try
                {
                    await CleanupPublishDirectoryAsync(config);
                }
                catch (Exception cleanupEx)
                {
                    ConsoleHelper.WriteWarning($"⚠️ 清理临时文件失败: {cleanupEx.Message}");
                }

                return false;
            }
        }

        public async Task<bool> StopAsync(DeploymentConfig config)
        {
            try
            {
                ConsoleHelper.WriteInfo("停止 Docker 服务...");
                await StopExistingContainersAsync(config);
                ConsoleHelper.WriteSuccess("✅ Docker 服务已停止");
                return true;
            }
            catch (Exception ex)
            {
                ConsoleHelper.WriteError($"❌ 停止 Docker 服务失败: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> UpdateAsync(DeploymentConfig config)
        {
            try
            {
                ConsoleHelper.WriteInfo("更新 Docker 服务...");
                
                // 更新配置
                await _configService.UpdateAppSettingsAsync(config);
                
                // 重新发布
                await PublishProjectAsync(config);
                
                // 重新构建容器
                await ExecuteCommandAsync($"docker-compose -f \"{GetDockerComposePath(config)}\" up -d --build", config.OutputPath);
                
                ConsoleHelper.WriteSuccess("✅ Docker 服务更新完成");
                return true;
            }
            catch (Exception ex)
            {
                ConsoleHelper.WriteError($"❌ 更新 Docker 服务失败: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> CheckDockerEnvironmentAsync()
        {
            try
            {
                ConsoleHelper.WriteInfo("正在检查 Docker 环境...");

                // 检查 Docker 是否安装
                ConsoleHelper.WriteInfo("  - 检查 Docker 安装状态...");
                var dockerVersionResult = await ExecuteCommandAsync("docker --version");
                if (dockerVersionResult.ExitCode != 0)
                {
                    ConsoleHelper.WriteError("❌ Docker 未安装");
                    ConsoleHelper.WriteWarning("请先安装 Docker Desktop:");
                    ConsoleHelper.WriteInfo("  下载地址: https://www.docker.com/products/docker-desktop");
                    return false;
                }

                ConsoleHelper.WriteSuccess($"  ✅ Docker 已安装: {dockerVersionResult.Output.Trim()}");

                // 检查 Docker 是否运行
                ConsoleHelper.WriteInfo("  - 检查 Docker 运行状态...");
                var dockerPsResult = await ExecuteCommandAsync("docker ps");
                if (dockerPsResult.ExitCode != 0)
                {
                    ConsoleHelper.WriteError("❌ Docker 未运行");
                    ConsoleHelper.WriteWarning("请启动 Docker Desktop 后重试");
                    ConsoleHelper.WriteInfo("启动方法:");
                    ConsoleHelper.WriteInfo("  1. 打开 Docker Desktop 应用程序");
                    ConsoleHelper.WriteInfo("  2. 等待 Docker 引擎启动完成");
                    ConsoleHelper.WriteInfo("  3. 确保系统托盘中 Docker 图标显示为运行状态");
                    return false;
                }

                ConsoleHelper.WriteSuccess("  ✅ Docker 运行正常");

                // 检查 Docker Compose
                ConsoleHelper.WriteInfo("  - 检查 Docker Compose...");
                var composeResult = await ExecuteCommandAsync("docker-compose --version");
                if (composeResult.ExitCode != 0)
                {
                    // 尝试新版本的 docker compose 命令
                    composeResult = await ExecuteCommandAsync("docker compose version");
                    if (composeResult.ExitCode != 0)
                    {
                        ConsoleHelper.WriteError("❌ Docker Compose 未安装");
                        ConsoleHelper.WriteWarning("请安装 Docker Compose 或更新 Docker Desktop 到最新版本");
                        return false;
                    }
                }

                ConsoleHelper.WriteSuccess($"  ✅ Docker Compose 可用: {composeResult.Output.Trim()}");

                // 检查 Docker 网络连接
                ConsoleHelper.WriteInfo("  - 检查 Docker 网络连接...");
                var networkResult = await ExecuteCommandAsync("docker network ls");
                if (networkResult.ExitCode != 0)
                {
                    ConsoleHelper.WriteWarning("⚠️ Docker 网络检查失败，但可能不影响部署");
                }
                else
                {
                    ConsoleHelper.WriteSuccess("  ✅ Docker 网络正常");
                }

                ConsoleHelper.WriteSuccess("🎉 Docker 环境检查完成，所有组件正常！");
                return true;
            }
            catch (Exception ex)
            {
                ConsoleHelper.WriteError($"❌ Docker 环境检查过程中发生异常: {ex.Message}");
                ConsoleHelper.WriteInfo("可能的解决方案:");
                ConsoleHelper.WriteInfo("  1. 确保 Docker Desktop 已安装并运行");
                ConsoleHelper.WriteInfo("  2. 检查系统环境变量中是否包含 Docker 路径");
                ConsoleHelper.WriteInfo("  3. 尝试重启 Docker Desktop");
                ConsoleHelper.WriteInfo("  4. 以管理员身份运行此程序");
                return false;
            }
        }

        private bool CreateOutputDirectory(DeploymentConfig config)
        {
            try
            {
                if (Directory.Exists(config.OutputPath))
                {
                    Directory.Delete(config.OutputPath, true);
                }
                Directory.CreateDirectory(config.OutputPath);
                ConsoleHelper.WriteSuccess($"✅ 创建输出目录: {config.OutputPath}");
                return true;
            }
            catch (Exception ex)
            {
                ConsoleHelper.WriteError($"❌ 创建输出目录失败: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> PublishProjectAsync(DeploymentConfig config)
        {
            try
            {
                ConsoleHelper.WriteInfo("发布项目...");

                var projectSettings = _configService.GetProjectSettings();
                var apiProjectPath = Path.Combine(config.ProjectPath, projectSettings.ApiProjectPath);
                var publishPath = Path.Combine(config.OutputPath, "app");

                var command = $"dotnet publish \"{apiProjectPath}\" -c Release -o \"{publishPath}\"";
                var result = await ExecuteCommandAsync(command);

                if (result.ExitCode != 0)
                {
                    ConsoleHelper.WriteError($"❌ 项目发布失败: {result.Output}");
                    return false;
                }

                ConsoleHelper.WriteSuccess("✅ 项目发布完成");
                return true;
            }
            catch (Exception ex)
            {
                ConsoleHelper.WriteError($"❌ 项目发布失败: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> CreateDockerFilesAsync(DeploymentConfig config)
        {
            try
            {
                ConsoleHelper.WriteInfo("创建 Docker 配置文件...");

                // 创建 Dockerfile
                await CreateDockerfileAsync(config);

                // 创建 docker-compose.yml
                await CreateDockerComposeAsync(config);

                // 只在使用 Nginx 代理时创建 nginx 配置
                if (config.UseNginxProxy)
                {
                    await CreateNginxConfigAsync(config);
                }

                ConsoleHelper.WriteSuccess("✅ Docker 配置文件创建完成");
                return true;
            }
            catch (Exception ex)
            {
                ConsoleHelper.WriteError($"❌ 创建 Docker 配置文件失败: {ex.Message}");
                return false;
            }
        }

        private async Task StopExistingContainersAsync(DeploymentConfig config)
        {
            try
            {
                ConsoleHelper.WriteInfo("停止现有容器...");
                
                var dockerComposePath = GetDockerComposePath(config);
                if (File.Exists(dockerComposePath))
                {
                    await ExecuteCommandAsync($"docker-compose -f \"{dockerComposePath}\" down", config.OutputPath);
                }
                
                ConsoleHelper.WriteSuccess("✅ 现有容器已停止");
            }
            catch (Exception ex)
            {
                ConsoleHelper.WriteWarning($"⚠️ 停止现有容器时出现警告: {ex.Message}");
            }
        }

        private async Task<bool> BuildAndStartContainersAsync(DeploymentConfig config)
        {
            try
            {
                ConsoleHelper.WriteInfo("构建并启动容器...");
                
                var dockerComposePath = GetDockerComposePath(config);
                var result = await ExecuteCommandAsync($"docker-compose -f \"{dockerComposePath}\" up -d --build", config.OutputPath);
                
                if (result.ExitCode != 0)
                {
                    ConsoleHelper.WriteError($"❌ 容器启动失败: {result.Output}");
                    return false;
                }

                ConsoleHelper.WriteSuccess("✅ 容器构建并启动成功");
                return true;
            }
            catch (Exception ex)
            {
                ConsoleHelper.WriteError($"❌ 构建启动容器失败: {ex.Message}");
                return false;
            }
        }

        private async Task<bool> VerifyDeploymentAsync(DeploymentConfig config)
        {
            try
            {
                ConsoleHelper.WriteInfo("验证部署状态...");

                // 检查 API 容器状态
                ConsoleHelper.WriteInfo("检查 API 容器状态...");
                var apiResult = await ExecuteCommandAsync($"docker ps --filter name={config.Docker.ApiContainerName} --format \"{{{{.Status}}}}\"");
                if (!apiResult.Output.Contains("Up"))
                {
                    ConsoleHelper.WriteError("❌ API 容器未正常启动");

                    // 显示容器日志
                    ConsoleHelper.WriteInfo("API 容器日志:");
                    var logResult = await ExecuteCommandAsync($"docker logs {config.Docker.ApiContainerName} --tail 20");
                    ConsoleHelper.WriteWarning(logResult.Output);
                    return false;
                }
                ConsoleHelper.WriteSuccess("✅ API 容器运行正常");

                // 如果使用 Nginx，检查 Nginx 容器状态
                if (config.UseNginxProxy)
                {
                    ConsoleHelper.WriteInfo("检查 Nginx 容器状态...");
                    var nginxResult = await ExecuteCommandAsync($"docker ps --filter name={config.Docker.NginxContainerName} --format \"{{{{.Status}}}}\"");
                    if (!nginxResult.Output.Contains("Up"))
                    {
                        ConsoleHelper.WriteError("❌ Nginx 容器未正常启动");

                        // 显示 Nginx 日志
                        ConsoleHelper.WriteInfo("Nginx 容器日志:");
                        var nginxLogResult = await ExecuteCommandAsync($"docker logs {config.Docker.NginxContainerName} --tail 20");
                        ConsoleHelper.WriteWarning(nginxLogResult.Output);
                        return false;
                    }
                    ConsoleHelper.WriteSuccess("✅ Nginx 容器运行正常");
                }

                // 等待服务完全启动
                ConsoleHelper.WriteInfo("等待服务完全启动...");
                await Task.Delay(15000);

                // 验证 API 服务健康状态
                var portMapping = _configService.GetPortMapping(config.Environment);
                var healthCheckUrl = config.UseNginxProxy
                    ? $"http://localhost:{portMapping.NginxPort}/health"
                    : $"http://localhost:{portMapping.ApiExternalPort}/health";

                ConsoleHelper.WriteInfo($"检查服务健康状态: {healthCheckUrl}");

                for (int i = 0; i < 6; i++) // 尝试 6 次，每次间隔 5 秒
                {
                    try
                    {
                        var curlResult = await ExecuteCommandAsync($"curl -s -o /dev/null -w \"%{{http_code}}\" {healthCheckUrl}");
                        if (curlResult.Output.Trim() == "200")
                        {
                            ConsoleHelper.WriteSuccess("✅ 服务健康检查通过");
                            return true;
                        }
                        ConsoleHelper.WriteInfo($"健康检查尝试 {i + 1}/6，HTTP 状态码: {curlResult.Output.Trim()}");
                    }
                    catch
                    {
                        ConsoleHelper.WriteInfo($"健康检查尝试 {i + 1}/6 失败");
                    }

                    if (i < 5) await Task.Delay(5000);
                }

                ConsoleHelper.WriteWarning("⚠️ 服务健康检查未通过，但容器已启动");
                ConsoleHelper.WriteInfo("请手动检查服务状态和日志");
                return true; // 容器启动了，即使健康检查失败也认为部署成功
            }
            catch (Exception ex)
            {
                ConsoleHelper.WriteWarning($"⚠️ 部署验证失败: {ex.Message}");
                return false;
            }
        }

        private void ShowDeploymentInfo(DeploymentConfig config)
        {
            var portMapping = _configService.GetPortMapping(config.Environment);

            ConsoleHelper.WriteTitle("================================================================");
            ConsoleHelper.WriteSuccess("🎉 Docker 部署完成！");
            ConsoleHelper.WriteTitle("================================================================");
            ConsoleHelper.WriteInfo($"环境: {config.Environment}");
            ConsoleHelper.WriteInfo($"部署方式: {(config.UseNginxProxy ? "Docker + Nginx 代理" : "Docker 直接部署")}");
            ConsoleHelper.WriteInfo($"Redis 缓存: {(config.UseRedis ? "已启用" : "已禁用")}");

            if (config.UseNginxProxy)
            {
                ConsoleHelper.WriteInfo($"API 访问地址: http://localhost:{portMapping.NginxPort}/api");
                ConsoleHelper.WriteInfo($"Swagger 文档: http://localhost:{portMapping.NginxPort}/swagger");
                ConsoleHelper.WriteInfo($"健康检查: http://localhost:{portMapping.NginxPort}/health");
            }
            else
            {
                ConsoleHelper.WriteInfo($"API 访问地址: http://localhost:{portMapping.ApiExternalPort}/api");
                ConsoleHelper.WriteInfo($"Swagger 文档: http://localhost:{portMapping.ApiExternalPort}/swagger");
                ConsoleHelper.WriteInfo($"健康检查: http://localhost:{portMapping.ApiExternalPort}/health");
            }

            ConsoleHelper.WriteInfo($"自动重启: {(config.AutoRestartAfterUpdate ? "已启用" : "已禁用")}");
            ConsoleHelper.WriteTitle("================================================================");

            // 故障排除提示
            ConsoleHelper.WriteWarning("💡 如果遇到 502 错误，请检查:");
            ConsoleHelper.WriteInfo("  1. 容器状态: docker ps");
            ConsoleHelper.WriteInfo($"  2. API 日志: docker logs {config.Docker.ApiContainerName}");
            if (config.UseNginxProxy)
            {
                ConsoleHelper.WriteInfo($"  3. Nginx 日志: docker logs {config.Docker.NginxContainerName}");
            }
            ConsoleHelper.WriteInfo("  4. 网络连接: docker network ls");
            ConsoleHelper.WriteInfo($"  5. 端口监听: netstat -an | findstr {portMapping.ApiInternalPort}");
            ConsoleHelper.WriteTitle("================================================================");
        }

        private string GetDockerComposePath(DeploymentConfig config)
        {
            return Path.Combine(config.OutputPath, "docker-compose.yml");
        }

        private async Task<(int ExitCode, string Output)> ExecuteCommandAsync(string command, string workingDirectory = null)
        {
            var processInfo = new ProcessStartInfo("cmd.exe", $"/c {command}")
            {
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                UseShellExecute = false,
                CreateNoWindow = true,
                WorkingDirectory = workingDirectory ?? Directory.GetCurrentDirectory()
            };

            using var process = Process.Start(processInfo);
            var output = await process.StandardOutput.ReadToEndAsync();
            var error = await process.StandardError.ReadToEndAsync();
            await process.WaitForExitAsync();

            var fullOutput = string.IsNullOrEmpty(error) ? output : $"{output}\n{error}";
            return (process.ExitCode, fullOutput);
        }

        private async Task CreateDockerfileAsync(DeploymentConfig config)
        {
            var templates = _configService.GetTemplates();
            var portMapping = _configService.GetPortMapping(config.Environment);

            var dockerfile = templates.DockerfileContent
                .Replace("{INTERNAL_PORT}", portMapping.ApiInternalPort.ToString());

            var dockerfilePath = Path.Combine(config.OutputPath, "Dockerfile");
            await File.WriteAllTextAsync(dockerfilePath, dockerfile);
        }

        private async Task CreateDockerComposeAsync(DeploymentConfig config)
        {
            var templates = _configService.GetTemplates();
            var portMapping = _configService.GetPortMapping(config.Environment);

            var dockerCompose = templates.DockerComposeContent
                .Replace("{API_CONTAINER_NAME}", config.Docker.ApiContainerName)
                .Replace("{ENVIRONMENT}", config.Environment)
                .Replace("{INTERNAL_PORT}", portMapping.ApiInternalPort.ToString())
                .Replace("{NETWORK_NAME}", config.Docker.NetworkName);

            // 配置端口映射
            string portMappingSection = "";
            if (!config.UseNginxProxy)
            {
                portMappingSection = $@"    ports:
      - ""{portMapping.ApiExternalPort}:{portMapping.ApiInternalPort}""";
            }
            dockerCompose = dockerCompose.Replace("{PORT_MAPPING}", portMappingSection);

            // 配置 Nginx 服务
            string nginxService = "";
            if (config.UseNginxProxy)
            {
                nginxService = $@"
  {config.Docker.NginxContainerName}:
    image: nginx:alpine
    container_name: {config.Docker.NginxContainerName}
    ports:
      - ""{portMapping.NginxPort}:{portMapping.NginxPort}""
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf:ro
    depends_on:
      - {config.Docker.ApiContainerName}
    networks:
      - {config.Docker.NetworkName}
    restart: unless-stopped";
            }
            dockerCompose = dockerCompose.Replace("{NGINX_SERVICE}", nginxService);

            var dockerComposePath = Path.Combine(config.OutputPath, "docker-compose.yml");
            await File.WriteAllTextAsync(dockerComposePath, dockerCompose);
        }

        private async Task CreateNginxConfigAsync(DeploymentConfig config)
        {
            var templates = _configService.GetTemplates();
            var portMapping = _configService.GetPortMapping(config.Environment);

            var nginxConfig = templates.NginxConfigContent
                .Replace("{API_CONTAINER_NAME}", config.Docker.ApiContainerName)
                .Replace("{INTERNAL_PORT}", portMapping.ApiInternalPort.ToString())
                .Replace("{NGINX_PORT}", portMapping.NginxPort.ToString())
                .Replace("{ENVIRONMENT}", config.Environment);

            var nginxConfigPath = Path.Combine(config.OutputPath, "nginx.conf");
            await File.WriteAllTextAsync(nginxConfigPath, nginxConfig);
        }

        /// <summary>
        /// 清理临时发布文件夹
        /// </summary>
        private async Task CleanupPublishDirectoryAsync(DeploymentConfig config)
        {
            try
            {
                ConsoleHelper.WriteInfo("清理临时发布文件夹...");

                if (Directory.Exists(config.OutputPath))
                {
                    // 等待一小段时间确保文件不被占用
                    await Task.Delay(1000);

                    // 递归删除目录及其所有内容
                    Directory.Delete(config.OutputPath, true);
                    ConsoleHelper.WriteSuccess($"✅ 已清理临时文件夹: {config.OutputPath}");
                }
                else
                {
                    ConsoleHelper.WriteInfo("💡 临时文件夹不存在，无需清理");
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                ConsoleHelper.WriteWarning($"⚠️ 清理文件夹权限不足: {ex.Message}");
                ConsoleHelper.WriteInfo("💡 请手动删除临时文件夹或以管理员权限运行");
            }
            catch (DirectoryNotFoundException)
            {
                ConsoleHelper.WriteInfo("💡 临时文件夹已不存在");
            }
            catch (IOException ex)
            {
                ConsoleHelper.WriteWarning($"⚠️ 清理文件夹时发生IO错误: {ex.Message}");
                ConsoleHelper.WriteInfo("💡 可能有文件正在被使用，请稍后手动删除");
            }
            catch (Exception ex)
            {
                ConsoleHelper.WriteWarning($"⚠️ 清理文件夹失败: {ex.Message}");
            }
        }
    }
}
