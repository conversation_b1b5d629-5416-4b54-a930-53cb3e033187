# CYSF API 统一发布管理系统

## 📋 概述

CYSF.Publish 是一个统一的发布管理控制台程序，支持多环境部署、配置管理和服务更新。

## 🚀 功能特性

### 支持的环境
- **Development** - 开发环境
- **Staging** - 测试环境  
- **Production** - 生产环境

### 支持的部署方式
- **Docker 直接部署** - 容器化部署，直接暴露 API 端口
- **Docker + Nginx** - 容器化部署，使用 Nginx 作为反向代理
- **IIS 部署** - Windows IIS 部署

### 核心功能
- ✅ 环境部署 - 部署到指定环境
- ✅ 服务更新 - 更新现有服务
- ✅ 停止服务 - 停止运行中的服务
- ✅ 配置更新 - 仅更新配置文件
- ✅ 动态配置 - 数据库和 Redis 连接配置
- ✅ 可选 Redis - Redis 缓存可选启用/禁用
- ✅ 自动重启 - 版本更新后自动重启服务

## ⚙️ 配置文件

所有默认配置都存储在 `appsettings.json` 文件中，可以直接修改：

```json
{
  "PublishSettings": {
    "DefaultEnvironment": "Production",
    "DefaultDeploymentType": "Docker",
    "AutoRestartAfterUpdate": true,
    "UseNginxProxy": false,
    "UseRedis": true,
    "Environments": {
      "Development": {
        "Database": {
          "Server": "localhost,1433",
          "Database": "CYSF",
          "UserId": "sa",
          "Password": "Pass@word1"
        },
        "Redis": {
          "Host": "localhost",
          "Port": 6379,
          "Password": "penjay_",
          "Database": 2
        }
      }
    }
  }
}
```

## 🛠️ 使用方法

### 运行程序
```bash
cd src/Core/CYSF.Publish
dotnet run
```

### 主菜单选项
1. **环境部署** - 完整的环境部署流程
2. **服务更新** - 快速更新现有服务
3. **停止服务** - 停止指定环境的服务
4. **配置更新** - 仅更新 appsettings 配置
5. **帮助信息** - 查看详细使用说明

### 部署流程
1. 选择目标环境（Development/Staging/Production）
2. 选择部署方式（Docker/Docker+Nginx/IIS）
3. 配置数据库连接
4. 配置 Redis 连接（可选）
5. 配置其他选项（Redis 启用/禁用、自动重启等）
6. 确认配置并开始部署

## 📁 输出目录

部署文件将输出到：
```
{ProjectRoot}/publish/{environment}/
├── app/                    # 发布的应用文件
├── Dockerfile             # Docker 镜像文件
├── docker-compose.yml     # Docker Compose 配置
└── nginx.conf             # Nginx 配置（如果使用代理）
```

## 🔧 配置说明

### 数据库配置
- 支持 SQL Server 连接
- 可配置服务器地址、数据库名、用户名密码
- 支持 Windows 身份验证

### Redis 配置
- 完全可选，可以禁用分布式缓存
- 支持密码认证
- 可配置不同的数据库索引

### Docker 配置
- 支持直接部署和 Nginx 代理两种模式
- 自动配置端口映射
- 支持自定义网络和容器名称

### IIS 配置
- 自动创建应用程序池和网站
- 支持自定义站点路径和端口
- 自动生成 web.config 配置

## 🚨 注意事项

1. **非侵入性** - 除了修改对应环境的 appsettings 配置外，不会修改其他项目文件
2. **环境隔离** - 不同环境使用不同的容器名称和端口，避免冲突
3. **配置备份** - 建议在部署前备份现有配置文件
4. **权限要求** - IIS 部署需要管理员权限

## 🔄 更新流程

版本更新后，如果启用了自动重启功能：
1. 自动停止现有服务
2. 重新构建应用
3. 更新容器/应用
4. 自动启动服务
5. 验证部署状态

## 📞 故障排除

### 常见问题
1. **Docker 未运行** - 确保 Docker Desktop 已启动
2. **端口冲突** - 检查配置文件中的端口设置
3. **权限不足** - IIS 部署需要管理员权限
4. **配置文件错误** - 检查 appsettings.json 格式

### 日志查看
- Docker 部署：使用 `docker-compose logs` 查看日志
- IIS 部署：查看 IIS 管理器中的应用程序日志

---

**版本**: 1.0  
**更新时间**: 2024-12-27  
**维护团队**: CYSF Development Team
