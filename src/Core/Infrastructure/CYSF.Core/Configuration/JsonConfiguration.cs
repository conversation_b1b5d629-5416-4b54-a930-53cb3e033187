using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using CYSF.Core.Extensions;

namespace CYSF.Core.Configuration
{
    /// <summary>
    /// JSON序列化配置
    /// </summary>
    public static class JsonConfiguration
    {
        /// <summary>
        /// 获取默认的JSON序列化设置
        /// </summary>
        /// <returns>JSON序列化设置</returns>
        public static JsonSerializerSettings GetDefaultSettings()
        {
            return new JsonSerializerSettings
            {
                // 日期格式
                DateFormatString = "yyyy-MM-dd HH:mm:ss",
                
                // 忽略null值
                NullValueHandling = NullValueHandling.Ignore,
                
                // 忽略循环引用
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                
                // 使用默认的属性名解析器
                ContractResolver = new DefaultContractResolver(),
                
                // 缩进格式化（开发环境可以设置为true）
                Formatting = Formatting.None,
                
                // 时区处理
                DateTimeZoneHandling = DateTimeZoneHandling.Local,
                
                // 默认值处理
                DefaultValueHandling = DefaultValueHandling.Include
            };
        }

        /// <summary>
        /// 获取驼峰命名的JSON序列化设置
        /// </summary>
        /// <returns>JSON序列化设置</returns>
        public static JsonSerializerSettings GetCamelCaseSettings()
        {
            var settings = GetDefaultSettings();
            settings.ContractResolver = new CamelCasePropertyNamesContractResolver();
            return settings;
        }

        /// <summary>
        /// 获取长整型转字符串的JSON序列化设置
        /// </summary>
        /// <returns>JSON序列化设置</returns>
        public static JsonSerializerSettings GetLongToStringSettings()
        {
            var settings = GetDefaultSettings();
            settings.ContractResolver = new LongToStringStringResolver();
            return settings;
        }

        /// <summary>
        /// 获取小写属性名的JSON序列化设置
        /// </summary>
        /// <returns>JSON序列化设置</returns>
        public static JsonSerializerSettings GetLowerCaseSettings()
        {
            var settings = GetDefaultSettings();
            settings.ContractResolver = new CYSF.Core.Providers.JsonContractResolver();
            return settings;
        }

        /// <summary>
        /// 获取开发环境的JSON序列化设置（格式化输出）
        /// </summary>
        /// <returns>JSON序列化设置</returns>
        public static JsonSerializerSettings GetDevelopmentSettings()
        {
            var settings = GetDefaultSettings();
            settings.Formatting = Formatting.Indented;
            return settings;
        }
    }
}
