using System.ComponentModel;

namespace CYSF.Core.Enums
{
    /// <summary>
    ///     接口返回状态码
    /// </summary>
    public enum ApiStatusCode
    {
        /// <summary>
        ///     执行成功
        /// </summary>
        [Description("执行成功")] Success = 200,

        /// <summary>
        ///     逻辑异常
        ///     (模型验证,逻辑处理等)
        /// </summary>
        [Description("逻辑异常")] LogicException = 201,

        /// <summary>
        ///     未登录,用户身份非法
        /// </summary>
        [Description("未登录")] Unauthorized = 401,

        /// <summary>
        ///     Token过期,无法刷新有效身份
        /// </summary>
        [Description("Token过期")] SecurityTokenExpired = 402,

        /// <summary>
        ///     无权限
        /// </summary>
        [Description("无权限")] NonPermission = 403,

        /// <summary>
        ///     未找到
        /// </summary>
        [Description("未找到")] Notfound = 404
    }
}