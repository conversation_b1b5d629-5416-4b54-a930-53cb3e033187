using System;
using CYSF.Core.Enums;
using CYSF.Core.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace CYSF.Core.Attributes
{
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
    public class ApiAuthorizeAttribute : ActionFilterAttribute, IAuthorizationFilter
    {
        public void OnAuthorization(AuthorizationFilterContext context)
        {
            var httpContext = context.HttpContext;
            var identity = httpContext.User.Identity;
            if (!identity.IsAuthenticated)
                context.Result = new ObjectResult(new ApiResult
                {
                    message = string.Empty,
                    code = ApiStatusCode.Unauthorized
                });
        }
    }
}