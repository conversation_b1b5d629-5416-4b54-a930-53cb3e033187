<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <LangVersion>latest</LangVersion>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="10.0.0" />
        <PackageReference Include="Autofac.Extras.DynamicProxy" Version="7.1.0" />
        <PackageReference Include="Caching.CSRedis" Version="3.8.800" />
        <PackageReference Include="CSRedisCore" Version="3.8.800" />
        <PackageReference Include="EPPlus" Version="4.5.3.3" />
        <PackageReference Include="Hangfire.AspNetCore" Version="1.8.20" />
        <PackageReference Include="Hangfire.Core" Version="1.8.20" />
        <PackageReference Include="Mapster" Version="7.4.0" />
        <PackageReference Include="Microsoft.AspNetCore.Cryptography.KeyDerivation" Version="8.0.12" />
        <PackageReference Include="Microsoft.AspNetCore.Routing" Version="2.2.2" />
        <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.Configuration.FileExtensions" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="8.0.1" />
        <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.Options" Version="8.0.2" />
        <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
        <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.2.1" />
        <PackageReference Include="Microsoft.IO.RecyclableMemoryStream" Version="3.0.1" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
        <PackageReference Include="NLog.Extensions.Logging" Version="5.3.14" />
        <PackageReference Include="NLog.Web.AspNetCore" Version="5.3.14" />
        <PackageReference Include="NLog.WindowsIdentity" Version="5.3.0" />
        <PackageReference Include="SqlSugar.IOC" Version="2.0.0" />
        <PackageReference Include="sqlSugarCore" Version="5.1.4.196" />
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.2.1" />
        <PackageReference Include="System.Security.Cryptography.Pkcs" Version="8.0.1" />
        <PackageReference Include="XC.RSAUtil" Version="1.3.6" />
        <PackageReference Include="ZKWeb.System.Drawing" Version="4.0.1" />
    </ItemGroup>

</Project>
