using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace CYSF.Core.Helpers
{
    public class RsaHelper
    {
        /// <summary>
        ///     签名(SHA1)
        /// </summary>
        /// <param name="content">待签名字符串</param>
        /// <param name="privateKey">私钥</param>
        /// <param name="inputCharset">编码格式</param>
        /// <returns>签名后字符串</returns>
        public static string Sign(string content, string privateKey, string inputCharset)
        {
            var data = Encoding.GetEncoding(inputCharset).GetBytes(content);
            var rsa = DecodePemPrivateKey(privateKey);
            var sh = SHA1.Create();
            var signData = rsa.SignData(data, sh);
            return Convert.ToBase64String(signData);
        }

        /// <summary>
        ///     签名(SHA256)
        /// </summary>
        /// <param name="content">待签名字符串</param>
        /// <param name="privateKey">私钥</param>
        /// <param name="inputCharset">编码格式</param>
        /// <returns>签名后字符串</returns>
        public static string SignWithRsasha256(string content, string privateKey, string inputCharset)
        {
            var data = Encoding.GetEncoding(inputCharset).GetBytes(content);
            var rsa = DecodePemPrivateKey(privateKey);
            using var sh = SHA256.Create();
            var signData = rsa.SignData(data, sh);
            return Convert.ToBase64String(signData);
        }

        /// <summary>
        ///     验签
        /// </summary>
        /// <param name="content">待验签字符串</param>
        /// <param name="signedString">签名</param>
        /// <param name="publicKey">公钥</param>
        /// <param name="inputCharset">编码格式</param>
        /// <returns>true(通过)，false(不通过)</returns>
        public static bool VerifySha1(string content, string signedString, string publicKey, string inputCharset)
        {
            var result = false;
            var buffer = Encoding.GetEncoding(inputCharset).GetBytes(content);
            var data = Convert.FromBase64String(signedString);
            var paraPub = ConvertFromPublicKey(publicKey);
            var rsaPub = new RSACryptoServiceProvider();
            rsaPub.ImportParameters(paraPub);
            using var sh = SHA1.Create();
            result = rsaPub.VerifyData(buffer, sh, data);
            return result;
        }

        /// <summary>
        ///     验签
        /// </summary>
        /// <param name="content">待验签字符串</param>
        /// <param name="signedString">签名</param>
        /// <param name="publicKey">公钥</param>
        /// <param name="inputCharset">编码格式</param>
        /// <returns>true(通过)，false(不通过)</returns>
        public static bool VerifySha256(string content, string signedString, string publicKey, string inputCharset)
        {
            var result = false;
            var buffer = Encoding.GetEncoding(inputCharset).GetBytes(content);
            var data = Convert.FromBase64String(signedString);
            var paraPub = ConvertFromPublicKey(publicKey);
            var rsaPub = new RSACryptoServiceProvider();
            rsaPub.ImportParameters(paraPub);
            using var sh = SHA256.Create();
            result = rsaPub.VerifyData(buffer, sh, data);
            return result;
        }

        /// <summary>
        ///     加密
        /// </summary>
        /// <param name="resData">需要加密的字符串</param>
        /// <param name="publicKey">公钥</param>
        /// <param name="input_charset">编码格式</param>
        /// <returns>明文</returns>
        public static string EncryptData(string resData, string publicKey, string inputCharset)
        {
            var dataToEncrypt = Encoding.ASCII.GetBytes(resData);
            var result = Encrypt(dataToEncrypt, publicKey, inputCharset);
            return result;
        }


        /// <summary>
        ///     解密
        /// </summary>
        /// <param name="resData">加密字符串</param>
        /// <param name="privateKey">私钥</param>
        /// <param name="input_charset">编码格式</param>
        /// <returns>明文</returns>
        public static string DecryptData(string resData, string privateKey, string inputCharset)
        {
            var dataToDecrypt = Convert.FromBase64String(resData);
            var result = "";
            for (var j = 0; j < dataToDecrypt.Length / 128; j++)
            {
                var buf = new byte[128];
                for (var i = 0; i < 128; i++) buf[i] = dataToDecrypt[i + 128 * j];
                result += Decrypt(buf, privateKey, inputCharset);
            }

            return result;
        }

        #region 内部方法

        private static string Encrypt(byte[] data, string publicKey, string inputCharset)
        {
            var rsa = DecodePemPublicKey(publicKey);
            using var sh = SHA1.Create();
            var result = rsa.Encrypt(data, false);

            return Convert.ToBase64String(result);
        }


        private static string Decrypt(byte[] data, string privateKey, string inputCharset)
        {
            var result = "";
            var rsa = DecodePemPrivateKey(privateKey);
            using var sh = SHA1.Create();
            var source = rsa.Decrypt(data, false);
            var asciiChars = new char[Encoding.GetEncoding(inputCharset).GetCharCount(source, 0, source.Length)];
            Encoding.GetEncoding(inputCharset).GetChars(source, 0, source.Length, asciiChars, 0);
            result = new string(asciiChars);
            //result = ASCIIEncoding.ASCII.GetString(source);
            return result;
        }


        private static RSACryptoServiceProvider DecodePemPublicKey(string pemstr)
        {
            byte[] pkcs8Publickkey;
            pkcs8Publickkey = Convert.FromBase64String(pemstr);
            if (pkcs8Publickkey != null)
            {
                var rsa = DecodeRsaPublicKey(pkcs8Publickkey);
                return rsa;
            }

            return null;
        }

        private static RSACryptoServiceProvider DecodePemPrivateKey(string pemstr)
        {
            byte[] pkcs8Privatekey;
            pkcs8Privatekey = Convert.FromBase64String(pemstr);
            if (pkcs8Privatekey != null)
            {
                var rsa = DecodePrivateKeyInfo(pkcs8Privatekey);
                return rsa;
            }

            return null;
        }


        private static RSACryptoServiceProvider DecodePrivateKeyInfo(byte[] pkcs8)
        {
            byte[] seqOid = { 0x30, 0x0D, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x01, 0x05, 0x00 };
            var seq = new byte[15];


            var mem = new MemoryStream(pkcs8);
            var lenstream = (int)mem.Length;
            var binr = new BinaryReader(mem); //wrap Memory Stream with BinaryReader for easy reading
            byte bt = 0;
            ushort twobytes = 0;


            try
            {
                twobytes = binr.ReadUInt16();
                if (twobytes == 0x8130) //data read as little endian order (actual data order for Sequence is 30 81)
                    binr.ReadByte(); //advance 1 byte
                else if (twobytes == 0x8230)
                    binr.ReadInt16(); //advance 2 bytes
                else
                    return null;


                bt = binr.ReadByte();
                if (bt != 0x02)
                    return null;


                twobytes = binr.ReadUInt16();


                if (twobytes != 0x0001)
                    return null;


                seq = binr.ReadBytes(15); //read the Sequence OID
                if (!CompareBytearrays(seq, seqOid)) //make sure Sequence for OID is correct
                    return null;


                bt = binr.ReadByte();
                if (bt != 0x04) //expect an Octet string
                    return null;


                bt = binr.ReadByte(); //read next byte, or next 2 bytes is  0x81 or 0x82; otherwise bt is the byte count
                if (bt == 0x81)
                    binr.ReadByte();
                else if (bt == 0x82)
                    binr.ReadUInt16();
                //------ at this stage, the remaining sequence should be the RSA private key


                var rsaprivkey = binr.ReadBytes((int)(lenstream - mem.Position));
                var rsacsp = DecodeRsaPrivateKey(rsaprivkey);
                return rsacsp;
            }


            catch (Exception)
            {
                return null;
            }


            finally
            {
                binr.Close();
            }
        }


        private static bool CompareBytearrays(byte[] a, byte[] b)
        {
            if (a.Length != b.Length)
                return false;
            var i = 0;
            foreach (var c in a)
            {
                if (c != b[i])
                    return false;
                i++;
            }

            return true;
        }


        private static RSACryptoServiceProvider DecodeRsaPublicKey(byte[] publickey)
        {
            // encoded OID sequence for  PKCS #1 rsaEncryption szOID_RSA_RSA = "1.2.840.113549.1.1.1"
            byte[] seqOid = { 0x30, 0x0D, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x01, 0x05, 0x00 };
            var seq = new byte[15];
            // ---------  Set up stream to read the asn.1 encoded SubjectPublicKeyInfo blob  ------
            var mem = new MemoryStream(publickey);
            var binr = new BinaryReader(mem); //wrap Memory Stream with BinaryReader for easy reading
            byte bt = 0;
            ushort twobytes = 0;


            try
            {
                twobytes = binr.ReadUInt16();
                if (twobytes == 0x8130) //data read as little endian order (actual data order for Sequence is 30 81)
                    binr.ReadByte(); //advance 1 byte
                else if (twobytes == 0x8230)
                    binr.ReadInt16(); //advance 2 bytes
                else
                    return null;


                seq = binr.ReadBytes(15); //read the Sequence OID
                if (!CompareBytearrays(seq, seqOid)) //make sure Sequence for OID is correct
                    return null;


                twobytes = binr.ReadUInt16();
                if (twobytes == 0x8103) //data read as little endian order (actual data order for Bit String is 03 81)
                    binr.ReadByte(); //advance 1 byte
                else if (twobytes == 0x8203)
                    binr.ReadInt16(); //advance 2 bytes
                else
                    return null;


                bt = binr.ReadByte();
                if (bt != 0x00) //expect null byte next
                    return null;


                twobytes = binr.ReadUInt16();
                if (twobytes == 0x8130) //data read as little endian order (actual data order for Sequence is 30 81)
                    binr.ReadByte(); //advance 1 byte
                else if (twobytes == 0x8230)
                    binr.ReadInt16(); //advance 2 bytes
                else
                    return null;


                twobytes = binr.ReadUInt16();
                byte lowbyte = 0x00;
                byte highbyte = 0x00;


                if (twobytes == 0x8102) //data read as little endian order (actual data order for Integer is 02 81)
                {
                    lowbyte = binr.ReadByte(); // read next bytes which is bytes in modulus
                }
                else if (twobytes == 0x8202)
                {
                    highbyte = binr.ReadByte(); //advance 2 bytes
                    lowbyte = binr.ReadByte();
                }
                else
                {
                    return null;
                }

                byte[] modint = { lowbyte, highbyte, 0x00, 0x00 }; //reverse byte order since asn.1 key uses big endian order
                var modsize = BitConverter.ToInt32(modint, 0);


                var firstbyte = binr.ReadByte();
                binr.BaseStream.Seek(-1, SeekOrigin.Current);


                if (firstbyte == 0x00)
                {
                    //if first byte (highest order) of modulus is zero, don't include it
                    binr.ReadByte(); //skip this null byte
                    modsize -= 1; //reduce modulus buffer size by 1
                }


                var modulus = binr.ReadBytes(modsize); //read the modulus bytes


                if (binr.ReadByte() != 0x02) //expect an Integer for the exponent data
                    return null;
                int expbytes = binr.ReadByte(); // should only need one byte for actual exponent data (for all useful values)
                var exponent = binr.ReadBytes(expbytes);


                // ------- create RSACryptoServiceProvider instance and initialize with public key -----
                var rsa = new RSACryptoServiceProvider();
                var rsaKeyInfo = new RSAParameters();
                rsaKeyInfo.Modulus = modulus;
                rsaKeyInfo.Exponent = exponent;
                rsa.ImportParameters(rsaKeyInfo);
                return rsa;
            }
            catch (Exception)
            {
                return null;
            }


            finally
            {
                binr.Close();
            }
        }


        private static RSACryptoServiceProvider DecodeRsaPrivateKey(byte[] privkey)
        {
            byte[] modulus, e, d, p, q, dp, dq, iq;


            // ---------  Set up stream to decode the asn.1 encoded RSA private key  ------
            var mem = new MemoryStream(privkey);
            var binr = new BinaryReader(mem); //wrap Memory Stream with BinaryReader for easy reading
            byte bt = 0;
            ushort twobytes = 0;
            var elems = 0;
            try
            {
                twobytes = binr.ReadUInt16();
                if (twobytes == 0x8130) //data read as little endian order (actual data order for Sequence is 30 81)
                    binr.ReadByte(); //advance 1 byte
                else if (twobytes == 0x8230)
                    binr.ReadInt16(); //advance 2 bytes
                else
                    return null;


                twobytes = binr.ReadUInt16();
                if (twobytes != 0x0102) //version number
                    return null;
                bt = binr.ReadByte();
                if (bt != 0x00)
                    return null;


                //------  all private key components are Integer sequences ----
                elems = GetIntegerSize(binr);
                modulus = binr.ReadBytes(elems);


                elems = GetIntegerSize(binr);
                e = binr.ReadBytes(elems);


                elems = GetIntegerSize(binr);
                d = binr.ReadBytes(elems);


                elems = GetIntegerSize(binr);
                p = binr.ReadBytes(elems);


                elems = GetIntegerSize(binr);
                q = binr.ReadBytes(elems);


                elems = GetIntegerSize(binr);
                dp = binr.ReadBytes(elems);


                elems = GetIntegerSize(binr);
                dq = binr.ReadBytes(elems);


                elems = GetIntegerSize(binr);
                iq = binr.ReadBytes(elems);


                // ------- create RSACryptoServiceProvider instance and initialize with public key -----
                var rsa = new RSACryptoServiceProvider();
                var rsAparams = new RSAParameters();
                rsAparams.Modulus = modulus;
                rsAparams.Exponent = e;
                rsAparams.D = d;
                rsAparams.P = p;
                rsAparams.Q = q;
                rsAparams.DP = dp;
                rsAparams.DQ = dq;
                rsAparams.InverseQ = iq;
                rsa.ImportParameters(rsAparams);
                return rsa;
            }
            catch (Exception)
            {
                return null;
            }
            finally
            {
                binr.Close();
            }
        }


        private static int GetIntegerSize(BinaryReader binr)
        {
            byte bt = 0;
            byte lowbyte = 0x00;
            byte highbyte = 0x00;
            var count = 0;
            bt = binr.ReadByte();
            if (bt != 0x02) //expect integer
                return 0;
            bt = binr.ReadByte();


            if (bt == 0x81)
            {
                count = binr.ReadByte(); // data size in next byte
            }
            else if (bt == 0x82)
            {
                highbyte = binr.ReadByte(); // data size in next 2 bytes
                lowbyte = binr.ReadByte();
                byte[] modint = { lowbyte, highbyte, 0x00, 0x00 };
                count = BitConverter.ToInt32(modint, 0);
            }
            else
            {
                count = bt; // we already have the data size
            }


            while (binr.ReadByte() == 0x00)
                //remove high order zeros in data
                count -= 1;
            binr.BaseStream.Seek(-1, SeekOrigin.Current); //last ReadByte wasn't a removed zero, so back up a byte
            return count;
        }

        #endregion


        #region 解析.net 生成的Pem

        private static RSAParameters ConvertFromPublicKey(string pemFileConent)
        {
            var keyData = Convert.FromBase64String(pemFileConent);
            if (keyData.Length < 162) throw new ArgumentException("pem file content is incorrect.");
            var pemModulus = new byte[128];
            var pemPublicExponent = new byte[3];
            Array.Copy(keyData, 29, pemModulus, 0, 128);
            Array.Copy(keyData, 159, pemPublicExponent, 0, 3);
            var para = new RSAParameters();
            para.Modulus = pemModulus;
            para.Exponent = pemPublicExponent;
            return para;
        }


        private static RSAParameters ConvertFromPrivateKey(string pemFileConent)
        {
            var keyData = Convert.FromBase64String(pemFileConent);
            if (keyData.Length < 609) throw new ArgumentException("pem file content is incorrect.");


            var index = 11;
            var pemModulus = new byte[128];
            Array.Copy(keyData, index, pemModulus, 0, 128);


            index += 128;
            index += 2; //141
            var pemPublicExponent = new byte[3];
            Array.Copy(keyData, index, pemPublicExponent, 0, 3);


            index += 3;
            index += 4; //148
            var pemPrivateExponent = new byte[128];
            Array.Copy(keyData, index, pemPrivateExponent, 0, 128);


            index += 128;
            index += keyData[index + 1] == 64 ? 2 : 3; //279
            var pemPrime1 = new byte[64];
            Array.Copy(keyData, index, pemPrime1, 0, 64);


            index += 64;
            index += keyData[index + 1] == 64 ? 2 : 3; //346
            var pemPrime2 = new byte[64];
            Array.Copy(keyData, index, pemPrime2, 0, 64);


            index += 64;
            index += keyData[index + 1] == 64 ? 2 : 3; //412/413
            var pemExponent1 = new byte[64];
            Array.Copy(keyData, index, pemExponent1, 0, 64);


            index += 64;
            index += keyData[index + 1] == 64 ? 2 : 3; //479/480
            var pemExponent2 = new byte[64];
            Array.Copy(keyData, index, pemExponent2, 0, 64);


            index += 64;
            index += keyData[index + 1] == 64 ? 2 : 3; //545/546
            var pemCoefficient = new byte[64];
            Array.Copy(keyData, index, pemCoefficient, 0, 64);


            var para = new RSAParameters();
            para.Modulus = pemModulus;
            para.Exponent = pemPublicExponent;
            para.D = pemPrivateExponent;
            para.P = pemPrime1;
            para.Q = pemPrime2;
            para.DP = pemExponent1;
            para.DQ = pemExponent2;
            para.InverseQ = pemCoefficient;
            return para;
        }

        #endregion
    }
}