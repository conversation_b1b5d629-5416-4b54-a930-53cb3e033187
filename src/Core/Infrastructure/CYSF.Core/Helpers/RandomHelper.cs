using System;

namespace CYSF.Core.Helpers
{
    public class RandomHelper
    {
        public enum VerifyCodeType
        {
            NumberVerifyCode,

            AbcVerifyCode,

            MixVerifyCode
        }

        // 创建私有化静态obj锁  
        private static readonly object ObjLock = new object();

        // 创建私有静态字段，接收类的实例化对象  
        private static RandomHelper _randomHelper;

        // 构造函数私有化  
        private RandomHelper()
        {
        }

        // 创建单利对象资源并返回  
        public static RandomHelper Instance()
        {
            if (_randomHelper == null)

                lock (ObjLock)
                {
                    if (_randomHelper == null)

                        _randomHelper = new RandomHelper();
                }

            return _randomHelper;
        }

        /// <summary>
        ///     产生验证码（随机产生4-6位）
        /// </summary>
        /// <param name="type">验证码类型：数字，字符，符合</param>
        /// <param name="length">验证码长度</param>
        /// <param name="lower">格式化成小写</param>
        /// <returns></returns>
        public string CreateVerifyCode(VerifyCodeType type = VerifyCodeType.NumberVerifyCode, int length = 6, bool lower = false)
        {
            string verifyCode;
            switch (type)
            {
                case VerifyCodeType.AbcVerifyCode:
                    verifyCode = Instance().CreateAbcVerifyCode(length, lower);
                    break;
                case VerifyCodeType.MixVerifyCode:
                    verifyCode = Instance().CreateMixVerifyCode(length);
                    break;
                case VerifyCodeType.NumberVerifyCode:
                    verifyCode = Instance().CreateNumberVerifyCode(length);
                    break;
                default:
                    verifyCode = Instance().CreateNumberVerifyCode(length);
                    break;
            }

            return verifyCode;
        }

        /// <summary>
        ///     2.字母验证码
        /// </summary>
        /// <param name="length">字符长度</param>
        /// <param name="lower">是否格式化成小写</param>
        /// <returns>验证码字符</returns>
        private string CreateAbcVerifyCode(int length, bool lower = false)
        {
            var verification = new char[length];

            char[] dictionary =
            {
                'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S',
                'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k',
                'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z' //'I', 'l',
            };

            var random = new Random();

            for (var i = 0; i < length; i++) verification[i] = dictionary[random.Next(dictionary.Length - 1)];

            var res = new string(verification);
            return lower ? res.ToLower() : res;
        }

        /// <summary>
        ///     3.混合验证码
        /// </summary>
        /// <param name="length">字符长度</param>
        /// <returns>验证码字符</returns>
        private string CreateMixVerifyCode(int length)
        {
            var verification = new char[length];
            char[] dictionary =
            {
                'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'S', // 'O','I', 
                'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '2', '3', '4', '5', '6', '7', '8', '9', // '0','1',
                'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'm', 'n', 'p', 'q', 'r', 's', // 'o', 'l', 
                't', 'u', 'v', 'w', 'x', 'y', 'z'
            };
            var random = new Random();
            for (var i = 0; i < length; i++) verification[i] = dictionary[random.Next(dictionary.Length - 1)];
            return new string(verification);
        }

        /// <summary>
        ///     1.数字验证码
        /// </summary>
        /// <param name="length"></param>
        /// <returns></returns>
        private string CreateNumberVerifyCode(int length)
        {
            var randMembers = new int[length];
            var validateNums = new int[length];
            var validateNumberStr = string.Empty;

            // 生成起始序列值  
            var seekSeek = unchecked((int)DateTime.Now.Ticks);
            var seekRand = new Random(seekSeek);
            var beginSeek = seekRand.Next(0, int.MaxValue - length * 10000);
            var seeks = new int[length];
            for (var i = 0; i < length; i++)
            {
                beginSeek += 10000;
                seeks[i] = beginSeek;
            }

            // 生成随机数字  
            for (var i = 0; i < length; i++)
            {
                var rand = new Random(seeks[i]);
                var pownum = 1 * (int)Math.Pow(10, length);
                randMembers[i] = rand.Next(pownum, int.MaxValue);
            }

            // 抽取随机数字  
            for (var i = 0; i < length; i++)
            {
                var numStr = randMembers[i].ToString();
                var numLength = numStr.Length;
                var rand = new Random();
                var numPosition = rand.Next(0, numLength - 1);
                validateNums[i] = int.Parse(numStr.Substring(numPosition, 1));
            }

            // 生成验证码  
            for (var i = 0; i < length; i++) validateNumberStr += validateNums[i].ToString();

            return validateNumberStr;
        }
    }
}