using System;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.WebUtilities;

namespace CYSF.Core.Helpers
{
    public static class UrlHelper
    {
        public static RedirectContext<CookieAuthenticationOptions> RebuildRedirectUri(string memberUri, RedirectContext<CookieAuthenticationOptions> context)
        {
            if (context.RedirectUri.Contains(memberUri)) return context;
            var originUri = new Uri(context.RedirectUri);
            var uriBuilder = new UriBuilder(memberUri)
            {
                Path = originUri.AbsolutePath
            };
            var queryStrings = QueryHelpers.ParseQuery(originUri.Query);
            var returnUrlName = context.Options.ReturnUrlParameter;
            var returnUrl = originUri.GetComponents(UriComponents.SchemeAndServer, UriFormat.Unescaped) + queryStrings[returnUrlName];
            uriBuilder.Query = QueryString.Create(returnUrlName, returnUrl).ToString();
            context.RedirectUri = uriBuilder.ToString();
            return context;
        }
    }
}