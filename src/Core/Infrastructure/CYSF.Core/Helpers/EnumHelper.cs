using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Reflection;
using CYSF.Core.Models;

namespace CYSF.Core.Helpers
{
    public static class EnumHelper
    {
        public static List<EnumItem> GetEnumList(string enumName, string assemblyName = "CYSF.Models", string fullNamePrefix = "CYSF.Models.Enum.")
        {
            // var localizer = DependencyResolver.Current.GetService<IStringLocalizer>();
            var res = new List<EnumItem>();
            var types = Assembly.Load(assemblyName).GetTypes().Where(r => r.FullName != null && r.FullName.StartsWith(fullNamePrefix) && r.Name.Equals(enumName)).ToList();
            foreach (var type in types)
            foreach (var item in type.GetFields())
                if (item.GetCustomAttribute(typeof(DescriptionAttribute), true) is DescriptionAttribute attr && !string.IsNullOrEmpty(attr.Description))
                {
                    // var isLocalizable = item.GetCustomAttribute(typeof(LocalizableAttribute), true) is LocalizableAttribute localizable && localizable.IsLocalizable;
                    var enumItem = new EnumItem
                    {
                        Key = (int)Enum.Parse(type, item.Name),
                        Description = attr.Description //isLocalizable ? localizer[attr.Description] : attr.Description
                    };
                    res.Add(enumItem);
                }

            return res;
        }

        // public static Dictionary<string, List<EnumItem>> GetAllEnumList(string assemblyName = "CYSF.Models", string fullNamePrefix = "CYSF.Models.Enum.")
        // {
        //     var res = new Dictionary<string, List<EnumItem>>();
        //     var types = Assembly.Load(assemblyName).GetTypes().Where(r => r.FullName != null && r.FullName.StartsWith(fullNamePrefix)).ToList();
        //     foreach (var type in types)
        //     {
        //         var temp = new List<EnumItem>();
        //         foreach (var item in type.GetFields())
        //             if (item.GetCustomAttribute(typeof(DescriptionAttribute), true) is DescriptionAttribute attr && !string.IsNullOrEmpty(attr.Description))
        //                 temp.Add(new EnumItem
        //                 {
        //                     Key = (int) Enum.Parse(type, item.Name),
        //                     Description = attr.Description
        //                 });
        //         res.Add(type.Name, temp);
        //     }
        //
        //     return res;
        // }
    }
}