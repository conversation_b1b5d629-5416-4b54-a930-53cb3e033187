using System;
using OfficeOpenXml;
using OfficeOpenXml.Style;

namespace CYSF.Core.Helpers
{
    public static class ExcelHelper
    {
        public static void IsDecimal(this ExcelRange excelRange, int row, int[] cols)
        {
            foreach (var col in cols) excelRange[row, col].Style.Numberformat.Format = "#,##0.00";
        }

        public static void IsNumber(this ExcelRange excelRange, int row, int[] cols)
        {
            foreach (var col in cols) excelRange[row, col].Style.Numberformat.Format = "#,##0";
        }

        public static void IsDate(this ExcelRange excelRange, int row, int[] cols)
        {
            foreach (var col in cols) excelRange[row, col].Style.Numberformat.Format = "yyyy-mm-dd";
        }

        public static void IsDateTime(this ExcelRange excelRange, int row, int[] cols)
        {
            foreach (var col in cols) excelRange[row, col].Style.Numberformat.Format = "yyyy-mm-dd HH:mm:ss";
        }

        public static void IsText(this ExcelRange excelRange, int row, int[] cols)
        {
            foreach (var col in cols) excelRange[row, col].Style.Numberformat.Format = "@";
        }

        public static void ColWidth(this ExcelWorksheet worksheet, int[] cols, int width = 12)
        {
            foreach (var col in cols) worksheet.Column(col).Width = width;
        }

        public static void ColCenter(this ExcelWorksheet worksheet, int[] cols)
        {
            foreach (var col in cols) worksheet.Column(col).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
        }
        public static void ColLeft(this ExcelWorksheet worksheet, int[] cols)
        {
            foreach (var col in cols) worksheet.Column(col).Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
        }

        public static void ColRight(this ExcelWorksheet worksheet, int[] cols)
        {
            foreach (var col in cols) worksheet.Column(col).Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
        }

        public static void AutoFitColWidth(this ExcelWorksheet worksheet, int[] cols)
        {
            foreach (var col in cols) worksheet.Column(col).AutoFit();
        }
        
        public static void MergeAndFormat(this ExcelRange excelRange, int startRow, int startCol, int endRow, int endCol)
        {
            // 合并单元格
            excelRange.Worksheet.Cells[startRow, startCol, endRow, endCol].Merge = true;
            
            // 设置合并后单元格的格式
            var mergedRange = excelRange.Worksheet.Cells[startRow, startCol, endRow, endCol];
            mergedRange.Style.Numberformat.Format = "@";
    }

        public static void SetLink(this ExcelRange excelRange, string link)
        {
            excelRange.Hyperlink = new Uri(link);
            excelRange.Style.Font.Color.SetColor(1, 24, 144, 255);
        }

        public static void SetLinkCell(this ExcelRange excelRange, ExcelWorksheet excelWorksheet, string cell)
        {
            excelRange.Hyperlink = new Uri($"#'{excelWorksheet.Name}'!{cell}", UriKind.Relative);
            excelRange.Style.Font.Color.SetColor(1, 24, 144, 255);
        }

        public static void SetHeaderStyle(this ExcelWorksheet worksheet, int headerRows = 1)
        {
            for (var i = 1; i <= headerRows; i++)
            {
                worksheet.Row(i).Height = 25; //首行行高
                worksheet.Row(i).Style.Font.Bold = true; //加粗
                worksheet.Row(i).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center; //水平居中
                worksheet.Row(i).Style.VerticalAlignment = ExcelVerticalAlignment.Center; //垂直居中
                worksheet.Row(i).Style.Fill.PatternType = ExcelFillStyle.Solid; //背景颜色
                worksheet.Row(i).Style.Fill.BackgroundColor.SetColor(1, 217, 217, 217);
                worksheet.Row(i).Style.Border.Bottom.Style = ExcelBorderStyle.Thin; //下边框
            }
        }

        public static void SetVals(this ExcelWorksheet worksheet, object[] cols, int row = 1)
        {
            for (var i = 0; i < cols.Length; i++) 
                worksheet.Cells[row, i + 1].Value = cols[i];
        }
    }
}