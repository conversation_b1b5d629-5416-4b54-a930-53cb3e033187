using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;

namespace CYSF.Core.Helpers
{
    public class FileHelper
    {
        public static void CreateFile(string filePath, string text, Encoding encoding)
        {
            if (IsExistFile(filePath)) DeleteFile(filePath);
            if (!IsExistFile(filePath))
            {
                var directoryPath = GetDirectoryFromFilePath(filePath);
                CreateDirectory(directoryPath);

                //Create File
                var file = new FileInfo(filePath);
                using (var stream = file.Create())
                {
                    using (var writer = new StreamWriter(stream, encoding))
                    {
                        writer.Write(text);
                        writer.Flush();
                    }
                }
            }
        }

        public static bool IsExistDirectory(string directoryPath)
        {
            return Directory.Exists(directoryPath);
        }

        public static void CreateDirectory(string directoryPath)
        {
            if (!IsExistDirectory(directoryPath)) Directory.CreateDirectory(directoryPath);
        }

        public static void DeleteDirectory(string directoryPath)
        {
            if (IsExistDirectory(directoryPath)) Directory.Delete(directoryPath, true);
        }

        public static void DeleteFile(string filePath)
        {
            if (IsExistFile(filePath))
                File.Delete(filePath);
        }

        public static string GetDirectoryFromFilePath(string filePath)
        {
            var fileInfo = new FileInfo(filePath);
            var directoryInfo = fileInfo.Directory;
            return directoryInfo != null ? directoryInfo.FullName : string.Empty;
        }

        public static bool IsExistFile(string filePath)
        {
            return File.Exists(filePath);
        }

        /// <summary>
        ///     获取后缀
        ///     获取最后一个.后的文本
        /// </summary>
        /// <param name="filePath">路径或url</param>
        /// <returns></returns>
        public static string GetSuffix(string filePath)
        {
            if (!filePath.Contains("."))
                return string.Empty;
            var arr = filePath.Split('.');
            return arr.Length > 0 ? arr[arr.Length - 1] : string.Empty;
        }

        /// <summary>
        ///     获取文件名
        ///     获取最后一个/后的文本
        /// </summary>
        /// <param name="filePath">路径或url</param>
        /// <returns></returns>
        public static string GetFileName(string filePath)
        {
            if (string.IsNullOrEmpty(filePath)) return string.Empty;
            if (!filePath.Contains("/")) return filePath;

            var arr = filePath.Split('/');
            return arr.Length > 0 ? arr[arr.Length - 1] : string.Empty;
        }

        /// <summary>
        /// 往指定文件写入内容
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="content">要写入的内容</param>
        /// <param name="append">是否追加内容，如果为 false，则覆盖文件</param>
        public static async Task WriteToFileAsync(string filePath, string content, bool append = false)
        {
            try
            {
                // 确保目录存在
                var directory = Path.GetDirectoryName(filePath);
                if (!Directory.Exists(directory))
                {
                    if (directory != null) 
                        Directory.CreateDirectory(directory);
                }

                await using var streamWriter = new StreamWriter(filePath, append, Encoding.UTF8);
                await streamWriter.WriteLineAsync(content);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"写入文件时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 从指定文件读取内容
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>读取到的内容</returns>
        public static async Task<string> ReadFromFileAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException("指定的文件不存在", filePath);
                }

                using var streamReader = new StreamReader(filePath, Encoding.UTF8);
                return await streamReader.ReadToEndAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取文件时发生错误: {ex.Message}");
                return string.Empty; // 或者处理为其他值
            }
        }
    }
}