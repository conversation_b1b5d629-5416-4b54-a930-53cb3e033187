using System;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using CYSF.Core.Configuration;

namespace CYSF.Core.Helpers
{
    /// <summary>
    /// JSON序列化帮助类
    /// </summary>
    public class JsonHelper
    {
        /// <summary>
        /// 实体转换为JSON
        /// </summary>
        /// <typeparam name="T">实体</typeparam>
        /// <param name="data">实体模型</param>
        /// <returns>JSON字符串</returns>
        public static string Serialize<T>(T data)
        {
            return JsonConvert.SerializeObject(data, JsonConfiguration.GetDefaultSettings());
        }

        /// <summary>
        /// 实体转换为JSON（驼峰命名）
        /// </summary>
        /// <typeparam name="T">实体</typeparam>
        /// <param name="data">实体模型</param>
        /// <returns>JSON字符串</returns>
        public static string SerializeCamelCasePropertyNames<T>(T data)
        {
            return JsonConvert.SerializeObject(data, JsonConfiguration.GetCamelCaseSettings());
        }

        /// <summary>
        ///     对象转换为JSON
        /// </summary>
        /// <param name="source">对象</param>
        /// <returns></returns>
        public static string Serialize(object source)
        {
            return JsonConvert.SerializeObject(source);
        }

        /// <summary>
        ///     JSON序列化为实体
        /// </summary>
        /// <typeparam name="T">实体</typeparam>
        /// <param name="source">json</param>
        /// <returns></returns>
        public static T Deserialize<T>(string source)
        {
            return JsonConvert.DeserializeObject<T>(source);
        }

        /// <summary>
        ///     JSON序列化为实体
        /// </summary>
        /// <typeparam name="T">实体</typeparam>
        /// <param name="source">json</param>
        /// <returns></returns>
        public static T Deserialize<T>(JObject source)
        {
            return source.ToObject<T>();
        }

        /// <summary>
        ///     JSON序列化为对象
        /// </summary>
        /// <param name="source"></param>
        /// <returns></returns>
        public static object Deserialize(string source)
        {
            return JsonConvert.DeserializeObject(source);
        }

        /// <summary>
        ///     JSON序列化为指定类型
        /// </summary>
        /// <param name="source"></param>
        /// <param name="destinationType"></param>
        /// <returns></returns>
        public static object Deserialize(string source, Type destinationType)
        {
            return JsonConvert.DeserializeObject(source, destinationType);
        }
    }
}