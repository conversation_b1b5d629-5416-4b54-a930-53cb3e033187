using System;
using System.Collections.Generic;
using System.DrawingCore;
using System.DrawingCore.Drawing2D;
using System.DrawingCore.Imaging;
using System.IO;
using CYSF.Core.Extensions;
using CYSF.Core.Providers;
using Microsoft.AspNetCore.Http;

namespace CYSF.Core.Helpers
{
    public static class ImageHelper
    {
        public static byte[] GetBytes(string verifyCode, int width, int height)
        {
            var font = new Font("Arial", 14, FontStyle.Bold | FontStyle.Italic);
            Brush brush;
            var bitmap = new Bitmap(width, height);
            var g = Graphics.FromImage(bitmap);
            var totalSizeF = g.MeasureString(verifyCode, font);
            SizeF curCharSizeF;
            var startPointF = new PointF(6, (height - totalSizeF.Height) / 2);
            var random = new Random(); //随机数产生器
            g.Clear(Color.White); //清空图片背景色  
            for (var i = 0; i < verifyCode.Length; i++)
            {
                brush = new LinearGradientBrush(new Point(0, 0), new Point(1, 1), Color.FromArgb(random.Next(255), random.Next(255), random.Next(255)),
                    Color.FromArgb(random.Next(255), random.Next(255), random.Next(255)));
                g.DrawString(verifyCode[i].ToString(), font, brush, startPointF);
                curCharSizeF = g.MeasureString(verifyCode[i].ToString(), font);
                startPointF.X += curCharSizeF.Width;
            }

            //画图片的干扰线  
            for (var i = 0; i < 5; i++)
            {
                var x1 = random.Next(bitmap.Width);
                var x2 = random.Next(bitmap.Width);
                var y1 = random.Next(bitmap.Height);
                var y2 = random.Next(bitmap.Height);
                g.DrawLine(new Pen(Color.Silver), x1, y1, x2, y2);
            }

            //画图片的前景干扰点  
            for (var i = 0; i < 50; i++)
            {
                var x = random.Next(bitmap.Width);
                var y = random.Next(bitmap.Height);
                bitmap.SetPixel(x, y, Color.FromArgb(random.Next()));
            }

            g.DrawRectangle(new Pen(Color.Silver), 0, 0, bitmap.Width - 1, bitmap.Height - 1); //画图片的边框线  
            g.Dispose();

            //保存图片数据  
            var stream = new MemoryStream();
            bitmap.Save(stream, ImageFormat.Jpeg);
            //输出图片流  
            return stream.ToArray();
        }

        public static string GetBase64Img(string verifyCode, int width, int height)
        {
            return Convert.ToBase64String(GetBytes(verifyCode, width, height));
        }
    }
}