using System;
using System.Collections.Generic;
using System.Linq;

namespace CYSF.Core.Models
{
    /// <summary>
    /// 验证结果模型
    /// </summary>
    public class ValidationResult
    {
        /// <summary>
        /// 是否验证成功
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 主要错误信息（第一个错误）
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 所有验证错误
        /// </summary>
        public Dictionary<string, string[]> Errors { get; set; } = new();

        /// <summary>
        /// 错误总数
        /// </summary>
        public int ErrorCount => Errors.Sum(x => x.Value.Length);

        /// <summary>
        /// 验证时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 创建成功的验证结果
        /// </summary>
        /// <returns></returns>
        public static ValidationResult Success()
        {
            return new ValidationResult
            {
                IsValid = true,
                Message = "验证成功"
            };
        }

        /// <summary>
        /// 创建失败的验证结果
        /// </summary>
        /// <param name="message">错误信息</param>
        /// <returns></returns>
        public static ValidationResult Failure(string message)
        {
            return new ValidationResult
            {
                IsValid = false,
                Message = message
            };
        }

        /// <summary>
        /// 创建失败的验证结果
        /// </summary>
        /// <param name="errors">错误字典</param>
        /// <returns></returns>
        public static ValidationResult Failure(Dictionary<string, string[]> errors)
        {
            var firstError = errors.FirstOrDefault();
            var message = firstError.Value?.FirstOrDefault() ?? "验证失败";

            return new ValidationResult
            {
                IsValid = false,
                Message = message,
                Errors = errors
            };
        }
    }
}
