using System.Collections.Generic;

namespace CYSF.Core.Models
{
    /// <summary>
    /// 应用程序配置选项
    /// </summary>
    public class AppSettings
    {
        /// <summary>
        /// Redis配置
        /// </summary>
        public RedisOptions Redis { get; set; } = new();

        /// <summary>
        /// Hangfire配置
        /// </summary>
        public HangfireOptions Hangfire { get; set; } = new();

        /// <summary>
        /// JWT配置
        /// </summary>
        public JwtOptions Jwt { get; set; } = new();

        /// <summary>
        /// Swagger配置
        /// </summary>
        public SwaggerOptions Swagger { get; set; } = new();

        /// <summary>
        /// CORS配置
        /// </summary>
        public CorsOptions Cors { get; set; } = new();
    }

    /// <summary>
    /// Redis配置选项
    /// </summary>
    public class RedisOptions
    {
        /// <summary>
        /// Redis连接字符串
        /// </summary>
        public string ConnectionString { get; set; } = string.Empty;
    }

    /// <summary>
    /// Hangfire配置选项
    /// </summary>
    public class HangfireOptions
    {
        /// <summary>
        /// 仪表板路径
        /// </summary>
        public string Dashboard { get; set; } = "/hangfire";

        /// <summary>
        /// SQL Server配置
        /// </summary>
        public HangfireSqlServerOptions SqlServer { get; set; } = new();
    }

    /// <summary>
    /// Hangfire SQL Server配置选项
    /// </summary>
    public class HangfireSqlServerOptions
    {
        /// <summary>
        /// 连接字符串
        /// </summary>
        public string ConnectionString { get; set; } = string.Empty;
    }

    /// <summary>
    /// JWT配置选项
    /// </summary>
    public class JwtOptions
    {
        /// <summary>
        /// 密钥
        /// </summary>
        public string Secret { get; set; } = string.Empty;

        /// <summary>
        /// 发行者
        /// </summary>
        public string Issuer { get; set; } = string.Empty;

        /// <summary>
        /// 受众
        /// </summary>
        public string Audience { get; set; } = string.Empty;

        /// <summary>
        /// Token过期时间（秒）
        /// </summary>
        public int TokenExpires { get; set; } = 14400;

        /// <summary>
        /// 刷新Token过期时间（秒）
        /// </summary>
        public int RefreshTokenExpires { get; set; } = 18000;
    }

    /// <summary>
    /// Swagger配置选项
    /// </summary>
    public class SwaggerOptions
    {
        /// <summary>
        /// 标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; } = string.Empty;
    }
    /// <summary>
    /// CORS配置选项
    /// </summary>
    public class CorsOptions
    {
        /// <summary>
        /// 允许的主机
        /// </summary>
        public string AllowHosts { get; set; } = string.Empty;
    }
}
