using System;

namespace CYSF.Core.Exceptions
{
    /// <summary>
    /// 模型验证异常类
    /// </summary>
    public class ModelInValidException : Exception
    {
        /// <summary>
        /// 初始化ModelInValidException类的新实例
        /// </summary>
        public ModelInValidException()
        {
        }

        /// <summary>
        /// 使用指定的错误消息初始化ModelInValidException类的新实例
        /// </summary>
        /// <param name="message">描述错误的消息</param>
        public ModelInValidException(string message) : base(message)
        {
        }

        /// <summary>
        /// 使用指定的错误消息和对作为此异常原因的内部异常的引用来初始化ModelInValidException类的新实例
        /// </summary>
        /// <param name="message">解释异常原因的错误消息</param>
        /// <param name="inner">导致当前异常的异常；如果未指定内部异常，则是一个 null 引用</param>
        public ModelInValidException(string message, Exception inner) : base(message, inner)
        {
        }
    }
}