using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Options;

namespace CYSF.Core.Cache;

/// <summary>
/// 通用缓存助手类，提供常用的缓存操作
/// </summary>
public class CacheHelper
{
    private readonly ICacheService _cacheService;
    private readonly CacheOptions _options;

    public CacheHelper(ICacheService cacheService, IOptions<CacheOptions> options)
    {
        _cacheService = cacheService;
        _options = options.Value;
    }

    /// <summary>
    /// 构建缓存键（添加前缀）
    /// </summary>
    /// <param name="key">原始键</param>
    /// <returns>带前缀的缓存键</returns>
    public string BuildKey(string key)
    {
        return $"{_options.KeyPrefix}{key}";
    }

    /// <summary>
    /// 缓存壳模式 - 类似于 RedisHelper.CacheShellAsync
    /// </summary>
    /// <typeparam name="T">缓存值类型</typeparam>
    /// <param name="key">缓存键</param>
    /// <param name="expiry">过期时间（秒）</param>
    /// <param name="factory">值工厂方法</param>
    /// <returns>缓存值</returns>
    public async Task<T> CacheShellAsync<T>(string key, int expiry, Func<Task<T>> factory)
    {
        var cacheKey = BuildKey(key);
        return await _cacheService.GetOrSetAsync(cacheKey, factory, TimeSpan.FromSeconds(expiry));
    }

    /// <summary>
    /// 缓存壳模式 - 使用 TimeSpan 过期时间
    /// </summary>
    /// <typeparam name="T">缓存值类型</typeparam>
    /// <param name="key">缓存键</param>
    /// <param name="expiry">过期时间</param>
    /// <param name="factory">值工厂方法</param>
    /// <returns>缓存值</returns>
    public async Task<T> CacheShellAsync<T>(string key, TimeSpan expiry, Func<Task<T>> factory)
    {
        var cacheKey = BuildKey(key);
        return await _cacheService.GetOrSetAsync(cacheKey, factory, expiry);
    }

    /// <summary>
    /// 获取或设置缓存（使用默认过期时间）
    /// </summary>
    /// <typeparam name="T">缓存值类型</typeparam>
    /// <param name="key">缓存键</param>
    /// <param name="factory">值工厂方法</param>
    /// <returns>缓存值</returns>
    public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory)
    {
        var cacheKey = BuildKey(key);
        var expiry = TimeSpan.FromMinutes(_options.DefaultExpirationMinutes);
        return await _cacheService.GetOrSetAsync(cacheKey, factory, expiry);
    }

    /// <summary>
    /// 获取或设置缓存（指定过期时间）
    /// </summary>
    /// <typeparam name="T">缓存值类型</typeparam>
    /// <param name="key">缓存键</param>
    /// <param name="factory">值工厂方法</param>
    /// <param name="expiry">过期时间</param>
    /// <returns>缓存值</returns>
    public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan expiry)
    {
        var cacheKey = BuildKey(key);
        return await _cacheService.GetOrSetAsync(cacheKey, factory, expiry);
    }

    /// <summary>
    /// 设置缓存（使用默认过期时间）
    /// </summary>
    /// <typeparam name="T">缓存值类型</typeparam>
    /// <param name="key">缓存键</param>
    /// <param name="value">缓存值</param>
    /// <returns></returns>
    public async Task SetAsync<T>(string key, T value)
    {
        var cacheKey = BuildKey(key);
        var expiry = TimeSpan.FromMinutes(_options.DefaultExpirationMinutes);
        await _cacheService.SetAsync(cacheKey, value, expiry);
    }

    /// <summary>
    /// 设置缓存（指定过期时间）
    /// </summary>
    /// <typeparam name="T">缓存值类型</typeparam>
    /// <param name="key">缓存键</param>
    /// <param name="value">缓存值</param>
    /// <param name="expiry">过期时间</param>
    /// <returns></returns>
    public async Task SetAsync<T>(string key, T value, TimeSpan expiry)
    {
        var cacheKey = BuildKey(key);
        await _cacheService.SetAsync(cacheKey, value, expiry);
    }

    /// <summary>
    /// 获取缓存
    /// </summary>
    /// <typeparam name="T">缓存值类型</typeparam>
    /// <param name="key">缓存键</param>
    /// <returns>缓存值</returns>
    public async Task<T> GetAsync<T>(string key)
    {
        var cacheKey = BuildKey(key);
        return await _cacheService.GetAsync<T>(cacheKey);
    }

    /// <summary>
    /// 删除缓存
    /// </summary>
    /// <param name="key">缓存键</param>
    /// <returns>是否删除成功</returns>
    public async Task<bool> RemoveAsync(string key)
    {
        var cacheKey = BuildKey(key);
        return await _cacheService.RemoveAsync(cacheKey);
    }

    /// <summary>
    /// 按模式删除缓存
    /// </summary>
    /// <param name="pattern">匹配模式</param>
    /// <returns>删除的数量</returns>
    public async Task<long> RemoveByPatternAsync(string pattern)
    {
        var cachePattern = BuildKey(pattern);
        return await _cacheService.RemoveByPatternAsync(cachePattern);
    }

    /// <summary>
    /// 检查缓存是否存在
    /// </summary>
    /// <param name="key">缓存键</param>
    /// <returns>是否存在</returns>
    public async Task<bool> ExistsAsync(string key)
    {
        var cacheKey = BuildKey(key);
        return await _cacheService.ExistsAsync(cacheKey);
    }
}
