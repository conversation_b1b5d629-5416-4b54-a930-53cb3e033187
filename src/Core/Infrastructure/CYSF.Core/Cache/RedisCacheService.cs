using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CSRedis;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace CYSF.Core.Cache;

/// <summary>
/// Redis 缓存服务实现
/// </summary>
public class RedisCacheService : ICacheService
{
    private readonly ILogger<RedisCacheService> _logger;

    public RedisCacheService(ILogger<RedisCacheService> logger)
    {
        _logger = logger;
    }

    public async Task<T> GetAsync<T>(string key)
    {
        try
        {
            var value = await RedisHelper.GetAsync(key);
            if (string.IsNullOrEmpty(value))
                return default(T);

            if (typeof(T) == typeof(string))
                return (T)(object)value;

            return JsonConvert.DeserializeObject<T>(value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis GetAsync error for key: {Key}", key);
            return default(T);
        }
    }

    public async Task<string> GetStringAsync(string key)
    {
        try
        {
            return await RedisHelper.GetAsync(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis GetStringAsync error for key: {Key}", key);
            return null;
        }
    }

    public async Task SetAsync<T>(string key, T value, TimeSpan? expiry = null)
    {
        try
        {
            string serializedValue;
            if (value is string stringValue)
            {
                serializedValue = stringValue;
            }
            else
            {
                serializedValue = JsonConvert.SerializeObject(value);
            }

            if (expiry.HasValue)
            {
                await RedisHelper.SetAsync(key, serializedValue, expiry.Value);
            }
            else
            {
                await RedisHelper.SetAsync(key, serializedValue);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis SetAsync error for key: {Key}", key);
            throw;
        }
    }

    public async Task SetStringAsync(string key, string value, TimeSpan? expiry = null)
    {
        try
        {
            if (expiry.HasValue)
            {
                await RedisHelper.SetAsync(key, value, expiry.Value);
            }
            else
            {
                await RedisHelper.SetAsync(key, value);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis SetStringAsync error for key: {Key}", key);
            throw;
        }
    }

    public async Task<bool> RemoveAsync(string key)
    {
        try
        {
            return await RedisHelper.DelAsync(key) > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis RemoveAsync error for key: {Key}", key);
            return false;
        }
    }

    public async Task<long> RemoveAsync(IEnumerable<string> keys)
    {
        try
        {
            return await RedisHelper.DelAsync(keys.ToArray());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis RemoveAsync error for keys: {Keys}", string.Join(",", keys));
            return 0;
        }
    }

    public async Task<bool> ExistsAsync(string key)
    {
        try
        {
            return await RedisHelper.ExistsAsync(key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis ExistsAsync error for key: {Key}", key);
            return false;
        }
    }

    public async Task<bool> ExpireAsync(string key, TimeSpan expiry)
    {
        try
        {
            return await RedisHelper.ExpireAsync(key, expiry);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis ExpireAsync error for key: {Key}", key);
            return false;
        }
    }

    public async Task<TimeSpan?> GetTtlAsync(string key)
    {
        try
        {
            var ttl = await RedisHelper.TtlAsync(key);
            return ttl < 0 ? null : TimeSpan.FromSeconds(ttl);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis GetTtlAsync error for key: {Key}", key);
            return null;
        }
    }

    public async Task<long> RemoveByPatternAsync(string pattern)
    {
        try
        {
            var keys = await RedisHelper.KeysAsync(pattern);
            if (keys?.Length > 0)
            {
                return await RedisHelper.DelAsync(keys);
            }
            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis RemoveByPatternAsync error for pattern: {Pattern}", pattern);
            return 0;
        }
    }

    public async Task<IEnumerable<string>> GetKeysAsync(string pattern)
    {
        try
        {
            var keys = await RedisHelper.KeysAsync(pattern);
            return keys ?? new string[0];
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis GetKeysAsync error for pattern: {Pattern}", pattern);
            return new string[0];
        }
    }

    public async Task<long> IncrementAsync(string key, long value = 1)
    {
        try
        {
            return await RedisHelper.IncrByAsync(key, value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis IncrementAsync error for key: {Key}", key);
            throw;
        }
    }
    public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiry = null)
    {
        try
        {
            var value = await GetAsync<T>(key);
            if (value != null && !value.Equals(default(T)))
            {
                return value;
            }

            var newValue = await factory();
            await SetAsync(key, newValue, expiry);
            return newValue;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Redis GetOrSetAsync error for key: {Key}", key);
            throw;
        }
    }
}
