using System;
using CSRedis;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Redis;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace CYSF.Core.Cache;

/// <summary>
/// 缓存服务扩展
/// </summary>
public static class CacheServiceExtensions
{
    /// <summary>
    /// 添加缓存服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns></returns>
    public static IServiceCollection AddCacheService(this IServiceCollection services, IConfiguration configuration)
    {
        // 绑定缓存配置
        var cacheOptions = new CacheOptions();
        configuration.GetSection("Cache").Bind(cacheOptions);
        services.Configure<CacheOptions>(configuration.GetSection("Cache"));

        // 始终注册 IMemoryCache，因为 CommonApp 等组件需要使用它
        // 无论是否使用 Redis 作为主缓存，IMemoryCache 都应该可用
        services.AddMemoryCache(memoryCacheOptions =>
        {
            memoryCacheOptions.SizeLimit = cacheOptions.MemoryCache.SizeLimit * 1024 * 1024; // 转换为字节
            memoryCacheOptions.CompactionPercentage = cacheOptions.MemoryCache.CompactionPercentage;
            memoryCacheOptions.ExpirationScanFrequency = TimeSpan.FromSeconds(cacheOptions.MemoryCache.ExpirationScanFrequency);
        });

        // 缓存是必需的，因为 Token 需要持久化
        // 如果配置中禁用了缓存，强制使用 MemoryCache 作为后备
        if (!cacheOptions.Enabled)
        {
            cacheOptions.Provider = CacheProvider.Memory;
            cacheOptions.Enabled = true;
        }

        switch (cacheOptions.Provider)
        {
            case CacheProvider.Redis:
                AddRedisCache(services, cacheOptions);
                break;
            case CacheProvider.Memory:
                AddMemoryCacheService(services, cacheOptions);
                break;
            default:
                // 默认使用 MemoryCache 作为后备
                AddMemoryCacheService(services, cacheOptions);
                break;
        }

        // 注册 CacheHelper
        services.AddScoped<CacheHelper>();

        return services;
    }

    /// <summary>
    /// 添加 Redis 缓存
    /// </summary>
    private static void AddRedisCache(IServiceCollection services, CacheOptions options)
    {
        // 配置 Redis
        var connectionStrings = options.RedisConnectionString?.Split(';');
        var redisClient = connectionStrings is { Length: 1 }
            ? new CSRedisClient(connectionStrings[0])
            : new CSRedisClient(null, connectionStrings);
        
        RedisHelper.Initialization(redisClient);

        // 注册分布式缓存
        services.AddSingleton<IDistributedCache>(new CSRedisCache(RedisHelper.Instance));
        
        // 注册缓存服务
        services.AddSingleton<ICacheService, RedisCacheService>();
    }

    /// <summary>
    /// 添加内存缓存服务（ICacheService 实现）
    /// </summary>
    private static void AddMemoryCacheService(IServiceCollection services, CacheOptions options)
    {
        // 注册缓存服务（IMemoryCache 已在主方法中注册）
        services.AddSingleton<ICacheService, MemoryCacheService>();
    }
}


