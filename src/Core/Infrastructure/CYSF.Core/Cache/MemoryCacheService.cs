using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace CYSF.Core.Cache;

/// <summary>
/// 内存缓存服务实现
/// </summary>
public class MemoryCacheService : ICacheService
{
    private readonly IMemoryCache _memoryCache;
    private readonly ILogger<MemoryCacheService> _logger;
    private readonly ConcurrentDictionary<string, DateTime> _keyExpirations;
    private readonly ConcurrentDictionary<string, long> _counters;

    public MemoryCacheService(IMemoryCache memoryCache, ILogger<MemoryCacheService> logger)
    {
        _memoryCache = memoryCache;
        _logger = logger;
        _keyExpirations = new ConcurrentDictionary<string, DateTime>();
        _counters = new ConcurrentDictionary<string, long>();
    }

    public Task<T> GetAsync<T>(string key)
    {
        try
        {
            if (_memoryCache.TryGetValue(key, out var value))
            {
                if (value is T directValue)
                    return Task.FromResult(directValue);

                if (value is string stringValue && typeof(T) != typeof(string))
                {
                    var deserializedValue = JsonConvert.DeserializeObject<T>(stringValue);
                    return Task.FromResult(deserializedValue);
                }

                if (typeof(T) == typeof(string))
                    return Task.FromResult((T)(object)value.ToString());
            }

            return Task.FromResult(default(T));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MemoryCache GetAsync error for key: {Key}", key);
            return Task.FromResult(default(T));
        }
    }

    public Task<string> GetStringAsync(string key)
    {
        try
        {
            if (_memoryCache.TryGetValue(key, out var value))
            {
                return Task.FromResult(value?.ToString());
            }
            return Task.FromResult<string>(null);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MemoryCache GetStringAsync error for key: {Key}", key);
            return Task.FromResult<string>(null);
        }
    }

    public Task SetAsync<T>(string key, T value, TimeSpan? expiry = null)
    {
        try
        {
            var options = new MemoryCacheEntryOptions();

            // 设置缓存项大小（当配置了SizeLimit时必需）
            options.Size = 1;

            if (expiry.HasValue)
            {
                options.AbsoluteExpirationRelativeToNow = expiry.Value;
                _keyExpirations[key] = DateTime.UtcNow.Add(expiry.Value);
            }
            else
            {
                _keyExpirations.TryRemove(key, out _);
            }

            // 注册移除回调，清理过期时间记录
            options.RegisterPostEvictionCallback((k, v, reason, state) =>
            {
                _keyExpirations.TryRemove(k.ToString(), out _);
            });

            object cacheValue = value;
            if (!(value is string) && value != null)
            {
                cacheValue = JsonConvert.SerializeObject(value);
            }

            _memoryCache.Set(key, cacheValue, options);
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MemoryCache SetAsync error for key: {Key}", key);
            throw;
        }
    }

    public Task SetStringAsync(string key, string value, TimeSpan? expiry = null)
    {
        return SetAsync(key, value, expiry);
    }

    public Task<bool> RemoveAsync(string key)
    {
        try
        {
            var exists = _memoryCache.TryGetValue(key, out _);
            _memoryCache.Remove(key);
            _keyExpirations.TryRemove(key, out _);
            _counters.TryRemove(key, out _);
            return Task.FromResult(exists);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MemoryCache RemoveAsync error for key: {Key}", key);
            return Task.FromResult(false);
        }
    }

    public async Task<long> RemoveAsync(IEnumerable<string> keys)
    {
        try
        {
            long removedCount = 0;
            foreach (var key in keys)
            {
                var removed = await RemoveAsync(key);
                if (removed)
                {
                    removedCount++;
                }
            }
            return removedCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MemoryCache RemoveAsync error for keys: {Keys}", string.Join(",", keys));
            return 0L;
        }
    }

    public Task<bool> ExistsAsync(string key)
    {
        try
        {
            var exists = _memoryCache.TryGetValue(key, out _);
            return Task.FromResult(exists);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MemoryCache ExistsAsync error for key: {Key}", key);
            return Task.FromResult(false);
        }
    }

    public Task<bool> ExpireAsync(string key, TimeSpan expiry)
    {
        try
        {
            if (_memoryCache.TryGetValue(key, out var value))
            {
                // 重新设置缓存项以更新过期时间
                var options = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = expiry,
                    Size = 1  // 设置缓存项大小（当配置了SizeLimit时必需）
                };

                _memoryCache.Set(key, value, options);
                _keyExpirations[key] = DateTime.UtcNow.Add(expiry);
                return Task.FromResult(true);
            }
            return Task.FromResult(false);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MemoryCache ExpireAsync error for key: {Key}", key);
            return Task.FromResult(false);
        }
    }

    public Task<TimeSpan?> GetTtlAsync(string key)
    {
        try
        {
            if (_keyExpirations.TryGetValue(key, out var expiration))
            {
                var remaining = expiration - DateTime.UtcNow;
                return Task.FromResult<TimeSpan?>(remaining > TimeSpan.Zero ? remaining : TimeSpan.Zero);
            }
            
            // 如果键存在但没有过期时间记录，说明是永久缓存
            if (_memoryCache.TryGetValue(key, out _))
            {
                return Task.FromResult<TimeSpan?>(null);
            }
            
            // 键不存在
            return Task.FromResult<TimeSpan?>(TimeSpan.FromSeconds(-1));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MemoryCache GetTtlAsync error for key: {Key}", key);
            return Task.FromResult<TimeSpan?>(null);
        }
    }

    public async Task<long> RemoveByPatternAsync(string pattern)
    {
        try
        {
            // MemoryCache 没有直接的模式匹配功能，这里使用简单的通配符匹配
            var keysToRemove = new List<string>();

            // 从过期时间字典中获取所有键进行匹配
            foreach (var key in _keyExpirations.Keys)
            {
                if (IsMatch(key, pattern))
                {
                    keysToRemove.Add(key);
                }
            }

            long removedCount = 0;
            foreach (var key in keysToRemove)
            {
                var removed = await RemoveAsync(key);
                if (removed)
                {
                    removedCount++;
                }
            }

            return removedCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MemoryCache RemoveByPatternAsync error for pattern: {Pattern}", pattern);
            return 0L;
        }
    }

    public Task<IEnumerable<string>> GetKeysAsync(string pattern)
    {
        try
        {
            var matchingKeys = _keyExpirations.Keys
                .Where(key => IsMatch(key, pattern))
                .ToList();
                
            return Task.FromResult<IEnumerable<string>>(matchingKeys);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MemoryCache GetKeysAsync error for pattern: {Pattern}", pattern);
            return Task.FromResult<IEnumerable<string>>(new string[0]);
        }
    }

    public Task<long> IncrementAsync(string key, long value = 1)
    {
        try
        {
            var newValue = _counters.AddOrUpdate(key, value, (k, v) => v + value);
            
            // 同时更新内存缓存
            _memoryCache.Set(key, newValue);
            
            return Task.FromResult(newValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MemoryCache IncrementAsync error for key: {Key}", key);
            throw;
        }
    }

    public Task<long> DecrementAsync(string key, long value = 1)
    {
        try
        {
            var newValue = _counters.AddOrUpdate(key, -value, (k, v) => v - value);
            
            // 同时更新内存缓存
            _memoryCache.Set(key, newValue);
            
            return Task.FromResult(newValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MemoryCache DecrementAsync error for key: {Key}", key);
            throw;
        }
    }

    public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiry = null)
    {
        try
        {
            // 先尝试获取缓存
            var value = await GetAsync<T>(key);
            if (value != null && !value.Equals(default(T)))
            {
                return value;
            }

            // 使用 SemaphoreSlim 防止并发执行相同的 factory
            var semaphoreKey = $"semaphore_{key}";
            var semaphore = new SemaphoreSlim(1, 1);

            await semaphore.WaitAsync();
            try
            {
                // 再次检查缓存，防止在等待期间其他线程已经设置了值
                value = await GetAsync<T>(key);
                if (value != null && !value.Equals(default(T)))
                {
                    return value;
                }

                var newValue = await factory();
                await SetAsync(key, newValue, expiry);
                return newValue;
            }
            finally
            {
                semaphore.Release();
                semaphore.Dispose();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "MemoryCache GetOrSetAsync error for key: {Key}", key);
            throw;
        }
    }

    /// <summary>
    /// 简单的通配符匹配
    /// </summary>
    private static bool IsMatch(string input, string pattern)
    {
        if (pattern == "*") return true;
        if (!pattern.Contains("*")) return input == pattern;
        
        // 简单的通配符匹配实现
        var regex = "^" + pattern.Replace("*", ".*") + "$";
        return System.Text.RegularExpressions.Regex.IsMatch(input, regex);
    }
}
