# 🚀 统一缓存服务使用指南

## 📋 概述

CYSF 缓存服务提供了统一的缓存抽象层，支持 Redis 和 MemoryCache 的无缝切换，让业务层无需关心底层缓存实现。

⚠️ **重要提示**：缓存是系统的重要组件，用于提高数据访问性能。系统支持 Redis 和 MemoryCache 两种缓存提供程序，可根据环境需要进行配置。

## 🔧 配置

### 缓存提供者配置

在 `appsettings.json` 中配置缓存：

```json
{
  "Cache": {
    "Provider": "Redis",  // 或 "Memory"
    "RedisConnectionString": "127.0.0.1:6379,password=xxx",
    "DefaultExpirationMinutes": 60,
    "KeyPrefix": "CYSF:Dev:",
    "Enabled": true,
    "MemoryCache": {
      "SizeLimit": 100,
      "CompactionPercentage": 0.25,
      "ExpirationScanFrequency": 60
    }
  }
}
```

### 环境配置建议

- **开发环境**: 使用 Redis（便于调试和数据查看）
- **测试环境**: 使用 MemoryCache（简化部署，提高测试速度）
- **生产环境**: 使用 Redis（支持分布式，数据持久化）

## 💻 使用方式

### 方式一：直接注入 ICacheService

```csharp
public class UserService
{
    private readonly ICacheService _cacheService;

    public UserService(ICacheService cacheService)
    {
        _cacheService = cacheService;
    }

    public async Task<User> GetUserAsync(long userId)
    {
        var cacheKey = $"user:{userId}";
        
        // 尝试从缓存获取
        var cachedUser = await _cacheService.GetAsync<User>(cacheKey);
        if (cachedUser != null)
        {
            return cachedUser;
        }

        // 从数据库获取
        var user = await _userRepository.GetByIdAsync(userId);
        
        // 设置缓存，30分钟过期
        await _cacheService.SetAsync(cacheKey, user, TimeSpan.FromMinutes(30));
        
        return user;
    }
}
```

### 方式二：使用 CacheHelper（推荐）

```csharp
public class UserService
{
    private readonly CacheHelper _cacheHelper;
    private readonly IUserRepository _userRepository;

    public UserService(CacheHelper cacheHelper, IUserRepository userRepository)
    {
        _cacheHelper = cacheHelper;
        _userRepository = userRepository;
    }

    public async Task<User> GetUserAsync(long userId)
    {
        var cacheKey = _cacheHelper.GetUserCacheKey(userId);
        
        return await _cacheHelper.GetOrSetAsync(cacheKey, async () =>
        {
            return await _userRepository.GetByIdAsync(userId);
        }, TimeSpan.FromMinutes(30));
    }

    public async Task UpdateUserAsync(User user)
    {
        await _userRepository.UpdateAsync(user);
        
        // 清除用户缓存
        await _cacheHelper.RemoveUserCacheAsync(user.Id);
    }
}
```

### 方式三：GetOrSet 模式

```csharp
public class ConfigService
{
    private readonly CacheHelper _cacheHelper;
    private readonly IConfigRepository _configRepository;

    public ConfigService(CacheHelper cacheHelper, IConfigRepository configRepository)
    {
        _cacheHelper = cacheHelper;
        _configRepository = configRepository;
    }

    public async Task<string> GetConfigValueAsync(string key)
    {
        return await _cacheHelper.GetOrSetAsync(
            _cacheHelper.GetConfigCacheKey(key),
            async () => await _configRepository.GetValueAsync(key),
            TimeSpan.FromHours(1) // 配置缓存1小时
        );
    }
}
```

## 🎯 常用场景

### 1. 用户信息缓存

```csharp
// 获取用户基本信息
var user = await _cacheHelper.GetOrSetAsync(
    _cacheHelper.GetUserCacheKey(userId, "profile"),
    () => _userRepository.GetProfileAsync(userId),
    TimeSpan.FromMinutes(30)
);

// 获取用户权限
var permissions = await _cacheHelper.GetOrSetAsync(
    _cacheHelper.GetPermissionCacheKey(userId, tenantId),
    () => _permissionService.GetUserPermissionsAsync(userId, tenantId),
    TimeSpan.FromMinutes(15)
);
```

### 2. 计数器操作

```csharp
// 增加访问计数
var count = await _cacheService.IncrementAsync("page:views:home");

// 限流计数
var requestCount = await _cacheService.IncrementAsync($"rate_limit:{userId}:{DateTime.Now:yyyyMMddHH}");
if (requestCount > 100)
{
    throw new RateLimitExceededException();
}
```

### 3. 分布式锁

```csharp
public async Task<bool> TryLockAsync(string lockKey, TimeSpan expiry)
{
    var lockValue = Guid.NewGuid().ToString();
    var success = await _cacheService.SetAsync($"lock:{lockKey}", lockValue, expiry);
    return success;
}
```

### 4. 批量操作

```csharp
// 批量删除缓存
var keysToDelete = new[] { "user:1", "user:2", "user:3" };
await _cacheService.RemoveAsync(keysToDelete);

// 按模式删除
await _cacheService.RemoveByPatternAsync("user:*:profile");
```

## 🔄 缓存策略

### 1. Cache-Aside（旁路缓存）

```csharp
public async Task<T> GetDataAsync<T>(string key, Func<Task<T>> dataLoader)
{
    // 1. 先查缓存
    var cached = await _cacheService.GetAsync<T>(key);
    if (cached != null) return cached;

    // 2. 缓存未命中，查数据库
    var data = await dataLoader();

    // 3. 写入缓存
    await _cacheService.SetAsync(key, data, TimeSpan.FromMinutes(30));

    return data;
}
```

### 2. Write-Through（写穿透）

```csharp
public async Task UpdateDataAsync<T>(string key, T data, Func<Task> dataSaver)
{
    // 1. 更新数据库
    await dataSaver();

    // 2. 更新缓存
    await _cacheService.SetAsync(key, data, TimeSpan.FromMinutes(30));
}
```

### 3. Write-Behind（写回）

```csharp
public async Task UpdateDataAsync<T>(string key, T data)
{
    // 1. 立即更新缓存
    await _cacheService.SetAsync(key, data, TimeSpan.FromMinutes(30));

    // 2. 异步更新数据库（通过后台任务）
    _backgroundTaskQueue.QueueBackgroundWorkItem(async token =>
    {
        await _repository.UpdateAsync(data);
    });
}
```

## ⚠️ 注意事项

### 1. 缓存键命名规范

- 使用有意义的前缀：`user:`, `config:`, `permission:`
- 包含必要的标识符：`user:{userId}:profile`
- 避免特殊字符，使用冒号分隔

### 2. 过期时间设置

- 热点数据：15-30分钟
- 配置数据：1-2小时
- 用户会话：根据业务需求
- 临时数据：5-10分钟

### 3. 缓存更新策略

- 数据更新时及时清除相关缓存
- 使用版本号或时间戳处理缓存一致性
- 考虑使用缓存预热避免缓存雪崩

### 4. 性能优化

- 避免缓存大对象（>1MB）
- 使用批量操作减少网络开销
- 合理设置缓存大小限制

## 🔧 故障排除

### 1. 缓存未生效

检查配置：
```json
{
  "Cache": {
    "Enabled": true,  // 确保启用
    "Provider": "Redis"  // 确保提供者正确
  }
}
```

### 2. Redis 连接问题

检查连接字符串和网络连通性：
```bash
redis-cli -h 127.0.0.1 -p 6379 ping
```

### 3. 内存缓存溢出

调整内存限制：
```json
{
  "Cache": {
    "MemoryCache": {
      "SizeLimit": 200,  // 增加内存限制
      "CompactionPercentage": 0.1  // 降低压缩阈值
    }
  }
}
```

## 📊 监控和调试

### 1. 缓存命中率监控

```csharp
public class CacheMetrics
{
    private long _hits;
    private long _misses;

    public void RecordHit() => Interlocked.Increment(ref _hits);
    public void RecordMiss() => Interlocked.Increment(ref _misses);
    
    public double HitRate => _hits + _misses == 0 ? 0 : (double)_hits / (_hits + _misses);
}
```

### 2. 缓存键查看

```csharp
// 查看所有用户相关的缓存键
var userKeys = await _cacheService.GetKeysAsync("user:*");
foreach (var key in userKeys)
{
    Console.WriteLine($"Key: {key}, TTL: {await _cacheService.GetTtlAsync(key)}");
}
```
