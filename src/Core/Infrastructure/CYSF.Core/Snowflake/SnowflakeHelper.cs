using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace CYSF.Core.Snowflake;

/// <summary>
/// 雪花ID助手类
/// 提供静态方法访问雪花ID生成器
/// </summary>
public static class SnowflakeHelper
{
    private static ISnowflakeIdGenerator _generator;
    private static readonly object _lock = new object();
    private static bool _initialized = false;

    /// <summary>
    /// 初始化雪花ID生成器
    /// </summary>
    /// <param name="serviceProvider">服务提供者</param>
    public static void Initialize(IServiceProvider serviceProvider)
    {
        if (_initialized)
            return;

        lock (_lock)
        {
            if (_initialized)
                return;

            _generator = serviceProvider.GetRequiredService<ISnowflakeIdGenerator>();
            _initialized = true;
        }
    }

    /// <summary>
    /// 生成下一个ID
    /// </summary>
    /// <returns>雪花ID</returns>
    public static long NextId()
    {
        EnsureInitialized();
        return _generator.NextId();
    }

    /// <summary>
    /// 异步生成下一个ID
    /// </summary>
    /// <returns>雪花ID</returns>
    public static Task<long> NextIdAsync()
    {
        EnsureInitialized();
        return _generator.NextIdAsync();
    }

    /// <summary>
    /// 批量生成ID
    /// </summary>
    /// <param name="count">数量</param>
    /// <returns>ID数组</returns>
    public static long[] NextIds(int count)
    {
        EnsureInitialized();
        return _generator.NextIds(count);
    }

    /// <summary>
    /// 生成字符串ID
    /// </summary>
    /// <returns>字符串ID</returns>
    public static string NextStringId()
    {
        return NextId().ToString();
    }

    /// <summary>
    /// 批量生成字符串ID
    /// </summary>
    /// <param name="count">数量</param>
    /// <returns>字符串ID数组</returns>
    public static string[] NextStringIds(int count)
    {
        var ids = NextIds(count);
        return ids.ToStringIds();
    }

    /// <summary>
    /// 解析雪花ID
    /// </summary>
    /// <param name="id">雪花ID</param>
    /// <returns>解析信息</returns>
    public static SnowflakeIdInfo ParseId(long id)
    {
        EnsureInitialized();
        return _generator.ParseId(id);
    }

    /// <summary>
    /// 解析字符串雪花ID
    /// </summary>
    /// <param name="stringId">字符串ID</param>
    /// <returns>解析信息</returns>
    public static SnowflakeIdInfo ParseStringId(string stringId)
    {
        var id = stringId.FromStringId();
        return ParseId(id);
    }

    /// <summary>
    /// 获取生成器信息
    /// </summary>
    /// <returns>生成器信息</returns>
    public static SnowflakeGeneratorInfo GetGeneratorInfo()
    {
        EnsureInitialized();
        return _generator.GetGeneratorInfo();
    }

    /// <summary>
    /// 检查是否为有效的雪花ID
    /// </summary>
    /// <param name="id">ID</param>
    /// <returns>是否有效</returns>
    public static bool IsValidId(long id)
    {
        EnsureInitialized();
        return id.IsValidSnowflakeId(_generator);
    }

    /// <summary>
    /// 检查是否为有效的字符串雪花ID
    /// </summary>
    /// <param name="stringId">字符串ID</param>
    /// <returns>是否有效</returns>
    public static bool IsValidStringId(string stringId)
    {
        try
        {
            var id = stringId.FromStringId();
            return IsValidId(id);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 获取ID的生成时间
    /// </summary>
    /// <param name="id">雪花ID</param>
    /// <returns>生成时间</returns>
    public static DateTime GetGeneratedTime(long id)
    {
        EnsureInitialized();
        return id.GetGeneratedTime(_generator);
    }

    /// <summary>
    /// 获取字符串ID的生成时间
    /// </summary>
    /// <param name="stringId">字符串ID</param>
    /// <returns>生成时间</returns>
    public static DateTime GetGeneratedTime(string stringId)
    {
        var id = stringId.FromStringId();
        return GetGeneratedTime(id);
    }

    /// <summary>
    /// 确保已初始化
    /// </summary>
    private static void EnsureInitialized()
    {
        if (!_initialized)
        {
            throw new InvalidOperationException("SnowflakeHelper has not been initialized. Call Initialize() first.");
        }
    }
}

/// <summary>
/// 雪花ID性能计数器
/// </summary>
public class SnowflakePerformanceCounter
{
    private readonly ConcurrentDictionary<string, long> _counters = new();
    private readonly ILogger<SnowflakePerformanceCounter> _logger;

    public SnowflakePerformanceCounter(ILogger<SnowflakePerformanceCounter> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 增加计数
    /// </summary>
    /// <param name="key">计数键</param>
    /// <param name="increment">增量</param>
    public void Increment(string key, long increment = 1)
    {
        _counters.AddOrUpdate(key, increment, (k, v) => v + increment);
    }

    /// <summary>
    /// 获取计数
    /// </summary>
    /// <param name="key">计数键</param>
    /// <returns>计数值</returns>
    public long GetCount(string key)
    {
        return _counters.GetValueOrDefault(key, 0);
    }

    /// <summary>
    /// 获取所有计数
    /// </summary>
    /// <returns>计数字典</returns>
    public Dictionary<string, long> GetAllCounts()
    {
        return new Dictionary<string, long>(_counters);
    }

    /// <summary>
    /// 重置计数
    /// </summary>
    /// <param name="key">计数键</param>
    public void Reset(string key)
    {
        _counters.TryRemove(key, out _);
    }

    /// <summary>
    /// 重置所有计数
    /// </summary>
    public void ResetAll()
    {
        _counters.Clear();
    }

    /// <summary>
    /// 记录性能指标
    /// </summary>
    public void LogPerformanceMetrics()
    {
        var counts = GetAllCounts();
        foreach (var kvp in counts)
        {
            _logger.LogInformation("Snowflake Performance - {Key}: {Count}", kvp.Key, kvp.Value);
        }
    }
}
