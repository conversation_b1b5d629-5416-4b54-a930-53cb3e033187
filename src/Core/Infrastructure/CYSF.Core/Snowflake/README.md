# 🔢 CYSF 雪花ID生成器

## 📋 概述

CYSF 雪花ID生成器是一个改进版的分布式唯一ID生成器，基于Twitter的Snowflake算法实现，支持配置化、线程安全、时钟回拨检测等企业级特性。

## 🏗️ 架构设计

### ID结构 (64位)

```
0 - 0000000000 0000000000 0000000000 0000000000 0 - 00000 - 0000000000 - 000000000000
|   |                                             |   |       |            |
|   |<-------------- 41位时间戳 ---------------->|   |       |            |
|   |                                             |   |       |            |
|   |                                             |   |<-5位->|            |
|   |                                             |   数据中心ID            |
|   |                                             |                        |
|   |                                             |<------- 10位工作机器ID ------>|
|   |                                                                      |
|   |                                                                      |<-12位序列号->|
|   |
|<-1位符号位(固定为0)
```

### 各部分说明

| 部分 | 位数 | 最大值 | 说明 |
|------|------|--------|------|
| 符号位 | 1 | - | 固定为0，保证生成的ID为正数 |
| 时间戳 | 41 | 2^41-1 | 毫秒级时间戳，可使用约69年 |
| 数据中心ID | 5 | 31 | 支持32个数据中心 |
| 工作机器ID | 10 | 1023 | 支持1024台机器 |
| 序列号 | 12 | 4095 | 同一毫秒内最多生成4096个ID |

## ⚡ 核心特性

### 1. 配置化支持
- 支持通过配置文件设置WorkerId、DatacenterId
- 可配置自定义起始时间戳
- 支持时钟回拨检测开关

### 2. 线程安全
- 使用锁机制确保并发安全
- 支持高并发ID生成
- 防止序列号溢出

### 3. 时钟回拨检测
- 检测系统时钟回拨
- 可配置容忍的回拨时间
- 自动等待时钟追上

### 4. 性能优化
- 使用自旋等待替代Thread.Sleep
- 批量生成支持
- 内存友好的实现

### 5. 易用性
- 提供静态助手类
- 支持字符串ID转换
- 完整的ID解析功能

## 🔧 配置说明

### appsettings.json 配置

```json
{
  "Snowflake": {
    "WorkerId": 1,                    // 工作机器ID (0-1023)
    "DatacenterId": 1,                // 数据中心ID (0-31)
    "Epoch": "2020-01-01T00:00:00Z",  // 起始时间戳
    "EnableClockBackwardsCheck": true, // 启用时钟回拨检测
    "MaxClockBackwardsMs": 5          // 最大容忍回拨时间(毫秒)
  }
}
```

### 环境配置建议

| 环境 | WorkerId | DatacenterId | 说明 |
|------|----------|--------------|------|
| 开发环境 | 1 | 1 | 本地开发使用 |
| 测试环境 | 2 | 2 | 测试环境使用 |
| 生产环境 | 3+ | 3+ | 根据实际部署配置 |

## 💻 使用方法

### 1. 依赖注入方式

```csharp
public class UserService
{
    private readonly ISnowflakeIdGenerator _snowflakeGenerator;

    public UserService(ISnowflakeIdGenerator snowflakeGenerator)
    {
        _snowflakeGenerator = snowflakeGenerator;
    }

    public async Task<long> CreateUserAsync()
    {
        var userId = await _snowflakeGenerator.NextIdAsync();
        // 创建用户逻辑
        return userId;
    }
}
```

### 2. 静态助手方式

```csharp
public class OrderService
{
    public void CreateOrder()
    {
        // 生成订单ID
        var orderId = SnowflakeHelper.NextId();
        var orderIdString = SnowflakeHelper.NextStringId();
        
        // 批量生成ID
        var batchIds = SnowflakeHelper.NextIds(10);
        
        // 解析ID信息
        var idInfo = SnowflakeHelper.ParseId(orderId);
        Console.WriteLine($"生成时间: {idInfo.GeneratedAt}");
        Console.WriteLine($"工作机器: {idInfo.WorkerId}");
    }
}
```

### 3. 批量生成

```csharp
// 批量生成1000个ID
var ids = _snowflakeGenerator.NextIds(1000);

// 转换为字符串ID
var stringIds = ids.ToStringIds();
```

### 4. ID解析

```csharp
var id = 1234567890123456789L;
var info = _snowflakeGenerator.ParseId(id);

Console.WriteLine($"ID: {info.Id}");
Console.WriteLine($"生成时间: {info.GeneratedAt}");
Console.WriteLine($"工作机器ID: {info.WorkerId}");
Console.WriteLine($"数据中心ID: {info.DatacenterId}");
Console.WriteLine($"序列号: {info.Sequence}");
```

### 5. ID验证

```csharp
var id = 1234567890123456789L;
var isValid = id.IsValidSnowflakeId(_snowflakeGenerator);

if (isValid)
{
    var generatedTime = id.GetGeneratedTime(_snowflakeGenerator);
    Console.WriteLine($"ID生成时间: {generatedTime}");
}
```

## 🚀 API 接口

### 生成ID

```http
GET /api/snowflake/next
```

### 批量生成

```http
GET /api/snowflake/batch?count=10
```

### 解析ID

```http
GET /api/snowflake/parse/1234567890123456789
GET /api/snowflake/parse?stringId=1234567890123456789
```

### 获取生成器信息

```http
GET /api/snowflake/info
```

### 验证ID

```http
GET /api/snowflake/validate/1234567890123456789
```

### 性能测试

```http
POST /api/snowflake/performance-test?count=1000
```

## 📊 性能指标

### 理论性能
- **单机QPS**: 400万+ (4096 * 1000)
- **并发支持**: 高并发安全
- **ID长度**: 64位长整型
- **使用年限**: 约69年

### 实际测试
- **生成速度**: 100万ID/秒 (单线程)
- **内存占用**: 极低
- **CPU占用**: 极低

## ⚠️ 注意事项

### 1. WorkerId 配置
- 每个实例必须配置唯一的WorkerId
- 同一数据中心内WorkerId不能重复
- 建议使用配置文件管理

### 2. 时钟同步
- 确保服务器时钟同步
- 避免手动调整系统时间
- 监控时钟回拨情况

### 3. 部署建议
- 不同环境使用不同的DatacenterId
- 集群部署时确保WorkerId唯一性
- 建议使用配置中心管理ID分配

### 4. 性能优化
- 批量生成时注意内存使用
- 避免频繁的字符串转换
- 合理设置时钟回拨容忍时间

## 🔍 故障排查

### 常见问题

1. **时钟回拨错误**
   ```
   Clock moved backwards. Refusing to generate id for X milliseconds
   ```
   - 检查系统时钟同步
   - 调整MaxClockBackwardsMs配置

2. **WorkerId冲突**
   ```
   Duplicate WorkerId detected
   ```
   - 确保每个实例WorkerId唯一
   - 检查配置文件

3. **配置验证失败**
   ```
   WorkerId must be between 0 and 1023
   ```
   - 检查配置值范围
   - 验证配置文件格式

### 监控建议

- 监控ID生成速率
- 监控时钟回拨事件
- 监控生成器状态信息

## 📈 扩展功能

### 1. 自定义格式
可以基于雪花ID实现自定义格式的ID生成器

### 2. 分布式协调
结合配置中心实现WorkerId自动分配

### 3. 监控集成
集成到监控系统，实时监控ID生成状态

## 🔗 相关链接

- [Twitter Snowflake 算法](https://github.com/twitter-archive/snowflake)
- [分布式ID生成器对比](https://tech.meituan.com/2017/04/21/mt-leaf.html)
- [CYSF 项目文档](../../../README.md)
