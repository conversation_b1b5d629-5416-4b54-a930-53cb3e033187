using System;
using System.Threading.Tasks;

namespace CYSF.Core.Snowflake;

/// <summary>
/// 雪花ID生成器接口
/// </summary>
public interface ISnowflakeIdGenerator
{
    /// <summary>
    /// 生成下一个唯一ID
    /// </summary>
    /// <returns>64位唯一ID</returns>
    long NextId();

    /// <summary>
    /// 异步生成下一个唯一ID
    /// </summary>
    /// <returns>64位唯一ID</returns>
    Task<long> NextIdAsync();

    /// <summary>
    /// 批量生成唯一ID
    /// </summary>
    /// <param name="count">生成数量</param>
    /// <returns>ID数组</returns>
    long[] NextIds(int count);

    /// <summary>
    /// 解析雪花ID
    /// </summary>
    /// <param name="id">雪花ID</param>
    /// <returns>解析结果</returns>
    SnowflakeIdInfo ParseId(long id);

    /// <summary>
    /// 获取生成器信息
    /// </summary>
    /// <returns>生成器信息</returns>
    SnowflakeGeneratorInfo GetGeneratorInfo();
}

/// <summary>
/// 雪花ID解析信息
/// </summary>
public class SnowflakeIdInfo
{
    /// <summary>
    /// 原始ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 时间戳部分
    /// </summary>
    public long Timestamp { get; set; }

    /// <summary>
    /// 数据中心ID
    /// </summary>
    public long DatacenterId { get; set; }

    /// <summary>
    /// 工作机器ID
    /// </summary>
    public long WorkerId { get; set; }

    /// <summary>
    /// 序列号
    /// </summary>
    public long Sequence { get; set; }

    /// <summary>
    /// 生成时间
    /// </summary>
    public DateTime GeneratedAt { get; set; }

    /// <summary>
    /// 转换为字符串
    /// </summary>
    public override string ToString()
    {
        return $"Id: {Id}, Timestamp: {Timestamp}, DatacenterId: {DatacenterId}, " +
               $"WorkerId: {WorkerId}, Sequence: {Sequence}, GeneratedAt: {GeneratedAt:yyyy-MM-dd HH:mm:ss.fff}";
    }
}

/// <summary>
/// 雪花ID生成器信息
/// </summary>
public class SnowflakeGeneratorInfo
{
    /// <summary>
    /// 工作机器ID
    /// </summary>
    public long WorkerId { get; set; }

    /// <summary>
    /// 数据中心ID
    /// </summary>
    public long DatacenterId { get; set; }

    /// <summary>
    /// 起始时间
    /// </summary>
    public DateTime Epoch { get; set; }

    /// <summary>
    /// 生成器启动时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 已生成ID数量
    /// </summary>
    public long GeneratedCount { get; set; }

    /// <summary>
    /// 最后生成时间
    /// </summary>
    public DateTime LastGeneratedTime { get; set; }

    /// <summary>
    /// 当前序列号
    /// </summary>
    public long CurrentSequence { get; set; }
}
