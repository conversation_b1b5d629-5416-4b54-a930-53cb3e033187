using System;

namespace CYSF.Core.Snowflake;

/// <summary>
/// 雪花ID生成器配置选项
/// </summary>
public class SnowflakeOptions
{
    /// <summary>
    /// 配置节点名称
    /// </summary>
    public const string SectionName = "Snowflake";

    /// <summary>
    /// 工作机器ID (0-1023)
    /// </summary>
    public long WorkerId { get; set; } = 1;

    /// <summary>
    /// 数据中心ID (0-31)
    /// </summary>
    public long DatacenterId { get; set; } = 1;

    /// <summary>
    /// 自定义起始时间戳 (默认: 2020-01-01 00:00:00 UTC)
    /// </summary>
    public DateTime Epoch { get; set; } = new DateTime(2020, 1, 1, 0, 0, 0, DateTimeKind.Utc);

    /// <summary>
    /// 是否启用时钟回拨检测
    /// </summary>
    public bool EnableClockBackwardsCheck { get; set; } = true;

    /// <summary>
    /// 最大时钟回拨容忍时间（毫秒）
    /// </summary>
    public long MaxClockBackwardsMs { get; set; } = 5;

    /// <summary>
    /// 验证配置是否有效
    /// </summary>
    public void Validate()
    {
        if (WorkerId < 0 || WorkerId > 1023)
            throw new ArgumentOutOfRangeException(nameof(WorkerId), "WorkerId must be between 0 and 1023");

        if (DatacenterId < 0 || DatacenterId > 31)
            throw new ArgumentOutOfRangeException(nameof(DatacenterId), "DatacenterId must be between 0 and 31");

        if (Epoch > DateTime.UtcNow)
            throw new ArgumentException("Epoch cannot be in the future", nameof(Epoch));

        if (MaxClockBackwardsMs < 0)
            throw new ArgumentOutOfRangeException(nameof(MaxClockBackwardsMs), "MaxClockBackwardsMs cannot be negative");
    }
}
