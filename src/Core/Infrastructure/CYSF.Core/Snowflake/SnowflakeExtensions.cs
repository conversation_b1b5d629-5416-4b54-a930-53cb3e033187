using System;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;

namespace CYSF.Core.Snowflake;

/// <summary>
/// 雪花ID生成器扩展方法
/// </summary>
public static class SnowflakeExtensions
{
    /// <summary>
    /// 添加雪花ID生成器服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddSnowflakeIdGenerator(this IServiceCollection services, IConfiguration configuration)
    {
        // 绑定配置
        services.Configure<SnowflakeOptions>(configuration.GetSection(SnowflakeOptions.SectionName));

        // 添加配置验证
        services.AddOptions<SnowflakeOptions>()
            .Bind(configuration.GetSection(SnowflakeOptions.SectionName))
            .Validate(options =>
            {
                try
                {
                    options.Validate();
                    return true;
                }
                catch
                {
                    return false;
                }
            }, "Invalid Snowflake configuration");

        // 注册雪花ID生成器为单例
        services.AddSingleton<ISnowflakeIdGenerator, SnowflakeIdGenerator>();

        return services;
    }

    /// <summary>
    /// 添加雪花ID生成器服务（使用委托配置）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureOptions">配置委托</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddSnowflakeIdGenerator(this IServiceCollection services, Action<SnowflakeOptions> configureOptions)
    {
        services.Configure(configureOptions);

        // 添加配置验证
        services.AddOptions<SnowflakeOptions>()
            .Configure(configureOptions)
            .Validate(options =>
            {
                try
                {
                    options.Validate();
                    return true;
                }
                catch
                {
                    return false;
                }
            }, "Invalid Snowflake configuration");

        // 注册雪花ID生成器为单例
        services.AddSingleton<ISnowflakeIdGenerator, SnowflakeIdGenerator>();

        return services;
    }

    /// <summary>
    /// 转换为字符串ID
    /// </summary>
    /// <param name="id">雪花ID</param>
    /// <returns>字符串ID</returns>
    public static string ToStringId(this long id)
    {
        return id.ToString();
    }

    /// <summary>
    /// 从字符串解析雪花ID
    /// </summary>
    /// <param name="stringId">字符串ID</param>
    /// <returns>雪花ID</returns>
    public static long FromStringId(this string stringId)
    {
        if (string.IsNullOrEmpty(stringId))
            throw new ArgumentException("String ID cannot be null or empty", nameof(stringId));

        if (!long.TryParse(stringId, out var id))
            throw new ArgumentException("Invalid string ID format", nameof(stringId));

        return id;
    }

    /// <summary>
    /// 检查是否为有效的雪花ID
    /// </summary>
    /// <param name="id">ID</param>
    /// <param name="generator">雪花ID生成器</param>
    /// <returns>是否有效</returns>
    public static bool IsValidSnowflakeId(this long id, ISnowflakeIdGenerator generator)
    {
        try
        {
            var info = generator.ParseId(id);
            return info.GeneratedAt > new DateTime(2020, 1, 1) && info.GeneratedAt <= DateTime.UtcNow.AddMinutes(1);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 获取ID生成时间
    /// </summary>
    /// <param name="id">雪花ID</param>
    /// <param name="generator">雪花ID生成器</param>
    /// <returns>生成时间</returns>
    public static DateTime GetGeneratedTime(this long id, ISnowflakeIdGenerator generator)
    {
        var info = generator.ParseId(id);
        return info.GeneratedAt;
    }

    /// <summary>
    /// 获取工作机器ID
    /// </summary>
    /// <param name="id">雪花ID</param>
    /// <param name="generator">雪花ID生成器</param>
    /// <returns>工作机器ID</returns>
    public static long GetWorkerId(this long id, ISnowflakeIdGenerator generator)
    {
        var info = generator.ParseId(id);
        return info.WorkerId;
    }

    /// <summary>
    /// 批量转换为字符串ID
    /// </summary>
    /// <param name="ids">雪花ID数组</param>
    /// <returns>字符串ID数组</returns>
    public static string[] ToStringIds(this long[] ids)
    {
        if (ids == null)
            return null;

        var stringIds = new string[ids.Length];
        for (int i = 0; i < ids.Length; i++)
        {
            stringIds[i] = ids[i].ToString();
        }

        return stringIds;
    }
}
