using CYSF.Core.Enums;

namespace CYSF.Core.HttpContextUser
{
    public interface IContextUser
    {
        int UserId { get; }
        string Token { get; }
        string RefreshToken { get; }
        string Name { get; }
        int RoleId { get; }
        int TenantId { get; }
        bool IsAuthenticated();
        /// <summary>
        /// 是否租户
        /// </summary>
        /// <returns></returns>
        bool IsTenant();
    }
}