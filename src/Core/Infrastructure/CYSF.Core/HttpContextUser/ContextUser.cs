using System;
using System.Linq;
using CYSF.Core.Enums;
using Microsoft.AspNetCore.Http;

namespace CYSF.Core.HttpContextUser
{
    public class ContextUser(IHttpContextAccessor accessor) : IContextUser
    {
        /// <summary>
        ///     Token
        /// </summary>
        public string Token => accessor.HttpContext?.Request.Headers["Authorization"].ToString().Replace("Bearer ", "");

        /// <summary>
        ///     RefreshToken
        /// </summary>
        public string RefreshToken => GetClaimStringValueByType("RefreshToken");

        /// <summary>
        ///     姓名
        /// </summary>
        public string Name => GetClaimStringValueByType("Name");

        /// <summary>
        ///     用户Id
        /// </summary>
        public int UserId => GetClaimIntValueByType("UserId");

        /// <summary>
        ///     角色Id
        /// </summary>
        public int RoleId => GetClaimIntValueByType("RoleId");

        /// <summary>
        ///     租户Id
        /// </summary>
        public int TenantId => GetClaimIntValueByType("TenantId");

        /// <summary>
        ///     已登录标识
        /// </summary>
        /// <returns></returns>
        public bool IsAuthenticated()
        {
            return accessor.HttpContext is { User.Identity.IsAuthenticated: true };
        }

        public bool IsTenant()
        {
            return this.TenantId > 0;
        }

        #region 私有方法

        private int GetClaimIntValueByType(string claimType)
        {
            try
            {
                var claim = (from item in accessor.HttpContext?.User.Claims
                    where item.Type == claimType
                    select item.Value).FirstOrDefault();
                return int.Parse(claim ?? "0");
            }
            catch (Exception)
            {
                return 0;
            }
        }

        private string GetClaimStringValueByType(string claimType)
        {
            try
            {
                var claim = (from item in accessor.HttpContext.User.Claims
                    where item.Type == claimType
                    select item.Value).FirstOrDefault();
                return claim;
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }

        #endregion
    }
}