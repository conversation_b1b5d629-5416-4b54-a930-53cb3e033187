using System;
using Microsoft.Extensions.DependencyInjection;

namespace CYSF.Core.Providers
{
    public class DependencyResolver
    {
        private static volatile DependencyResolver _resolver;
        private static readonly object _lock = new object();

        private readonly IServiceProvider _serviceProvider;

        private DependencyResolver(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public static DependencyResolver Current
        {
            get
            {
                if (_resolver == null)
                    throw new Exception("DependencyResolver not initialized. You should initialize it in Startup class");
                return _resolver;
            }
        }

        public static void Init(IServiceProvider services)
        {
            if (_resolver == null)
            {
                lock (_lock)
                {
                    if (_resolver == null)
                        _resolver = new DependencyResolver(services);
                }
            }
        }

        public object GetService(Type serviceType)
        {
            return _serviceProvider.GetService(serviceType);
        }

        public T GetService<T>()
        {
            return _serviceProvider.GetService<T>();
        }
    }
}