using System;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace CYSF.Core.Providers
{
    public class JsonContractResolver : DefaultContractResolver
    {
        protected override JsonConverter ResolveContractConverter(Type objectType)
        {
            return base.ResolveContractConverter(objectType);
        }

        protected override JsonLinqContract CreateLinqContract(Type objectType)
        {
            return base.CreateLinqContract(objectType);
        }

        protected override string ResolvePropertyName(string propertyName)
        {
            return propertyName.ToLower();
        }
    }
}