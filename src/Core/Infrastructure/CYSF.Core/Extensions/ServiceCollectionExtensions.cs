using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using CYSF.Core.Cache;
using CYSF.Core.Models;

namespace CYSF.Core.Extensions
{
    /// <summary>
    /// 服务集合扩展方法
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// 添加应用程序配置选项
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="configuration">配置</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddAppSettings(this IServiceCollection services, IConfiguration configuration)
        {
            // 配置强类型选项
            services.Configure<AppSettings>(configuration);
            services.Configure<RedisOptions>(configuration.GetSection("Redis"));
            services.Configure<HangfireOptions>(configuration.GetSection("Hangfire"));
            services.Configure<JwtOptions>(configuration.GetSection("Jwt"));
            services.Configure<SwaggerOptions>(configuration.GetSection("Swagger"));
            services.Configure<CorsOptions>(configuration.GetSection("Cors"));

            // 注册单例服务以便直接访问配置
            services.AddSingleton(provider => provider.GetRequiredService<IOptions<AppSettings>>().Value);
            services.AddSingleton(provider => provider.GetRequiredService<IOptions<RedisOptions>>().Value);
            services.AddSingleton(provider => provider.GetRequiredService<IOptions<HangfireOptions>>().Value);
            services.AddSingleton(provider => provider.GetRequiredService<IOptions<JwtOptions>>().Value);
            services.AddSingleton(provider => provider.GetRequiredService<IOptions<SwaggerOptions>>().Value);
            services.AddSingleton(provider => provider.GetRequiredService<IOptions<CorsOptions>>().Value);

            return services;
        }

        /// <summary>
        /// 验证配置选项
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection ValidateAppSettings(this IServiceCollection services)
        {
            // 数据库连接字符串验证已移至 SqlSugar 配置中进行
            // 使用 Configuration.GetConnectionString("DefaultConnection") 直接验证

            // Redis 配置验证已移至 Cache 配置中
            // services.AddOptions<RedisOptions>()
            //     .Validate(options => !string.IsNullOrEmpty(options.ConnectionString), "Redis连接字符串不能为空");

            services.AddOptions<JwtOptions>()
                .Validate(options => !string.IsNullOrEmpty(options.Secret), "JWT密钥不能为空")
                .Validate(options => !string.IsNullOrEmpty(options.Issuer), "JWT发行者不能为空")
                .Validate(options => !string.IsNullOrEmpty(options.Audience), "JWT受众不能为空");

            // 添加缓存配置验证
            services.AddOptions<CacheOptions>()
                .Validate(options => options.Provider == CacheProvider.Memory ||
                                   !string.IsNullOrEmpty(options.RedisConnectionString),
                         "当使用Redis缓存时，Redis连接字符串不能为空");

            return services;
        }
    }
}
