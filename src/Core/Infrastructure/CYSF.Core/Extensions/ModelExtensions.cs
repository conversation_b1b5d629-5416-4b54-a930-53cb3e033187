using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using CYSF.Core.Attributes;

namespace CYSF.Core.Extensions
{
    public static class ModelExtensions
    {
        /// <summary>
        ///     实体对比
        /// </summary>
        /// <typeparam name="T1"></typeparam>
        /// <typeparam name="T2"></typeparam>
        /// <param name="source"></param>
        /// <param name="current"></param>
        /// <returns></returns>
        public static (bool Matched, List<string> NotMatchedProps) Compare<T1, T2>(this T1 source, T2 current) where T1 : class where T2 : class
        {
            var diff = new List<string>();
            var property = source.GetType().GetProperties();
            if (current == null)
                return (
                    false,
                    property.Where(r => r.GetCustomAttributes().Any(x => x.GetType() == typeof(ExcludeCompareAttribute)) == false).Select(r => r.Name).ToList()
                );
            foreach (var info in property)
            {
                if (info.GetCustomAttributes().Any(r => r.GetType() == typeof(ExcludeCompareAttribute)))
                    continue;

                var name = info.Name;
                var val1 = info.GetValue(source, null);
                var val2 = current.GetType().GetProperty(name)?.GetValue(current, null);
                if (val1 is decimal && val2 is decimal)
                {
                    if (decimal.Parse(val1.ToString()).CompareTo(val2) != 0) diff.Add(info.Name);
                }
                else
                {
                    if (val1?.ToString() != val2?.ToString())
                        diff.Add(info.Name);
                }
            }

            return (diff.Count == 0, diff);
        }

        /// <summary>
        ///     实体对比
        /// </summary>
        /// <typeparam name="T1"></typeparam>
        /// <typeparam name="T2"></typeparam>
        /// <param name="source"></param>
        /// <param name="current"></param>
        /// <param name="ignoreProps"></param>
        /// <returns></returns>
        public static (bool Matched, List<string> NotMatchedProps) Compare<T1, T2>(this T1 source, T2 current, string[] ignoreProps = null) where T1 : class where T2 : class
        {
            var diff = new List<string>();
            var property = source.GetType().GetProperties();
            if (current == null)
                return (
                    false,
                    property.Where(r => r.GetCustomAttributes().Any(x => x.GetType() == typeof(ExcludeCompareAttribute)) == false).Select(r => r.Name).ToList()
                );
            foreach (var info in property)
            {
                if (info.GetCustomAttributes().Any(r => r.GetType() == typeof(ExcludeCompareAttribute)))
                    continue;

                var name = info.Name;
                var val1 = info.GetValue(source, null);
                var val2 = current.GetType().GetProperty(name)?.GetValue(current, null);
                if (val1 is decimal && val2 is decimal)
                {
                    if (decimal.Parse(val1.ToString()).CompareTo(val2) != 0 && (ignoreProps == null || !ignoreProps.Contains(info.Name))) diff.Add(info.Name);
                }
                else
                {
                    if (val1?.ToString() != val2?.ToString() && (ignoreProps == null || !ignoreProps.Contains(info.Name)))
                        diff.Add(info.Name);
                }
            }

            return (diff.Count == 0, diff);
        }
    }
}