using System;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using Microsoft.AspNetCore.Http;

namespace CYSF.Core.Extensions
{
    public static class HttpExtensions
    {
        /// <summary>
        ///     是否Ajax请求
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public static bool IsAjaxRequest(this HttpRequest request)
        {
            if (request == null) throw new ArgumentNullException(nameof(request));

            return request.Headers.ContainsKey("X-Requested-With") &&
                   request.Headers["X-Requested-With"].Equals("XMLHttpRequest");
        }

        /// <summary>
        ///     获取客户端IP
        /// </summary>
        /// <param name="context">请求Context</param>
        /// <returns></returns>
        public static string GetClientIp(this HttpContext context)
        {
            var ip = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (string.IsNullOrEmpty(ip) || ip.ToLower() == "unknown")
            {
                ip = context.Request.Headers["Proxy-Client-IP"].FirstOrDefault();
            }
            if (string.IsNullOrEmpty(ip) || ip.ToLower() == "unknown")
            {
                ip = context.Connection.RemoteIpAddress?.ToString();
            }
            // 处理多级代理（如 ***********:12345 -> ***********）
            if (!string.IsNullOrEmpty(ip) && ip.Contains(","))
            {
                ip = ip.Split(',')[0].Trim();
            }
            return ip == "::1" ? "127.0.0.1" : ip;
        }

        public static string GetAbsoluteUri(this HttpRequest request)
        {
            return new StringBuilder()
                .Append(request.Scheme)
                .Append("://")
                .Append(request.Host)
                .Append(request.PathBase)
                .Append(request.Path)
                .Append(request.QueryString)
                .ToString();
        }


        /// <summary>
        ///     通过UserAgent获取浏览器准确版本
        ///     仅匹配: msie,firefox,chrome,opera,safari
        /// </summary>
        /// <param name="userAgent">UserAgent</param>
        /// <returns>能匹配,返回准确版本,不能匹配,返回原始UserAgent</returns>
        public static (string Name, string Version, string FullName) GetBrowser(this string userAgent)
        {
            // IE
            var ie = @"msie (?<ver>[\d.]+)";
            var firefox = @"firefox\/([\d.]+)";
            var chrome = @"chrome\/([\d.]+)";
            var opera = @"opera.([\d.]+)";
            var safari = @"version\/([\d.]+).*safari";
            bool isMatched;
            string version;
            (isMatched, version) = MatchBrowser(ie, userAgent);
            if (isMatched) return ("IE", version, $"IE {version}");

            (isMatched, version) = MatchBrowser(firefox, userAgent);
            if (isMatched) return ("Firefox", version, $"Firefox {version}");

            (isMatched, version) = MatchBrowser(chrome, userAgent);
            if (isMatched) return ("Chrome", version, $"Chrome {version}");

            (isMatched, version) = MatchBrowser(opera, userAgent);
            if (isMatched) return ("Opera", version, $"Opera {version}");

            (isMatched, version) = MatchBrowser(safari, userAgent);

            return isMatched ? ("Safari", version, $"Safari {version}") : (string.Empty, string.Empty, userAgent);
        }

        /// <summary>
        ///     匹配浏览器
        /// </summary>
        /// <param name="pattern">正则</param>
        /// <param name="userAgent">浏览器</param>
        /// <returns></returns>
        private static (bool IsMatched, string Version) MatchBrowser(string pattern, string userAgent)
        {
            var regex = new Regex(pattern, RegexOptions.IgnoreCase);
            var match = regex.Match(userAgent.ToLower());

            if (!match.Success) return (false, string.Empty);
            return !string.IsNullOrEmpty(match.Groups["ver"].Value) ? (true, match.Groups["ver"].Value) : (true, match.Groups[1].Value);
        }
    }
}