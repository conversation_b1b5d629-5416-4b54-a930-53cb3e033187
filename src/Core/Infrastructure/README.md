# 🏗️ CYSF.Core - 核心基础设施层

## 📋 项目概述

CYSF.Core是系统的核心基础设施层，提供通用的基础功能、工具类、扩展方法、异常处理、缓存管理等。该层为整个系统提供底层支撑，被所有其他项目引用。

## 🏗️ 项目架构

### 技术栈
- **框架**: .NET 8.0
- **缓存**: 统一缓存抽象（Redis/MemoryCache 可配置）
- **依赖注入**: Autofac 8.1.1
- **对象映射**: Mapster 7.4.1
- **任务调度**: Hangfire 1.8.17
- **配置管理**: Microsoft.Extensions.Configuration 8.0.0
- **雪花ID**: 自定义雪花ID生成器

### 项目结构
```
CYSF.Core/
├── Attributes/                # 自定义特性
│   └── PermissionAttribute.cs    # 权限验证特性
├── Cache/                     # 缓存抽象层
│   ├── ICacheService.cs          # 缓存服务接口
│   ├── MemoryCacheService.cs     # 内存缓存实现
│   ├── RedisCacheService.cs      # Redis缓存实现
│   └── CacheHelper.cs            # 缓存助手
├── Exceptions/                # 异常定义
│   ├── ApiException.cs           # API异常
│   └── BusinessException.cs      # 业务异常
├── Extensions/                # 扩展方法
│   ├── ServiceCollectionExtensions.cs # 服务集合扩展
│   ├── StringExtensions.cs       # 字符串扩展
│   └── QueryableExtensions.cs    # 查询扩展
├── Helpers/                   # 工具类
│   ├── EncryptHelper.cs          # 加密助手
│   ├── FileHelper.cs             # 文件助手
│   └── ConfigHelper.cs           # 配置助手
├── HttpContextUser/           # 用户上下文
│   ├── IContextUser.cs           # 用户上下文接口
│   └── ContextUser.cs            # 用户上下文实现
├── Models/                    # 核心模型
│   ├── ApiResult.cs              # API响应模型
│   ├── PageResult.cs             # 分页结果模型
│   └── CacheOptions.cs           # 缓存选项
├── Providers/                 # 提供者
│   ├── ExpressionProvider.cs     # 表达式提供者
│   └── SortProvider.cs           # 排序提供者
├── Snowflake/                 # 雪花ID生成器
│   ├── ISnowflakeIdGenerator.cs  # 雪花ID接口
│   └── SnowflakeIdGenerator.cs   # 雪花ID实现
└── CYSF.Core.csproj          # 项目文件
```

## 🚀 核心功能

### 1. 异常处理体系
提供统一的异常处理机制：

<augment_code_snippet path="src/Core/Infrastructure/CYSF.Core/Exceptions/ApiException.cs" mode="EXCERPT">
````csharp
/// <summary>
/// API异常类
/// </summary>
public class ApiException : Exception
{
    /// <summary>
    /// 错误码
    /// </summary>
    public int Code { get; set; }

    public ApiException(string message, int code = 400) : base(message)
    {
        Code = code;
    }

    public ApiException(string message, Exception innerException, int code = 400) 
        : base(message, innerException)
    {
        Code = code;
    }
}
````
</augment_code_snippet>

### 2. Redis缓存管理
提供完整的Redis缓存操作：

```csharp
/// <summary>
/// Redis助手类
/// </summary>
public static class RedisHelper
{
    /// <summary>
    /// 缓存壳模式 - 先从缓存获取，没有则执行方法并缓存结果
    /// </summary>
    public static async Task<T> CacheShellAsync<T>(string key, TimeSpan expiry, Func<Task<T>> func)
    {
        var cached = await GetAsync<T>(key);
        if (cached != null)
            return cached;

        var result = await func();
        if (result != null)
            await SetAsync(key, result, expiry);

        return result;
    }

    /// <summary>
    /// 设置缓存
    /// </summary>
    public static async Task<bool> SetAsync<T>(string key, T value, TimeSpan? expiry = null)
    {
        return await RedisClient.SetAsync(key, value, expiry);
    }

    /// <summary>
    /// 获取缓存
    /// </summary>
    public static async Task<T> GetAsync<T>(string key)
    {
        return await RedisClient.GetAsync<T>(key);
    }
}
```

### 3. 扩展方法
提供丰富的扩展方法：

```csharp
/// <summary>
/// 查询扩展方法
/// </summary>
public static class QueryableExtensions
{
    /// <summary>
    /// 动态排序
    /// </summary>
    public static IQueryable<T> SortBy<T>(this IQueryable<T> query, List<PageSort> sorts)
    {
        if (sorts == null || !sorts.Any())
            return query;

        foreach (var sort in sorts)
        {
            query = sort.IsDesc 
                ? query.OrderByDescending(x => EF.Property<object>(x, sort.Field))
                : query.OrderBy(x => EF.Property<object>(x, sort.Field));
        }

        return query;
    }

    /// <summary>
    /// 分页查询
    /// </summary>
    public static async Task<PageResult<T>> ToPageResultAsync<T>(
        this IQueryable<T> query, 
        int pageIndex, 
        int pageSize)
    {
        var totalCount = await query.CountAsync();
        var items = await query
            .Skip((pageIndex - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return new PageResult<T>
        {
            Items = items,
            TotalCount = totalCount,
            PageIndex = pageIndex,
            PageSize = pageSize
        };
    }
}
```

### 4. 工具类集合
提供各种实用工具：

```csharp
/// <summary>
/// 加密助手
/// </summary>
public static class EncryptHelper
{
    /// <summary>
    /// MD5加密
    /// </summary>
    public static string MD5Encrypt(string input)
    {
        using var md5 = MD5.Create();
        var bytes = md5.ComputeHash(Encoding.UTF8.GetBytes(input));
        return Convert.ToHexString(bytes).ToLower();
    }

    /// <summary>
    /// SHA256加密
    /// </summary>
    public static string SHA256Encrypt(string input)
    {
        using var sha256 = SHA256.Create();
        var bytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(input));
        return Convert.ToHexString(bytes).ToLower();
    }
}

/// <summary>
/// 文件助手
/// </summary>
public static class FileHelper
{
    /// <summary>
    /// 创建目录
    /// </summary>
    public static void CreateDirectory(string path)
    {
        if (!Directory.Exists(path))
            Directory.CreateDirectory(path);
    }

    /// <summary>
    /// 创建文件
    /// </summary>
    public static void CreateFile(string filePath, string content, Encoding encoding = null)
    {
        encoding ??= Encoding.UTF8;
        File.WriteAllText(filePath, content, encoding);
    }
}
```

## 🔧 核心特性

### 1. 依赖注入配置
```csharp
/// <summary>
/// 服务集合扩展
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加核心服务
    /// </summary>
    public static IServiceCollection AddCoreServices(this IServiceCollection services, IConfiguration configuration)
    {
        // 添加Redis
        services.AddSingleton<IConnectionMultiplexer>(provider =>
        {
            var connectionString = configuration.GetConnectionString("Redis");
            return ConnectionMultiplexer.Connect(connectionString);
        });

        // 添加Mapster
        services.AddMapster();

        // 添加Hangfire
        services.AddHangfire(config =>
        {
            config.UseSqlServerStorage(configuration.GetConnectionString("DefaultConnection"));
        });

        return services;
    }
}
```

### 2. 表达式构建器
```csharp
/// <summary>
/// 表达式提供者
/// </summary>
public static class ExpressionProvider
{
    /// <summary>
    /// 构建动态Where条件
    /// </summary>
    public static Expression<Func<T, bool>> BuildWhereExpression<T>(Dictionary<string, object> conditions)
    {
        var parameter = Expression.Parameter(typeof(T), "x");
        Expression body = null;

        foreach (var condition in conditions)
        {
            var property = Expression.Property(parameter, condition.Key);
            var constant = Expression.Constant(condition.Value);
            var equal = Expression.Equal(property, constant);

            body = body == null ? equal : Expression.AndAlso(body, equal);
        }

        return body == null 
            ? x => true 
            : Expression.Lambda<Func<T, bool>>(body, parameter);
    }
}
```

### 3. 配置管理
```csharp
/// <summary>
/// 配置助手
/// </summary>
public static class ConfigHelper
{
    private static IConfiguration _configuration;

    /// <summary>
    /// 初始化配置
    /// </summary>
    public static void Initialize(IConfiguration configuration)
    {
        _configuration = configuration;
    }

    /// <summary>
    /// 获取配置值
    /// </summary>
    public static T GetValue<T>(string key, T defaultValue = default)
    {
        return _configuration.GetValue(key, defaultValue);
    }

    /// <summary>
    /// 获取连接字符串
    /// </summary>
    public static string GetConnectionString(string name)
    {
        return _configuration.GetConnectionString(name);
    }
}
```

## 📦 依赖关系

### NuGet包
- `Autofac` 8.1.1
- `Autofac.Extensions.DependencyInjection` 10.0.0
- `CSRedisCore` 3.8.800
- `Mapster` 7.4.0
- `Hangfire.AspNetCore` 1.8.20
- `Microsoft.Extensions.Configuration` 8.0.0
- `EPPlus` 4.5.3.3

## 🛠️ 开发指南

### 添加新的工具类

1. **创建工具类**
```csharp
/// <summary>
/// 新工具助手
/// </summary>
public static class NewHelper
{
    /// <summary>
    /// 工具方法
    /// </summary>
    public static string ProcessData(string input)
    {
        // 实现具体逻辑
        return input.ToUpper();
    }

    /// <summary>
    /// 异步工具方法
    /// </summary>
    public static async Task<string> ProcessDataAsync(string input)
    {
        // 实现异步逻辑
        await Task.Delay(100);
        return input.ToLower();
    }
}
```

2. **添加扩展方法**
```csharp
/// <summary>
/// 新扩展方法
/// </summary>
public static class NewExtensions
{
    /// <summary>
    /// 字符串扩展
    /// </summary>
    public static bool IsValidEmail(this string email)
    {
        if (string.IsNullOrEmpty(email))
            return false;

        return Regex.IsMatch(email, @"^[^@\s]+@[^@\s]+\.[^@\s]+$");
    }

    /// <summary>
    /// 集合扩展
    /// </summary>
    public static bool IsNullOrEmpty<T>(this IEnumerable<T> source)
    {
        return source == null || !source.Any();
    }
}
```

### 添加新的异常类型

```csharp
/// <summary>
/// 自定义业务异常
/// </summary>
public class CustomBusinessException : BusinessException
{
    public CustomBusinessException(string message) : base(message)
    {
    }

    public CustomBusinessException(string message, Exception innerException) 
        : base(message, innerException)
    {
    }
}
```

## 🔍 最佳实践

### 1. 异常处理
- 使用特定的异常类型
- 提供清晰的错误消息
- 记录异常详细信息
- 避免暴露敏感信息

### 2. 缓存策略
- 合理设置缓存过期时间
- 使用缓存壳模式
- 及时清理无效缓存
- 监控缓存命中率

### 3. 工具类设计
- 保持方法的纯函数特性
- 提供完整的XML注释
- 考虑线程安全性
- 添加参数验证

### 4. 扩展方法
- 只为常用操作添加扩展
- 保持扩展方法简洁
- 避免副作用
- 考虑性能影响

## 🧪 测试

### 工具类测试
```csharp
[Test]
public void EncryptHelper_MD5Encrypt_ReturnsCorrectHash()
{
    // Arrange
    var input = "test";
    var expected = "098f6bcd4621d373cade4e832627b4f6";

    // Act
    var result = EncryptHelper.MD5Encrypt(input);

    // Assert
    Assert.AreEqual(expected, result);
}
```

### 扩展方法测试
```csharp
[Test]
public void StringExtensions_IsValidEmail_ValidEmail_ReturnsTrue()
{
    // Arrange
    var email = "<EMAIL>";

    // Act
    var result = email.IsValidEmail();

    // Assert
    Assert.IsTrue(result);
}
```

## 📚 相关文档

- [CYSF.Generate - 代码生成器](CYSF.Generate/README.md)
- [Redis官方文档](https://redis.io/documentation)
- [Autofac文档](https://docs.autofac.org/)
- [Mapster文档](https://github.com/MapsterMapper/Mapster)
- [Hangfire文档](https://docs.hangfire.io/)

---

📚 更多信息请参考 [主项目文档](../../../README.md)
