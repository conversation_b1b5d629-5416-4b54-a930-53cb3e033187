using System;
using CYSF.Application.HttpClient;
using CYSF.Core.Cache;
using CYSF.Core.Configuration;
using CYSF.Core.HttpContextUser;
using CYSF.Core.Snowflake;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Api.Backend.Extensions;

/// <summary>
/// 基础服务注册扩展
/// </summary>
public static class BaseServiceExtensions
{
    /// <summary>
    /// 添加基础服务
    /// </summary>
    public static IServiceCollection AddBaseServices(this IServiceCollection services, IConfiguration configuration)
    {
        // HttpContext 注入
        services.AddHttpContextAccessor();
        services.AddScoped<IContextUser, ContextUser>();

        // HTTP客户端
        services.AddHttpClient<DownloadClient>();

        // 雪花ID生成器服务
        services.AddSnowflakeIdGenerator(configuration);

        // Session支持（用于Hangfire Dashboard认证）
        services.AddDistributedMemoryCache();
        services.AddSession(options =>
        {
            options.IdleTimeout = TimeSpan.FromMinutes(30);
            options.Cookie.HttpOnly = true;
            options.Cookie.IsEssential = true;
        });

        services.AddOptions();

        return services;
    }
}
