using System;
using Hangfire;
using Hangfire.SqlServer;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Api.Backend.Extensions;

/// <summary>
/// 后台任务服务注册扩展
/// </summary>
public static class BackgroundServiceExtensions
{
    /// <summary>
    /// 添加后台任务服务
    /// </summary>
    public static IServiceCollection AddBackgroundServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Hangfire配置
        services.AddHangfire(r => r
            .SetDataCompatibilityLevel(CompatibilityLevel.Version_170)
            .UseSimpleAssemblyNameTypeSerializer()
            .UseRecommendedSerializerSettings()
            .UseSqlServerStorage(configuration["Hangfire:ConnectionString"], new SqlServerStorageOptions()
            {
                QueuePollInterval = TimeSpan.FromMinutes(15),
                JobExpirationCheckInterval = TimeSpan.FromHours(1),
                CountersAggregateInterval = TimeSpan.FromMinutes(5),
                PrepareSchemaIfNecessary = true,
                DashboardJobListLimit = 50000,
                TransactionTimeout = TimeSpan.FromMinutes(1),
            }));

        services.AddHangfireServer(r =>
        {
            r.ServerName = "Api.Backend";
            r.WorkerCount = 1;
            r.SchedulePollingInterval = TimeSpan.FromMinutes(1);
        });

        return services;
    }
}
