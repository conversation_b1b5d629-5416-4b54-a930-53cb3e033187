using System;
using CYSF.Repositories.Base;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using SqlSugar;

namespace Api.Backend.Extensions;

/// <summary>
/// 数据库服务注册扩展
/// </summary>
public static class DatabaseServiceExtensions
{
    /// <summary>
    /// 添加数据库服务
    /// </summary>
    public static IServiceCollection AddDatabaseServices(this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment environment)
    {
        services.AddScoped(typeof(BaseRepository<>));

        // 修改为线程安全的SqlSugar配置 - 使用SqlSugarScope确保线程安全
        services.AddSingleton<ISqlSugarClient>(provider =>
        {
            var connectionString = configuration.GetConnectionString("DefaultConnection");
            var config = new ConnectionConfig
            {
                ConnectionString = connectionString,
                DbType = SqlSugar.DbType.SqlServer,
                IsAutoCloseConnection = true,
                InitKeyType = InitKeyType.Attribute,
                MoreSettings = new ConnMoreSettings
                {
                    IsAutoRemoveDataCache = true,
                    SqlServerCodeFirstNvarchar = true
                }
            };

            // 使用SqlSugarScope确保线程安全
            var sqlSugar = new SqlSugarScope(config, db =>
            {
                // 保留原有的开发环境日志配置
                if (environment.IsDevelopment())
                {
                    db.Aop.OnLogExecuting = (sql, pars) =>
                    {
                        var originalsColor = Console.ForegroundColor;
                        Console.ForegroundColor = ConsoleColor.Green;
                        Console.WriteLine(UtilMethods.GetNativeSql(sql, pars));
                        Console.ForegroundColor = originalsColor;
                    };

                    db.Aop.OnError = exception =>
                    {
                        Console.ForegroundColor = ConsoleColor.Red;
                        Console.WriteLine($"SQL执行错误: {exception.Message}");
                        if (exception.InnerException != null)
                        {
                            Console.WriteLine($"内部异常: {exception.InnerException.Message}");
                        }
                        Console.ResetColor();
                    };
                }
            });

            return sqlSugar;
        });

        return services;
    }
}
