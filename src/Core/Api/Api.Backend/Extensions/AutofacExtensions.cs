using System.Reflection;
using Autofac;

namespace Api.Backend.Extensions;

/// <summary>
/// Autofac依赖注入扩展
/// </summary>
public static class AutofacExtensions
{
    /// <summary>
    /// 配置Autofac容器
    /// </summary>
    public static void ConfigureAutofacContainer(this ContainerBuilder builder)
    {
        // 注入Application层
        builder.RegisterApplicationServices();
        
        // 注入Service层
        builder.RegisterBusinessServices();
        
        // 注入Repository层
        builder.RegisterRepositoryServices();
    }

    /// <summary>
    /// 注册Application层服务
    /// </summary>
    private static void RegisterApplicationServices(this ContainerBuilder builder)
    {
        var assemblyApplication = Assembly.Load("CYSF.Application");
        builder
            .RegisterAssemblyTypes(assemblyApplication)
            .Where(type => type.Name.EndsWith("App"))
            .InstancePerLifetimeScope()
            .PropertiesAutowired(PropertyWiringOptions.AllowCircularDependencies);
    }

    /// <summary>
    /// 注册Service层服务
    /// </summary>
    private static void RegisterBusinessServices(this ContainerBuilder builder)
    {
        var assemblyServices = Assembly.Load("CYSF.Services");
        builder.RegisterAssemblyTypes(assemblyServices)
            .InstancePerLifetimeScope()
            .PropertiesAutowired(PropertyWiringOptions.AllowCircularDependencies);
    }

    /// <summary>
    /// 注册Repository层服务
    /// </summary>
    private static void RegisterRepositoryServices(this ContainerBuilder builder)
    {
        var assemblyRepository = Assembly.Load("CYSF.Repositories");

        // 注册实现了接口的Repository类
        builder.RegisterAssemblyTypes(assemblyRepository)
            .AsImplementedInterfaces()
            .InstancePerLifetimeScope()
            .PropertiesAutowired(PropertyWiringOptions.AllowCircularDependencies);

        // 注册具体的Repository类（用于没有接口的Repository）
        builder.RegisterAssemblyTypes(assemblyRepository)
            .Where(t => t.Name.EndsWith("Repository"))
            .AsSelf()
            .InstancePerLifetimeScope()
            .PropertiesAutowired(PropertyWiringOptions.AllowCircularDependencies);
    }
}
