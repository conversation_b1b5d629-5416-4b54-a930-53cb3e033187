using Api.Backend.Filters;
using CYSF.Core.Configuration;
using CYSF.Core.Extensions;
using CYSF.Models.Request.Auth;
using FluentValidation;
using FluentValidation.AspNetCore;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;

namespace Api.Backend.Extensions;

/// <summary>
/// API相关服务注册扩展
/// </summary>
public static class ApiServiceExtensions
{
    /// <summary>
    /// 添加API相关服务
    /// </summary>
    public static IServiceCollection AddApiServices(this IServiceCollection services, IConfiguration configuration)
    {
        // CORS配置
        services.AddCorsServices(configuration);

        // 控制器和过滤器配置
        services.AddControllers(options =>
            {
                options.Filters.Add(typeof(ApiExceptionFilter));
                options.Filters.Add(typeof(ApiResultFilter));
                options.Filters.Add(typeof(ValidationFilter));
            })
            .ConfigureApiBehaviorOptions(options =>
            {
                // 禁用默认的模型验证过滤器，使用我们自定义的ValidationFilter
                options.SuppressModelStateInvalidFilter = true;
            })
            .AddNewtonsoftJson(options =>
            {
                // 使用统一的JSON配置
                var settings = JsonConfiguration.GetDefaultSettings();
                options.SerializerSettings.ContractResolver = settings.ContractResolver;
                options.SerializerSettings.ReferenceLoopHandling = settings.ReferenceLoopHandling;
                options.SerializerSettings.DateFormatString = settings.DateFormatString;
                options.SerializerSettings.NullValueHandling = settings.NullValueHandling;
                options.SerializerSettings.DateTimeZoneHandling = settings.DateTimeZoneHandling;
                options.SerializerSettings.DefaultValueHandling = settings.DefaultValueHandling;

                // 添加长整型转字符串转换器，解决JavaScript精度问题
                options.SerializerSettings.Converters.Add(new NumberConverter(NumberConverterShip.Int64));
            });

        // FluentValidation配置
        services.AddValidationServices();

        return services;
    }

    /// <summary>
    /// 添加CORS服务
    /// </summary>
    private static IServiceCollection AddCorsServices(this IServiceCollection services, IConfiguration configuration)
    {
        var allowHosts = configuration["Cors:AllowHosts"]?.Split(';');
        services.AddCors(options =>
        {
            options.AddPolicy("AllowHosts", policy =>
            {
                if (allowHosts != null)
                    policy.SetIsOriginAllowedToAllowWildcardSubdomains()
                        .WithOrigins(allowHosts)
                        .AllowAnyHeader()
                        .AllowAnyMethod()
                        .AllowCredentials();
            });
        });

        return services;
    }

    /// <summary>
    /// 添加验证服务
    /// </summary>
    private static IServiceCollection AddValidationServices(this IServiceCollection services)
    {
        // 配置FluentValidation,自动注册所有验证器
        services.AddValidatorsFromAssemblyContaining<LoginReqValidator>();
        services.AddFluentValidationAutoValidation();
        services.AddFluentValidationClientsideAdapters();

        return services;
    }
}
