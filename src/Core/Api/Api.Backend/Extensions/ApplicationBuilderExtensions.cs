using System.Text;
using CYSF.Application;
using CYSF.Core.Providers;
using CYSF.Core.Snowflake;
using Hangfire;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Swashbuckle.AspNetCore.SwaggerUI;

namespace Api.Backend.Extensions;

/// <summary>
/// 应用程序构建器扩展
/// </summary>
public static class ApplicationBuilderExtensions
{
    /// <summary>
    /// 配置应用程序中间件管道
    /// </summary>
    public static IApplicationBuilder ConfigureApplicationPipeline(this IApplicationBuilder app, IConfiguration configuration, IWebHostEnvironment environment)
    {
        // 初始化依赖解析器和雪花ID
        app.InitializeDependencies();
        
        // 添加编码支持
        Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

        // 环境相关配置
        app.ConfigureEnvironmentSpecific(environment);

        // 初始化应用程序
        app.InitializeApplication(environment);
        
        // 配置代理头
        app.ConfigureProxyHeaders();
        
        // 配置路由和认证
        app.ConfigureRoutingAndAuth();
        
        // 配置文档
        app.ConfigureDocumentation(configuration);

        // 配置Hangfire Dashboard
        app.ConfigureHangfireDashboard(configuration);

        // 配置端点
        app.ConfigureEndpoints();

        return app;
    }

    /// <summary>
    /// 初始化依赖项
    /// </summary>
    private static IApplicationBuilder InitializeDependencies(this IApplicationBuilder app)
    {
        DependencyResolver.Init(app.ApplicationServices);
        SnowflakeHelper.Initialize(app.ApplicationServices);
        return app;
    }

    /// <summary>
    /// 配置环境特定设置
    /// </summary>
    private static IApplicationBuilder ConfigureEnvironmentSpecific(this IApplicationBuilder app, IWebHostEnvironment environment)
    {
        if (environment.IsDevelopment())
            app.UseDeveloperExceptionPage();
        else
            app.UseHsts();

        return app;
    }

    /// <summary>
    /// 初始化应用程序
    /// </summary>
    private static IApplicationBuilder InitializeApplication(this IApplicationBuilder app, IWebHostEnvironment environment)
    {
        // if (!environment.IsDevelopment())
        // {
            var jobApp = DependencyResolver.Current.GetService<JobApp>();
            jobApp.Init();
        //}
        return app;
    }

    /// <summary>
    /// 配置代理头（解决 Nginx 代理不能获取IP问题）
    /// </summary>
    private static IApplicationBuilder ConfigureProxyHeaders(this IApplicationBuilder app)
    {
        app.UseForwardedHeaders(new ForwardedHeadersOptions
        {
            ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto
        });
        return app;
    }

    /// <summary>
    /// 配置路由和认证
    /// </summary>
    private static IApplicationBuilder ConfigureRoutingAndAuth(this IApplicationBuilder app)
    {
        app.UseRouting();
        app.UseCors("AllowHosts");
        app.UseSession(); // 添加Session支持（用于Hangfire Dashboard认证）
        app.UseAuthentication();
        app.UseAuthorization();
        return app;
    }

    /// <summary>
    /// 配置文档
    /// </summary>
    private static IApplicationBuilder ConfigureDocumentation(this IApplicationBuilder app, IConfiguration configuration)
    {
        app.UseSwagger();
        app.UseSwaggerUI(c =>
        {
            c.SwaggerEndpoint("/swagger/v1/swagger.json", $"{configuration["Swagger:Title"]}-接口 v1");
            c.RoutePrefix = "swagger";
            
            // 优化Swagger UI显示
            c.DocExpansion(DocExpansion.None); // 默认折叠所有接口
            c.DefaultModelsExpandDepth(-1); // 隐藏Models部分
            c.DisplayRequestDuration(); // 显示请求耗时
            c.EnableFilter(); // 启用过滤器
            c.ShowExtensions(); // 显示扩展信息
            c.EnableDeepLinking(); // 启用深度链接
            c.DisplayOperationId(); // 显示操作ID

            // 设置页面标题
            c.DocumentTitle = $"{configuration["Swagger:Title"]} - API文档";
        });
        return app;
    }

    /// <summary>
    /// 配置Hangfire Dashboard
    /// </summary>
    private static IApplicationBuilder ConfigureHangfireDashboard(this IApplicationBuilder app, IConfiguration configuration)
    {
        var hangfireEnabled = configuration.GetValue<bool>("Hangfire:Enabled", false);
        var dashboardEnabled = configuration.GetValue<bool>("Hangfire:DashboardEnabled", false);

        if (hangfireEnabled && dashboardEnabled)
        {
            var dashboardPath = configuration.GetValue<string>("Hangfire:DashboardPath", "/hangfire");

            app.UseHangfireDashboard(dashboardPath, new DashboardOptions
            {
                Authorization = new[] { new Filters.HangfireDashboardAuthorizationFilter(configuration) },
                DashboardTitle = "CYSF - 后台任务监控",
                StatsPollingInterval = 2000, // 2秒刷新一次统计信息
                DisplayStorageConnectionString = false // 不显示连接字符串
            });
        }

        return app;
    }

    /// <summary>
    /// 配置端点
    /// </summary>
    private static IApplicationBuilder ConfigureEndpoints(this IApplicationBuilder app)
    {
        app.UseEndpoints(endpoints =>
        {
            endpoints.MapControllers();
            endpoints.MapSwagger();

            // 添加健康检查端点
            endpoints.MapHealthChecks("/health");
            endpoints.MapHealthChecks("/health/ready", new HealthCheckOptions
            {
                Predicate = check => check.Tags.Contains("ready")
            });
            endpoints.MapHealthChecks("/health/live", new HealthCheckOptions
            {
                Predicate = check => check.Tags.Contains("live")
            });
        });
        return app;
    }
}
