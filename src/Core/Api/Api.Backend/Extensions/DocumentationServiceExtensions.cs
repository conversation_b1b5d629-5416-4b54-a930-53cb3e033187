using System;
using System.IO;
using System.Reflection;
using Api.Backend.Swagger;
using Microsoft.DotNet.PlatformAbstractions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.OpenApi.Models;

namespace Api.Backend.Extensions;

/// <summary>
/// 文档服务注册扩展
/// </summary>
public static class DocumentationServiceExtensions
{
    /// <summary>
    /// 添加文档服务
    /// </summary>
    public static IServiceCollection AddDocumentationServices(this IServiceCollection services, IConfiguration configuration)
    {
        var jwtIssuer = configuration["Jwt:Issuer"];
        var basePath = ApplicationEnvironment.ApplicationBasePath;

        services.AddSwaggerGen(options =>
        {
            // 配置API文档信息
            options.SwaggerDoc("v1", new OpenApiInfo
            {
                Version = "v1",
                Title = $"{configuration["Swagger:Title"]}-接口文档",
                Description = configuration["Swagger:Description"]
            });

            // 枚举过滤器
            options.SchemaFilter<SwaggerEnumFilter>();

            // XML注释文件
            var xmlApiPath = Path.Combine(basePath, "Api.Backend.xml");
            options.IncludeXmlComments(xmlApiPath, true);
            var xmlModelPath = Path.Combine(basePath, "CYSF.Models.xml");
            options.IncludeXmlComments(xmlModelPath);

            // JWT安全定义
            options.AddSecurityDefinition(jwtIssuer, new OpenApiSecurityScheme
            {
                In = ParameterLocation.Header,
                Description = "JWT授权(数据将在请求头中进行传输) 直接在下框中输入Bearer {token}",
                Name = "Authorization",
                Type = SecuritySchemeType.ApiKey
            });
            
            options.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {
                    new OpenApiSecurityScheme
                    {
                        Reference = new OpenApiReference
                        {
                            Type = ReferenceType.SecurityScheme,
                            Id = jwtIssuer
                        }
                    },
                    []
                }
            });
        });

        return services;
    }
}
