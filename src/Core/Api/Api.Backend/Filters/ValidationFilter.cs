using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using CYSF.Core.Enums;
using CYSF.Core.Models;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;

namespace Api.Backend.Filters
{
    /// <summary>
    /// 最终优化的验证过滤器
    /// 完全遵循ApiResult返回格式，支持快速失败和详细错误信息
    /// </summary>
    public class ValidationFilter : IActionFilter
    {
        private readonly ILogger<ValidationFilter> _logger;

        public ValidationFilter(ILogger<ValidationFilter> logger)
        {
            _logger = logger;
        }

        public void OnActionExecuting(ActionExecutingContext context)
        {
            // 检查模型验证状态
            if (!context.ModelState.IsValid)
            {
                // 处理验证错误并返回ApiResult格式
                var validationResult = ProcessValidationErrors(context);
                
                // 记录验证失败日志
                LogValidationFailure(context, validationResult);

                // 直接返回ApiResult格式，不抛出异常
                context.Result = new OkObjectResult(new ApiResult
                {
                    code = ApiStatusCode.LogicException,
                    message = validationResult.Message,
                    data = CreateValidationErrorData(validationResult)
                });

                return;
            }
        }

        public void OnActionExecuted(ActionExecutedContext context)
        {
            // 执行后不需要处理
        }

        /// <summary>
        /// 处理验证错误
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        private ValidationResult ProcessValidationErrors(ActionExecutingContext context)
        {
            // 收集验证错误信息
            var errors = context.ModelState
                .Where(x => x.Value.Errors.Count > 0)
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value.Errors.Select(e => e.ErrorMessage).ToArray()
                );

            return ValidationResult.Failure(errors);
        }

        /// <summary>
        /// 记录验证失败日志
        /// </summary>
        /// <param name="context"></param>
        /// <param name="validationResult"></param>
        private void LogValidationFailure(ActionExecutingContext context, ValidationResult validationResult)
        {
            var controllerName = context.Controller.GetType().Name;
            var actionName = context.ActionDescriptor.DisplayName;
            
            _logger.LogWarning("验证失败 - Controller: {Controller}, Action: {Action}, 错误: {Message}, 错误数量: {ErrorCount}",
                controllerName, actionName, validationResult.Message, validationResult.ErrorCount);

            // 详细错误日志（Debug级别）
            if (_logger.IsEnabled(LogLevel.Debug))
            {
                foreach (var error in validationResult.Errors)
                {
                    _logger.LogDebug("验证错误详情 - 字段: {Field}, 错误: {Errors}",
                        error.Key, string.Join(", ", error.Value));
                }
            }
        }

        /// <summary>
        /// 创建验证错误数据
        /// 根据配置决定是否返回详细错误信息
        /// </summary>
        /// <param name="validationResult"></param>
        /// <returns></returns>
        private object CreateValidationErrorData(ValidationResult validationResult)
        {
            // 在开发环境返回详细错误信息，生产环境只返回基本信息
            var isDevelopment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Development";

            if (isDevelopment)
            {
                return new
                {
                    validationErrors = validationResult.Errors,
                    errorCount = validationResult.ErrorCount,
                    timestamp = validationResult.Timestamp,
                    message = "详细验证错误信息（仅开发环境显示）"
                };
            }
            else
            {
                return new
                {
                    errorCount = validationResult.ErrorCount,
                    timestamp = validationResult.Timestamp,
                    message = "验证失败，请检查输入参数"
                };
            }
        }
    }
}

