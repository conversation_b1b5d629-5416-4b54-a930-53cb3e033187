using System;
using System.Text;
using Hangfire.Dashboard;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;

namespace Api.Backend.Filters;

/// <summary>
/// Hangfire Dashboard 认证过滤器
/// </summary>
public class HangfireDashboardAuthorizationFilter : IDashboardAuthorizationFilter
{
    private readonly IConfiguration _configuration;

    public HangfireDashboardAuthorizationFilter(IConfiguration configuration)
    {
        _configuration = configuration;
    }

    public bool Authorize(DashboardContext context)
    {
        var httpContext = context.GetHttpContext();

        // 检查是否需要认证
        var requireAuth = _configuration.GetValue<bool>("Hangfire:Dashboard:RequireAuthentication", true);
        if (!requireAuth)
        {
            return true;
        }

        // 检查是否已经通过认证
        if (httpContext.Session.GetString("HangfireAuth") == "authenticated")
        {
            return true;
        }

        // 检查基本认证头
        var authHeader = httpContext.Request.Headers["Authorization"].ToString();
        if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Basic "))
        {
            // 返回401并要求基本认证
            SetUnauthorizedResponse(httpContext);
            return false;
        }

        try
        {
            // 解析基本认证
            var encodedCredentials = authHeader.Substring("Basic ".Length).Trim();
            var credentials = Encoding.UTF8.GetString(Convert.FromBase64String(encodedCredentials));
            var parts = credentials.Split(':', 2);

            if (parts.Length != 2)
            {
                SetUnauthorizedResponse(httpContext);
                return false;
            }

            var username = parts[0];
            var password = parts[1];

            // 验证用户名和密码
            var configUsername = _configuration.GetValue<string>("Hangfire:Dashboard:Username", "admin");
            var configPassword = _configuration.GetValue<string>("Hangfire:Dashboard:Password", "admin123");

            if (username == configUsername && password == configPassword)
            {
                // 认证成功，设置会话
                httpContext.Session.SetString("HangfireAuth", "authenticated");
                return true;
            }
        }
        catch (Exception)
        {
            // 认证失败
        }

        SetUnauthorizedResponse(httpContext);
        return false;
    }

    private static void SetUnauthorizedResponse(Microsoft.AspNetCore.Http.HttpContext httpContext)
    {
        httpContext.Response.StatusCode = 401;
        httpContext.Response.Headers["WWW-Authenticate"] = "Basic realm=\"Hangfire Dashboard\"";
    }
}
