# 📊 Hangfire Dashboard 使用指南

## 🎯 概述

Hangfire Dashboard 是 CYSF 系统的后台任务监控和管理界面，提供了强大的任务调度、监控和管理功能。

## 🔐 访问认证

### 访问地址
- **开发环境**: http://127.0.0.1:2021/hangfire
- **测试环境**: http://127.0.0.1:6061/hangfire
- **生产环境**: http://127.0.0.1:8081/hangfire

### 登录凭据

| 环境 | 用户名 | 密码 | 说明 |
|------|--------|------|------|
| 开发环境 | admin | admin123 | 开发调试使用 |
| 测试环境 | admin | test123 | 测试验证使用 |
| 生产环境 | admin | Prod@2024! | 生产环境强密码 |

### 认证方式
- 使用 **HTTP Basic Authentication**
- 认证成功后使用 **Session** 保持登录状态
- 可通过配置文件控制是否需要认证

## 🎛️ 主要功能

### 1. 仪表板概览 (Dashboard)
- **实时统计**: 显示成功、失败、处理中的任务数量
- **服务器状态**: 显示 Hangfire 服务器运行状态
- **队列信息**: 显示各队列的任务数量
- **历史图表**: 显示任务执行的历史趋势

### 2. 任务管理 (Jobs)
- **任务列表**: 查看所有任务的详细信息
- **任务状态**: 
  - ✅ **Succeeded** - 执行成功
  - ❌ **Failed** - 执行失败
  - ⏳ **Processing** - 正在执行
  - 📅 **Scheduled** - 已调度
  - ⏸️ **Enqueued** - 已入队
- **任务操作**:
  - 🔄 **重试失败任务**
  - 🗑️ **删除任务**
  - 📋 **查看任务详情**

### 3. 队列管理 (Queues)
系统配置了三个队列：
- **default**: 默认队列，处理常规任务
- **critical**: 高优先级队列，处理重要任务
- **background**: 后台队列，处理低优先级任务

### 4. 服务器监控 (Servers)
- **服务器列表**: 显示所有 Hangfire 服务器
- **服务器状态**: 显示服务器的运行状态和配置
- **工作线程**: 显示每个服务器的工作线程数量

### 5. 重复任务 (Recurring Jobs)
- **任务列表**: 显示所有定时任务
- **Cron 表达式**: 显示任务的执行计划
- **任务操作**: 手动触发、暂停、恢复定时任务

## ⚙️ 配置管理

### 基本配置
```json
{
  "Hangfire": {
    "Enabled": true,
    "DashboardEnabled": true,
    "DashboardPath": "/hangfire",
    "WorkerCount": 5,
    "Queues": ["default", "critical", "background"],
    "Dashboard": {
      "Username": "admin",
      "Password": "admin123",
      "RequireAuthentication": true
    }
  }
}
```

### 配置说明
- **Enabled**: 是否启用 Hangfire 服务
- **DashboardEnabled**: 是否启用 Dashboard
- **DashboardPath**: Dashboard 访问路径
- **WorkerCount**: 工作线程数量
- **Queues**: 队列配置
- **Dashboard.RequireAuthentication**: 是否需要认证

## 🔧 常用操作

### 1. 查看任务执行情况
1. 访问 Dashboard 主页
2. 查看实时统计数据
3. 点击具体数字查看详细任务列表

### 2. 重试失败任务
1. 进入 **Jobs** → **Failed** 页面
2. 选择要重试的任务
3. 点击 **Requeue** 按钮

### 3. 手动触发定时任务
1. 进入 **Recurring Jobs** 页面
2. 找到目标任务
3. 点击 **Trigger now** 按钮

### 4. 监控队列状态
1. 进入 **Queues** 页面
2. 查看各队列的任务数量
3. 点击队列名称查看详细任务

## 🛡️ 安全建议

### 开发环境
- ✅ 启用 Dashboard 进行调试
- ✅ 使用默认密码即可
- ✅ 可以关闭认证便于开发

### 测试环境
- ✅ 启用 Dashboard 进行测试验证
- ✅ 使用测试环境专用密码
- ✅ 启用认证确保安全

### 生产环境
- ⚠️ **建议禁用 Dashboard** 或使用强密码
- ⚠️ 通过防火墙限制访问
- ⚠️ 定期更换密码
- ⚠️ 监控访问日志

## 🚨 故障排除

### 1. 无法访问 Dashboard
- 检查 `DashboardEnabled` 配置
- 确认端口是否正确
- 检查防火墙设置

### 2. 认证失败
- 确认用户名密码是否正确
- 检查配置文件中的认证信息
- 清除浏览器缓存和 Session

### 3. 任务不执行
- 检查 Hangfire 服务是否启动
- 确认数据库连接是否正常
- 查看服务器状态和工作线程

### 4. 性能问题
- 调整 `WorkerCount` 配置
- 优化任务执行逻辑
- 监控数据库性能

## 📚 相关文档

- [配置指南](CONFIGURATION_GUIDE.md)
- [开发指南](../../../DEVELOPMENT_GUIDE.md)
- [API 文档](README.md)
- [Hangfire 官方文档](https://docs.hangfire.io/)

---

💡 **提示**: Dashboard 是监控和管理后台任务的重要工具，建议开发和测试阶段充分利用其功能来优化任务执行效率。
