using System.Threading.Tasks;
using CYSF.Application;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Api.Backend.Controllers;

/// <summary>
///     公共Api
/// </summary>
[Route("api/[controller]/[action]")]
[ApiController]
public class CommonController(CommonApp commonApp) : ControllerBase
{
    /// <summary>
    ///     获取一个或者多个枚举键值对
    ///     多个使用英文逗号分隔
    /// </summary>
    /// <param name="enums">枚举类型名称</param>
    /// <returns></returns>
    [HttpGet]
    [AllowAnonymous]
    public async Task<IActionResult> GetEnumList(string enums)
    {
        return Ok(await commonApp.GetEnumList(enums));
    }
}