using System;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;

namespace Api.Backend.Controllers;

/// <summary>
/// 健康检查控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
[AllowAnonymous]
public class HealthController : ControllerBase
{
    private readonly HealthCheckService _healthCheckService;
    private readonly ILogger<HealthController> _logger;

    public HealthController(HealthCheckService healthCheckService, ILogger<HealthController> logger)
    {
        _healthCheckService = healthCheckService;
        _logger = logger;
    }

    /// <summary>
    /// 获取系统健康状态
    /// </summary>
    /// <returns>系统健康状态信息</returns>
    [HttpGet]
    public async Task<IActionResult> Get()
    {
        try
        {
            var healthReport = await _healthCheckService.CheckHealthAsync();
            
            var response = new
            {
                Status = healthReport.Status.ToString(),
                Duration = healthReport.TotalDuration.TotalMilliseconds,
                Timestamp = DateTime.UtcNow,
                Version = Assembly.GetExecutingAssembly().GetName().Version?.ToString(),
                Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT"),
                Checks = healthReport.Entries.Select(entry => new
                {
                    Name = entry.Key,
                    Status = entry.Value.Status.ToString(),
                    Duration = entry.Value.Duration.TotalMilliseconds,
                    Description = entry.Value.Description,
                    Data = entry.Value.Data,
                    Exception = entry.Value.Exception?.Message,
                    Tags = entry.Value.Tags
                })
            };

            var statusCode = healthReport.Status == HealthStatus.Healthy ? 200 : 503;
            return StatusCode(statusCode, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "健康检查异常");
            return StatusCode(503, new { Status = "Unhealthy", Message = ex.Message });
        }
    }

    /// <summary>
    /// 获取就绪状态检查
    /// </summary>
    /// <returns>就绪状态信息</returns>
    [HttpGet("ready")]
    public async Task<IActionResult> Ready()
    {
        try
        {
            var healthReport = await _healthCheckService.CheckHealthAsync(check => check.Tags.Contains("ready"));
            
            var response = new
            {
                Status = healthReport.Status.ToString(),
                Duration = healthReport.TotalDuration.TotalMilliseconds,
                Timestamp = DateTime.UtcNow,
                Checks = healthReport.Entries.Select(entry => new
                {
                    Name = entry.Key,
                    Status = entry.Value.Status.ToString(),
                    Duration = entry.Value.Duration.TotalMilliseconds,
                    Description = entry.Value.Description
                })
            };

            var statusCode = healthReport.Status == HealthStatus.Healthy ? 200 : 503;
            return StatusCode(statusCode, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "就绪检查异常");
            return StatusCode(503, new { Status = "Unhealthy", Message = ex.Message });
        }
    }

    /// <summary>
    /// 获取存活状态检查
    /// </summary>
    /// <returns>存活状态信息</returns>
    [HttpGet("live")]
    public async Task<IActionResult> Live()
    {
        try
        {
            var healthReport = await _healthCheckService.CheckHealthAsync(check => check.Tags.Contains("live"));
            
            var response = new
            {
                Status = healthReport.Status.ToString(),
                Duration = healthReport.TotalDuration.TotalMilliseconds,
                Timestamp = DateTime.UtcNow,
                Checks = healthReport.Entries.Select(entry => new
                {
                    Name = entry.Key,
                    Status = entry.Value.Status.ToString(),
                    Duration = entry.Value.Duration.TotalMilliseconds,
                    Description = entry.Value.Description
                })
            };

            var statusCode = healthReport.Status == HealthStatus.Healthy ? 200 : 503;
            return StatusCode(statusCode, response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "存活检查异常");
            return StatusCode(503, new { Status = "Unhealthy", Message = ex.Message });
        }
    }

    /// <summary>
    /// 获取系统信息
    /// </summary>
    /// <returns>系统基本信息</returns>
    [HttpGet("info")]
    public IActionResult Info()
    {
        try
        {
            var assembly = Assembly.GetExecutingAssembly();
            var version = assembly.GetName().Version?.ToString() ?? "Unknown";
            var buildDate = System.IO.File.GetLastWriteTime(assembly.Location);

            var info = new
            {
                Application = "CYSF Api.Backend",
                Version = version,
                BuildDate = buildDate,
                Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown",
                MachineName = Environment.MachineName,
                ProcessorCount = Environment.ProcessorCount,
                WorkingSet = Environment.WorkingSet,
                OSVersion = Environment.OSVersion.ToString(),
                Framework = Environment.Version.ToString(),
                Timestamp = DateTime.UtcNow
            };

            return Ok(info);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取系统信息异常");
            return StatusCode(500, new { Message = ex.Message });
        }
    }
}
