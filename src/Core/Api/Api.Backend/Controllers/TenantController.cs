using System.Threading.Tasks;
using CYSF.Application;
using CYSF.Core.Attributes;
using CYSF.Models.Request;
using CYSF.Models.Request.Tenant;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Api.Backend.Controllers;

/// <summary>
/// Tenant管理控制器
/// </summary>
[Route("api/[controller]/[action]")]
[ApiController]
[Authorize]
public class TenantController : ControllerBase
{
    private readonly TenantApp _tenantApp;
    private readonly ILogger<TenantController> _logger;

    public TenantController(TenantApp tenantApp, ILogger<TenantController> logger)
    {
        _tenantApp = tenantApp;
        _logger = logger;
    }

    /// <summary>
    /// 获取Tenant列表
    /// </summary>
    /// <param name="req">查询条件</param>
    /// <returns>Tenant列表</returns>
    [HttpPost]
    public async Task<IActionResult> GetPageList([FromBody] PageListReq<TenantPageListReq> req)
    {
        var result = await _tenantApp.GetPageListAsync(req);
        return Ok(result);
    }

    /// <summary>
    /// 根据ID获取Tenant详情
    /// </summary>
    /// <param name="id">TenantID</param>
    /// <returns>Tenant详情</returns>
    [HttpGet]
    public async Task<IActionResult> GetById([FromQuery] int id)
    {
        var result = await _tenantApp.GetByIdAsync(id);
        return Ok(result);
    }

    /// <summary>
    /// 创建Tenant
    /// </summary>
    /// <param name="entity">Tenant实体</param>
    /// <returns>创建的Tenant信息</returns>
    [HttpPost]
    public async Task<IActionResult> Create([FromBody] Tenant entity)
    {
        var result = await _tenantApp.CreateAsync(entity);
        return Ok(result);
    }

    /// <summary>
    /// 更新Tenant
    /// </summary>
    /// <param name="entity">Tenant实体</param>
    /// <returns>更新结果</returns>
    [HttpPost]
    public async Task<IActionResult> Update([FromBody] Tenant entity)
    {
        var result = await _tenantApp.UpdateAsync(entity);
        return Ok(result);
    }

    /// <summary>
    /// 删除Tenant
    /// </summary>
    /// <param name="id">TenantID</param>
    /// <returns>删除结果</returns>
    [HttpPost]
    public async Task<IActionResult> Delete([FromQuery] int id)
    {
        var result = await _tenantApp.DeleteAsync(id);
        return Ok(result);
    }

    /// <summary>
    /// 批量删除Tenant
    /// </summary>
    /// <param name="ids">TenantID列表</param>
    /// <returns>删除结果</returns>
    [HttpPost]
    public async Task<IActionResult> DeleteBatch([FromBody] List<int> ids)
    {
        var result = await _tenantApp.DeleteBatchAsync(ids);
        return Ok(result);
    }

    /// <summary>
    /// 检查Tenant是否存在
    /// </summary>
    /// <param name="id">TenantID</param>
    /// <returns>是否存在</returns>
    [HttpGet]
    public async Task<IActionResult> Exists([FromQuery] int id)
    {
        var result = await _tenantApp.ExistsAsync(id);
        return Ok(new { exists = result });
    }
}
