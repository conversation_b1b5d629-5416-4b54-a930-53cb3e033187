using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using CYSF.Application;
using CYSF.Models.Request.Tenant;
using CYSF.Core.Attributes;
using CYSF.Models.Request;

namespace Api.Backend.Controllers;

/// <summary>
/// 租户管理控制器
/// </summary>
[Route("api/[controller]/[action]")]
[ApiController]
[Authorize]
public class TenantController : ControllerBase
{
    private readonly TenantApp _tenantApp;
    private readonly ILogger<TenantController> _logger;

    public TenantController(TenantApp tenantApp, ILogger<TenantController> logger)
    {
        _tenantApp = tenantApp;
        _logger = logger;
    }

    /// <summary>
    /// 获取租户列表
    /// </summary>
    /// <param name="req">查询条件</param>
    /// <returns>租户列表</returns>
    [HttpPost]
    public async Task<IActionResult> GetPageList([FromBody] PageListReq<TenantPageListReq> req)
    {
        var result = await _tenantApp.GetPageListAsync(req);
        return Ok(result);
    }

    /// <summary>
    /// 根据ID获取租户详情
    /// </summary>
    /// <param name="id">租户ID</param>
    /// <returns>租户详情</returns>
    [HttpGet]
    public async Task<IActionResult> GetById([FromQuery] int id)
    {
        var result = await _tenantApp.GetByIdAsync(id);
        return Ok(result);
    }

    /// <summary>
    /// 创建租户
    /// </summary>
    /// <param name="req">创建请求</param>
    /// <returns>创建的租户信息</returns>
    [HttpPost]
    public async Task<IActionResult> Create([FromBody] CreateTenantReq req)
    {
        var result = await _tenantApp.CreateAsync(req);
        return Ok(result);
    }

    /// <summary>
    /// 更新租户
    /// </summary>
    /// <param name="req">更新请求</param>
    /// <returns>更新结果</returns>
    [HttpPost]
    public async Task<IActionResult> Update([FromBody] UpdateTenantReq req)
    {
        var result = await _tenantApp.UpdateAsync(req);
        return Ok(result);
    }

    /// <summary>
    /// 删除租户
    /// </summary>
    /// <param name="id">租户ID</param>
    /// <returns>删除结果</returns>
    [HttpPost]
    public async Task<IActionResult> Delete([FromQuery] int id)
    {
        var result = await _tenantApp.DeleteAsync(id);
        return Ok(result);
    }

    /// <summary>
    /// 检查租户编号是否可用
    /// </summary>
    /// <param name="code">租户编号</param>
    /// <param name="excludeId">排除的租户ID（用于更新时检查）</param>
    /// <returns>是否可用</returns>
    [HttpGet]
    public async Task<IActionResult> CheckCodeAvailable([FromQuery] string code, [FromQuery] int? excludeId = null)
    {
        var result = await _tenantApp.IsCodeAvailableAsync(code, excludeId);
        return Ok(new { available = result });
    }
}
