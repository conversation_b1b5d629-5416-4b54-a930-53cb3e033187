using System;
using System.Collections.Generic;
using System.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CYSF.Application;
using CYSF.Core.Attributes;
using CYSF.Models.Request;
using CYSF.Models.Request.Dic;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Api.Backend.Controllers;

/// <summary>
/// Dic管理控制器
/// </summary>
[Route("api/[controller]/[action]")]
[ApiController]
[Authorize]
public class DicController : ControllerBase
{
    private readonly DicApp _dicApp;
    private readonly ILogger<DicController> _logger;

    public DicController(DicApp dicApp, ILogger<DicController> logger)
    {
        _dicApp = dicApp;
        _logger = logger;
    }

    /// <summary>
    /// 获取Dic列表
    /// </summary>
    /// <param name="req">查询条件</param>
    /// <returns>Dic列表</returns>
    [HttpPost]
    public async Task<IActionResult> GetPageList([FromBody] PageListReq<DicPageListReq> req)
    {
        var result = await _dicApp.GetPageListAsync(req);
        return Ok(result);
    }

    /// <summary>
    /// 根据ID获取Dic详情
    /// </summary>
    /// <param name="id">DicID</param>
    /// <returns>Dic详情</returns>
    [HttpGet]
    public async Task<IActionResult> GetById([FromQuery] int id)
    {
        var result = await _dicApp.GetByIdAsync(id);
        return Ok(result);
    }

    /// <summary>
    /// 创建Dic
    /// </summary>
    /// <param name="entity">Dic实体</param>
    /// <returns>创建的Dic信息</returns>
    [HttpPost]
    public async Task<IActionResult> Create([FromBody] Dic entity)
    {
        var result = await _dicApp.CreateAsync(entity);
        return Ok(result);
    }

    /// <summary>
    /// 更新Dic
    /// </summary>
    /// <param name="entity">Dic实体</param>
    /// <returns>更新结果</returns>
    [HttpPost]
    public async Task<IActionResult> Update([FromBody] Dic entity)
    {
        var result = await _dicApp.UpdateAsync(entity);
        return Ok(result);
    }

    /// <summary>
    /// 删除Dic
    /// </summary>
    /// <param name="id">DicID</param>
    /// <returns>删除结果</returns>
    [HttpPost]
    public async Task<IActionResult> Delete([FromQuery] int id)
    {
        var result = await _dicApp.DeleteAsync(id);
        return Ok(result);
    }


}
