using System.Threading.Tasks;
using CYSF.Application;
using CYSF.Core.Attributes;
using CYSF.Models.Request;
using CYSF.Models.Request.TenantRole;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Api.Backend.Controllers;

/// <summary>
/// TenantRole管理控制器
/// </summary>
[Route("api/[controller]/[action]")]
[ApiController]
[Authorize]
public class TenantRoleController : ControllerBase
{
    private readonly TenantRoleApp _tenantRoleApp;
    private readonly ILogger<TenantRoleController> _logger;

    public TenantRoleController(TenantRoleApp tenantRoleApp, ILogger<TenantRoleController> logger)
    {
        _tenantRoleApp = tenantRoleApp;
        _logger = logger;
    }

    /// <summary>
    /// 获取TenantRole列表
    /// </summary>
    /// <param name="req">查询条件</param>
    /// <returns>TenantRole列表</returns>
    [HttpPost]
    public async Task<IActionResult> GetPageList([FromBody] PageListReq<TenantRolePageListReq> req)
    {
        var result = await _tenantRoleApp.GetPageListAsync(req);
        return Ok(result);
    }

    /// <summary>
    /// 根据ID获取TenantRole详情
    /// </summary>
    /// <param name="id">TenantRoleID</param>
    /// <returns>TenantRole详情</returns>
    [HttpGet]
    public async Task<IActionResult> GetById([FromQuery] int id)
    {
        var result = await _tenantRoleApp.GetByIdAsync(id);
        return Ok(result);
    }

    /// <summary>
    /// 创建TenantRole
    /// </summary>
    /// <param name="entity">TenantRole实体</param>
    /// <returns>创建的TenantRole信息</returns>
    [HttpPost]
    public async Task<IActionResult> Create([FromBody] TenantRole entity)
    {
        var result = await _tenantRoleApp.CreateAsync(entity);
        return Ok(result);
    }

    /// <summary>
    /// 更新TenantRole
    /// </summary>
    /// <param name="entity">TenantRole实体</param>
    /// <returns>更新结果</returns>
    [HttpPost]
    public async Task<IActionResult> Update([FromBody] TenantRole entity)
    {
        var result = await _tenantRoleApp.UpdateAsync(entity);
        return Ok(result);
    }

    /// <summary>
    /// 删除TenantRole
    /// </summary>
    /// <param name="id">TenantRoleID</param>
    /// <returns>删除结果</returns>
    [HttpPost]
    public async Task<IActionResult> Delete([FromQuery] int id)
    {
        var result = await _tenantRoleApp.DeleteAsync(id);
        return Ok(result);
    }

    /// <summary>
    /// 批量删除TenantRole
    /// </summary>
    /// <param name="ids">TenantRoleID列表</param>
    /// <returns>删除结果</returns>
    [HttpPost]
    public async Task<IActionResult> DeleteBatch([FromBody] List<int> ids)
    {
        var result = await _tenantRoleApp.DeleteBatchAsync(ids);
        return Ok(result);
    }

    /// <summary>
    /// 检查TenantRole是否存在
    /// </summary>
    /// <param name="id">TenantRoleID</param>
    /// <returns>是否存在</returns>
    [HttpGet]
    public async Task<IActionResult> Exists([FromQuery] int id)
    {
        var result = await _tenantRoleApp.ExistsAsync(id);
        return Ok(new { exists = result });
    }
}
