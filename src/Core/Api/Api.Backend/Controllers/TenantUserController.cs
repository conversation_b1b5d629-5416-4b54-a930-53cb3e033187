using System.Threading.Tasks;
using CYSF.Application;
using CYSF.Core.Attributes;
using CYSF.Models.Request;
using CYSF.Models.Request.TenantUser;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Api.Backend.Controllers;

/// <summary>
/// 租户用户管理控制器
/// </summary>
[Route("api/[controller]/[action]")]
[ApiController]
[Authorize]
public class TenantUserController : ControllerBase
{
    private readonly TenantUserApp _tenantUserApp;
    private readonly ILogger<TenantUserController> _logger;

    public TenantUserController(TenantUserApp tenantUserApp, ILogger<TenantUserController> logger)
    {
        _tenantUserApp = tenantUserApp;
        _logger = logger;
    }

    /// <summary>
    /// 获取租户用户列表
    /// </summary>
    /// <param name="req">查询条件</param>
    /// <returns>用户列表</returns>
    [HttpPost]
    public async Task<IActionResult> GetPageList([FromBody] PageListReq<TenantUserPageListReq> req)
    {
        var result = await _tenantUserApp.GetPageListAsync(req);
        return Ok(result);
    }

    /// <summary>
    /// 根据ID获取租户用户详情
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <returns>用户详情</returns>
    [HttpGet]
    public async Task<IActionResult> GetById([FromQuery] int id)
    {
        var result = await _tenantUserApp.GetByIdAsync(id);
        return Ok(result);
    }

    /// <summary>
    /// 创建租户用户
    /// </summary>
    /// <param name="req">创建请求</param>
    /// <returns>创建的用户信息</returns>
    [HttpPost]
    public async Task<IActionResult> Create([FromBody] CreateTenantUserReq req)
    {
        var result = await _tenantUserApp.CreateAsync(req);
        return Ok(result);
    }

    /// <summary>
    /// 更新租户用户
    /// </summary>
    /// <param name="req">更新请求</param>
    /// <returns>更新结果</returns>
    [HttpPost]
    public async Task<IActionResult> Update([FromBody] UpdateTenantUserReq req)
    {
        var result = await _tenantUserApp.UpdateAsync(req);
        return Ok(result);
    }

    /// <summary>
    /// 删除租户用户
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <returns>删除结果</returns>
    [HttpPost]
    public async Task<IActionResult> Delete([FromQuery] int id)
    {
        var result = await _tenantUserApp.DeleteAsync(id);
        return Ok(result);
    }

    /// <summary>
    /// 检查用户名是否可用
    /// </summary>
    /// <param name="userName">用户名</param>
    /// <param name="excludeId">排除的用户ID（用于更新时检查）</param>
    /// <returns>是否可用</returns>
    [HttpGet]
    public async Task<IActionResult> CheckUserNameAvailable([FromQuery] string userName, [FromQuery] int? excludeId = null)
    {
        var result = await _tenantUserApp.IsUserNameAvailableAsync(userName, excludeId);
        return Ok(new { available = result });
    }

    /// <summary>
    /// 检查手机号是否可用
    /// </summary>
    /// <param name="mobile">手机号</param>
    /// <param name="excludeId">排除的用户ID（用于更新时检查）</param>
    /// <returns>是否可用</returns>
    [HttpGet]
    public async Task<IActionResult> CheckMobileAvailable([FromQuery] string mobile, [FromQuery] int? excludeId = null)
    {
        var result = await _tenantUserApp.IsMobileAvailableAsync(mobile, excludeId);
        return Ok(new { available = result });
    }

    /// <summary>
    /// 重置用户密码
    /// </summary>
    /// <param name="req">重置密码请求</param>
    /// <returns>重置结果</returns>
    [HttpPost]
    public async Task<IActionResult> ResetPassword([FromBody] ResetPasswordReq req)
    {
        var result = await _tenantUserApp.ResetPasswordAsync(req);
        return Ok(result);
    }
}
