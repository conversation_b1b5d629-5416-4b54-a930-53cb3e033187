using System;
using System.Collections.Generic;
using System.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CYSF.Application;
using CYSF.Core.Attributes;
using CYSF.Models.Request;
using CYSF.Models.Request.TenantUser;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Api.Backend.Controllers;

/// <summary>
/// TenantUser管理控制器
/// </summary>
[Route("api/[controller]/[action]")]
[ApiController]
[Authorize]
public class TenantUserController : ControllerBase
{
    private readonly TenantUserApp _tenantUserApp;
    private readonly ILogger<TenantUserController> _logger;

    public TenantUserController(TenantUserApp tenantUserApp, ILogger<TenantUserController> logger)
    {
        _tenantUserApp = tenantUserApp;
        _logger = logger;
    }

    /// <summary>
    /// 获取TenantUser列表
    /// </summary>
    /// <param name="req">查询条件</param>
    /// <returns>TenantUser列表</returns>
    [HttpPost]
    public async Task<IActionResult> GetPageList([FromBody] PageListReq<TenantUserPageListReq> req)
    {
        var result = await _tenantUserApp.GetPageListAsync(req);
        return Ok(result);
    }

    /// <summary>
    /// 根据ID获取TenantUser详情
    /// </summary>
    /// <param name="id">TenantUserID</param>
    /// <returns>TenantUser详情</returns>
    [HttpGet]
    public async Task<IActionResult> GetById([FromQuery] int id)
    {
        var result = await _tenantUserApp.GetByIdAsync(id);
        return Ok(result);
    }

    /// <summary>
    /// 创建TenantUser
    /// </summary>
    /// <param name="entity">TenantUser实体</param>
    /// <returns>创建的TenantUser信息</returns>
    [HttpPost]
    public async Task<IActionResult> Create([FromBody] TenantUser entity)
    {
        var result = await _tenantUserApp.CreateAsync(entity);
        return Ok(result);
    }

    /// <summary>
    /// 更新TenantUser
    /// </summary>
    /// <param name="entity">TenantUser实体</param>
    /// <returns>更新结果</returns>
    [HttpPost]
    public async Task<IActionResult> Update([FromBody] TenantUser entity)
    {
        var result = await _tenantUserApp.UpdateAsync(entity);
        return Ok(result);
    }

    /// <summary>
    /// 删除TenantUser
    /// </summary>
    /// <param name="id">TenantUserID</param>
    /// <returns>删除结果</returns>
    [HttpPost]
    public async Task<IActionResult> Delete([FromQuery] int id)
    {
        var result = await _tenantUserApp.DeleteAsync(id);
        return Ok(result);
    }


}
