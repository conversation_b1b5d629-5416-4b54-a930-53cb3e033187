using System.Threading.Tasks;
using CYSF.Application;
using CYSF.Models.Request.Auth;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Api.Backend.Controllers;

/// <summary>
///     平台/租户用户登录
/// </summary>
[Route("api/[controller]/[action]")]
[ApiController]
public class TokenController : ControllerBase
{
    private readonly ILogger<TokenController> _logger;
    private readonly TokenApp _tokenApp;

    public TokenController(TokenApp tokenApp, ILogger<TokenController> logger)
    {
        _tokenApp = tokenApp;
        _logger = logger;
    }

    /// <summary>
    ///     获取Token
    /// </summary>
    /// <param name="req">请求实体</param>
    /// <returns></returns>
    [HttpPost]
    [AllowAnonymous]
    public async Task<IActionResult> Get([FromBody] LoginReq req)
    {
        return Ok(await _tokenApp.GetToken(req));
    }

    /// <summary>
    ///     刷新Token
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [AllowAnonymous]
    public async Task<IActionResult> Refresh([FromBody] RefreshTokenReq req)
    {
        return Ok(await _tokenApp.RefreshToken(req));
    }

    /// <summary>
    ///     删除Token
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [AllowAnonymous]
    public async Task<IActionResult> Delete([FromBody] DelTokenReq req)
    {
        return Ok(await _tokenApp.DelToken(req));
    }
}

