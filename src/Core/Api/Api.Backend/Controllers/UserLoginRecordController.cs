using System;
using System.Collections.Generic;
using System.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CYSF.Application;
using CYSF.Core.Attributes;
using CYSF.Models.Request;
using CYSF.Models.Request.UserLoginRecord;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Api.Backend.Controllers;

/// <summary>
/// UserLoginRecord管理控制器
/// </summary>
[Route("api/[controller]/[action]")]
[ApiController]
[Authorize]
public class UserLoginRecordController : ControllerBase
{
    private readonly UserLoginRecordApp _userLoginRecordApp;
    private readonly ILogger<UserLoginRecordController> _logger;

    public UserLoginRecordController(UserLoginRecordApp userLoginRecordApp, ILogger<UserLoginRecordController> logger)
    {
        _userLoginRecordApp = userLoginRecordApp;
        _logger = logger;
    }

    /// <summary>
    /// 获取UserLoginRecord列表
    /// </summary>
    /// <param name="req">查询条件</param>
    /// <returns>UserLoginRecord列表</returns>
    [HttpPost]
    public async Task<IActionResult> GetPageList([FromBody] PageListReq<UserLoginRecordPageListReq> req)
    {
        var result = await _userLoginRecordApp.GetPageListAsync(req);
        return Ok(result);
    }

    /// <summary>
    /// 根据ID获取UserLoginRecord详情
    /// </summary>
    /// <param name="id">UserLoginRecordID</param>
    /// <returns>UserLoginRecord详情</returns>
    [HttpGet]
    public async Task<IActionResult> GetById([FromQuery] int id)
    {
        var result = await _userLoginRecordApp.GetByIdAsync(id);
        return Ok(result);
    }

    /// <summary>
    /// 创建UserLoginRecord
    /// </summary>
    /// <param name="entity">UserLoginRecord实体</param>
    /// <returns>创建的UserLoginRecord信息</returns>
    [HttpPost]
    public async Task<IActionResult> Create([FromBody] UserLoginRecord entity)
    {
        var result = await _userLoginRecordApp.CreateAsync(entity);
        return Ok(result);
    }

    /// <summary>
    /// 更新UserLoginRecord
    /// </summary>
    /// <param name="entity">UserLoginRecord实体</param>
    /// <returns>更新结果</returns>
    [HttpPost]
    public async Task<IActionResult> Update([FromBody] UserLoginRecord entity)
    {
        var result = await _userLoginRecordApp.UpdateAsync(entity);
        return Ok(result);
    }

    /// <summary>
    /// 删除UserLoginRecord
    /// </summary>
    /// <param name="id">UserLoginRecordID</param>
    /// <returns>删除结果</returns>
    [HttpPost]
    public async Task<IActionResult> Delete([FromQuery] int id)
    {
        var result = await _userLoginRecordApp.DeleteAsync(id);
        return Ok(result);
    }


}
