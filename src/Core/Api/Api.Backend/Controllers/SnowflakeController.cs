using System;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using CYSF.Core.Snowflake;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Api.Backend.Controllers;

/// <summary>
/// 雪花ID生成器控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
[AllowAnonymous]
public class SnowflakeController : ControllerBase
{
    private readonly ISnowflakeIdGenerator _snowflakeGenerator;
    private readonly ILogger<SnowflakeController> _logger;

    public SnowflakeController(ISnowflakeIdGenerator snowflakeGenerator, ILogger<SnowflakeController> logger)
    {
        _snowflakeGenerator = snowflakeGenerator;
        _logger = logger;
    }

    /// <summary>
    /// 生成单个雪花ID
    /// </summary>
    /// <returns>雪花ID</returns>
    [HttpGet("next")]
    public async Task<IActionResult> NextId()
    {
        try
        {
            var id = await _snowflakeGenerator.NextIdAsync();
            return Ok(new { id = id, stringId = id.ToString() });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate snowflake ID");
            return StatusCode(500, "Failed to generate ID");
        }
    }

    /// <summary>
    /// 批量生成雪花ID
    /// </summary>
    /// <param name="count">生成数量 (1-100)</param>
    /// <returns>雪花ID数组</returns>
    [HttpGet("batch")]
    public IActionResult BatchIds([FromQuery, Range(1, 100)] int count = 10)
    {
        try
        {
            var ids = _snowflakeGenerator.NextIds(count);
            var stringIds = ids.ToStringIds();
            
            return Ok(new 
            { 
                count = ids.Length,
                ids = ids,
                stringIds = stringIds
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate batch snowflake IDs with count: {Count}", count);
            return StatusCode(500, "Failed to generate batch IDs");
        }
    }

    /// <summary>
    /// 解析雪花ID
    /// </summary>
    /// <param name="id">雪花ID</param>
    /// <returns>解析结果</returns>
    [HttpGet("parse/{id}")]
    public IActionResult ParseId(long id)
    {
        try
        {
            var info = _snowflakeGenerator.ParseId(id);
            return Ok(info);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to parse snowflake ID: {Id}", id);
            return BadRequest("Invalid snowflake ID");
        }
    }

    /// <summary>
    /// 解析字符串雪花ID
    /// </summary>
    /// <param name="stringId">字符串雪花ID</param>
    /// <returns>解析结果</returns>
    [HttpGet("parse")]
    public IActionResult ParseStringId([FromQuery, Required] string stringId)
    {
        try
        {
            var id = stringId.FromStringId();
            var info = _snowflakeGenerator.ParseId(id);
            return Ok(info);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to parse string snowflake ID: {StringId}", stringId);
            return BadRequest("Invalid string snowflake ID");
        }
    }

    /// <summary>
    /// 获取生成器信息
    /// </summary>
    /// <returns>生成器信息</returns>
    [HttpGet("info")]
    public IActionResult GetGeneratorInfo()
    {
        try
        {
            var info = _snowflakeGenerator.GetGeneratorInfo();
            return Ok(info);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get generator info");
            return StatusCode(500, "Failed to get generator info");
        }
    }

    /// <summary>
    /// 验证雪花ID是否有效
    /// </summary>
    /// <param name="id">雪花ID</param>
    /// <returns>验证结果</returns>
    [HttpGet("validate/{id}")]
    public IActionResult ValidateId(long id)
    {
        try
        {
            var isValid = id.IsValidSnowflakeId(_snowflakeGenerator);
            var result = new
            {
                id = id,
                isValid = isValid,
                message = isValid ? "Valid snowflake ID" : "Invalid snowflake ID"
            };

            if (isValid)
            {
                var info = _snowflakeGenerator.ParseId(id);
                return Ok(new
                {
                    id = id,
                    isValid = isValid,
                    message = "Valid snowflake ID",
                    generatedAt = info.GeneratedAt,
                    workerId = info.WorkerId,
                    datacenterId = info.DatacenterId
                });
            }

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to validate snowflake ID: {Id}", id);
            return BadRequest("Invalid ID format");
        }
    }

    /// <summary>
    /// 性能测试 - 生成大量ID
    /// </summary>
    /// <param name="count">生成数量 (1-10000)</param>
    /// <returns>性能统计</returns>
    [HttpPost("performance-test")]
    public IActionResult PerformanceTest([FromQuery, Range(1, 10000)] int count = 1000)
    {
        try
        {
            var startTime = DateTime.UtcNow;
            var ids = _snowflakeGenerator.NextIds(count);
            var endTime = DateTime.UtcNow;
            
            var duration = endTime - startTime;
            var idsPerSecond = count / Math.Max(duration.TotalSeconds, 0.001);

            return Ok(new
            {
                count = count,
                durationMs = duration.TotalMilliseconds,
                idsPerSecond = Math.Round(idsPerSecond, 2),
                firstId = ids[0],
                lastId = ids[count - 1],
                testTime = startTime
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Performance test failed with count: {Count}", count);
            return StatusCode(500, "Performance test failed");
        }
    }

    /// <summary>
    /// 使用静态助手生成ID（演示用法）
    /// </summary>
    /// <returns>雪花ID</returns>
    [HttpGet("helper/next")]
    public IActionResult NextIdUsingHelper()
    {
        try
        {
            var id = SnowflakeHelper.NextId();
            var stringId = SnowflakeHelper.NextStringId();
            
            return Ok(new 
            { 
                id = id, 
                stringId = stringId,
                generatedAt = SnowflakeHelper.GetGeneratedTime(id),
                isValid = SnowflakeHelper.IsValidId(id)
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate ID using helper");
            return StatusCode(500, "Failed to generate ID using helper");
        }
    }
}
