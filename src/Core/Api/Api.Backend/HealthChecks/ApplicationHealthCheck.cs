using System;
using System.Collections.Generic;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;

namespace Api.Backend.HealthChecks;

/// <summary>
/// 应用程序基础健康检查
/// </summary>
public class ApplicationHealthCheck : IHealthCheck
{
    private readonly ILogger<ApplicationHealthCheck> _logger;
    private static readonly DateTime _startTime = DateTime.UtcNow;

    public ApplicationHealthCheck(ILogger<ApplicationHealthCheck> logger)
    {
        _logger = logger;
    }

    public Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            var uptime = DateTime.UtcNow - _startTime;
            var version = Assembly.GetExecutingAssembly().GetName().Version?.ToString() ?? "Unknown";

            var data = new Dictionary<string, object>
            {
                ["Version"] = version,
                ["Uptime"] = uptime.ToString(@"dd\.hh\:mm\:ss"),
                ["UptimeSeconds"] = (int)uptime.TotalSeconds,
                ["Environment"] = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Unknown",
                ["MachineName"] = Environment.MachineName,
                ["ProcessorCount"] = Environment.ProcessorCount,
                ["WorkingSet"] = Environment.WorkingSet,
                ["Timestamp"] = DateTime.UtcNow
            };

            _logger.LogInformation("应用程序健康检查通过，运行时间: {Uptime}", uptime.ToString(@"dd\.hh\:mm\:ss"));

            return Task.FromResult(HealthCheckResult.Healthy("应用程序运行正常", data));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "应用程序健康检查异常");
            return Task.FromResult(HealthCheckResult.Unhealthy($"应用程序状态异常: {ex.Message}", ex));
        }
    }
}
