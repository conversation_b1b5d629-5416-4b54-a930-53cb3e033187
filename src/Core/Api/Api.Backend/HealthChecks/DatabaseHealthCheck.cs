using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace Api.Backend.HealthChecks;

/// <summary>
/// 数据库健康检查
/// </summary>
public class DatabaseHealthCheck : IHealthCheck
{
    private readonly ILogger<DatabaseHealthCheck> _logger;
    private readonly ISqlSugarClient _sqlSugarClient;

    public DatabaseHealthCheck(ILogger<DatabaseHealthCheck> logger, ISqlSugarClient sqlSugarClient)
    {
        _logger = logger;
        _sqlSugarClient = sqlSugarClient;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            // 执行简单的数据库查询来检查连接
            var result = await _sqlSugarClient.Ado.GetDataTableAsync("SELECT 1");
            
            if (result != null && result.Rows.Count > 0)
            {
                _logger.LogInformation("数据库健康检查通过");
                return HealthCheckResult.Healthy("数据库连接正常");
            }
            
            _logger.LogWarning("数据库健康检查失败：查询结果为空");
            return HealthCheckResult.Unhealthy("数据库查询结果异常");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "数据库健康检查异常");
            return HealthCheckResult.Unhealthy($"数据库连接失败: {ex.Message}", ex);
        }
    }
}
