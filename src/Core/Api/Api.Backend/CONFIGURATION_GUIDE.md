# 🔧 CYSF 配置指南

## 📋 概述

本文档详细介绍了 CYSF 系统的配置方式，包括数据库连接、缓存配置、JWT 设置、健康检查等各个方面的配置说明。

## 🏗️ 配置文件结构

### 配置文件层次
```
appsettings.json                 # 基础配置模板（包含在版本控制中）
appsettings.Development.json     # 开发环境配置（不包含在版本控制中）
appsettings.Staging.json         # 测试环境配置
appsettings.Production.json      # 生产环境配置
```

### 配置使用原则
1. **appsettings.json** 包含完整的配置模板，开发者复制到对应环境文件进行自定义
2. **appsettings.Development.json** 被 .gitignore 排除，避免开发者本地配置冲突
3. 环境特定配置会覆盖基础配置

## 🚀 开发环境快速设置

### 1. 复制配置文件
```bash
# 在 Api.Backend 项目目录下执行
cd src/Core/Api/Api.Backend
copy appsettings.json appsettings.Development.json
```

### 2. 修改本地配置
编辑 `appsettings.Development.json`，根据你的本地环境修改配置：

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=你的数据库服务器;Database=CYSF_Dev;Trusted_Connection=true;TrustServerCertificate=true;"
  },
  "Hangfire": {
    "ConnectionString": "Server=你的数据库服务器;Database=CYSF_Dev_Hangfire;Trusted_Connection=true;TrustServerCertificate=true;"
  },
  "Cache": {
    "Provider": "Memory"  // 开发环境推荐使用 Memory
  }
}
```

## 🗄️ 数据库配置

### ConnectionStrings 配置
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=CYSF_Dev;Trusted_Connection=true;TrustServerCertificate=true;"
  }
}
```

### Hangfire 数据库配置
```json
{
  "Hangfire": {
    "ConnectionString": "Server=localhost;Database=CYSF_Dev_Hangfire;Trusted_Connection=true;TrustServerCertificate=true;",
    "Enabled": true,
    "DashboardEnabled": true,
    "DashboardPath": "/hangfire",
    "WorkerCount": 5,
    "Queues": ["default", "critical", "background"],
    "MaxRetryAttempts": 3,
    "DeletedJobRetentionDays": 7,
    "SucceededJobRetentionDays": 1,
    "Dashboard": {
      "Username": "admin",
      "Password": "admin123",
      "RequireAuthentication": true
    }
  }
}
```

### Hangfire Dashboard 配置说明
- **Username**: Dashboard登录用户名
- **Password**: Dashboard登录密码
- **RequireAuthentication**: 是否需要认证（建议生产环境设为true）
- **DashboardPath**: Dashboard访问路径，默认为 `/hangfire`
- **DashboardEnabled**: 是否启用Dashboard，可在生产环境设为false

### Hangfire Dashboard 访问
- **开发环境**: http://127.0.0.1:2021/hangfire
- **测试环境**: http://127.0.0.1:6061/hangfire
- **生产环境**: http://127.0.0.1:8081/hangfire

### Hangfire Dashboard 功能
- **任务监控**: 实时查看任务执行状态、成功/失败统计
- **任务管理**: 手动触发、重试、删除任务
- **队列管理**: 监控不同队列（default、critical、background）
- **性能统计**: 查看任务执行时间、吞吐量等指标
- **服务器信息**: 查看Hangfire服务器状态和配置

### 安全建议
- **生产环境**: 建议设置 `DashboardEnabled: false` 或使用强密码
- **网络安全**: 建议通过防火墙限制Dashboard访问
- **密码管理**: 定期更换Dashboard密码
- **监控日志**: 监控Dashboard访问日志
```

## 🚀 缓存配置

### 统一缓存抽象
系统支持 Redis 和 MemoryCache 两种缓存提供程序，可通过配置切换：

```json
{
  "Cache": {
    "Provider": "Memory",  // "Memory" 或 "Redis"
    "RedisConnectionString": "localhost:6379,password=your_password,defaultDatabase=2",
    "DefaultExpirationMinutes": 60,
    "KeyPrefix": "CYSF:Dev:",
    "Enabled": true,
    "MemoryCache": {
      "SizeLimit": 100,
      "CompactionPercentage": 0.25,
      "ExpirationScanFrequency": 60
    }
  }
}
```

### 缓存提供程序说明
- **Memory**: 使用内存缓存，适合开发和单机部署
- **Redis**: 使用 Redis 缓存，适合生产环境和分布式部署

## 🔐 JWT 配置

### JWT 设置
```json
{
  "Jwt": {
    "Secret": "A1B2C3D4E5F6G7H8I9J0K1L2M3N4O5P6Q7R8S9T0U1V2W3X4Y5Z6A7B8C9D0E1F2G3H4I5J6K7L8M9N0",
    "Issuer": "CYSF-API",
    "Audience": "CYSF-Client",
    "TokenExpires": "14400",      // Token 过期时间（秒）
    "RefreshTokenExpires": "18000" // RefreshToken 过期时间（秒）
  }
}
```

### JWT 特性
- **持久化存储**: Token 和 RefreshToken 持久化到数据库
- **简化验证**: 只验证 JWT 有效性，不验证缓存
- **支持刷新**: 通过 RefreshToken 刷新过期的 Token

## ❄️ 雪花ID配置

```json
{
  "Snowflake": {
    "WorkerId": 1,
    "DatacenterId": 1,
    "Epoch": "2025-01-01T00:00:00Z",
    "EnableClockBackwardsCheck": true,
    "MaxClockBackwardsMs": 5
  }
}
```

## 📚 API文档配置

### Swagger 配置
```json
{
  "Swagger": {
    "Title": "创研智慧工厂",
    "Description": "CY-Api",
    "Contact": {
      "Name": "开发团队",
      "Email": "<EMAIL>",
      "Url": "https://example.com"
    }
  }
}
```

## 🌐 CORS 配置

```json
{
  "Cors": {
    "AllowHosts": "http://127.0.0.1:*;https://127.0.0.1:*;http://localhost:*;https://localhost:*;http://192.168.*:*;https://192.168.*:*;"
  }
}
```

## 🚀 Kestrel 服务器配置

### 环境端口分配
- **开发环境 (Development)**: `2021`
- **测试环境 (Staging)**: `6061`
- **生产环境 (Production)**: `8081`

### 端口配置方法

#### 方法一：通过配置文件（推荐）
```json
{
  "Kestrel": {
    "Endpoints": {
      "Http": {
        "Url": "http://127.0.0.1:2021"  // 开发环境
      }
    }
  }
}
```

#### 方法二：通过环境变量
```bash
# 开发环境
export ASPNETCORE_URLS=http://127.0.0.1:2021

# 测试环境
export ASPNETCORE_URLS=http://127.0.0.1:6061

# 生产环境
export ASPNETCORE_URLS=http://0.0.0.0:8081
```

#### 方法三：通过命令行参数
```bash
# 开发环境
dotnet run --urls="http://127.0.0.1:2021"

# 测试环境
dotnet run --urls="http://127.0.0.1:6061" --environment=Staging

# 生产环境
dotnet run --urls="http://0.0.0.0:8081" --environment=Production
```

## 🏥 健康检查配置

### 健康检查端点
- `/health` - 完整健康检查
- `/health/ready` - 就绪检查
- `/health/live` - 存活检查
- `/api/health` - RESTful API 健康检查
- `/api/health/info` - 系统信息

### 健康检查项目
- **Application**: 应用程序健康状态
- **Database**: 数据库连接状态
- **Cache**: 缓存服务状态（Redis/Memory）
- **Hangfire**: 后台任务服务状态

## 🔧 数据库初始化配置

```json
{
  "DatabaseInitialization": {
    "Enabled": true,
    "ForceReinitialize": false,
    "DevelopmentOnly": true,
    "TimeoutSeconds": 30,
    "EnableVerboseLogging": true,
    "SeedDataTypes": [],
    "ExecutionOrder": {}
  }
}
```

## 📝 日志配置

### NLog 配置
日志配置通过 `nlog.config` 文件管理，支持：
- 控制台输出
- 文件输出
- 结构化日志
- 不同级别的日志过滤

### 日志级别配置
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Warning",
      "Hangfire": "Warning",
      "Microsoft": "Warning",
      "DotNetCore": "Warning",
      "Hangfire.Server.ServerHeartbeatProcess": "Error"
    }
  }
}
```

## 🔄 环境特定配置

### 开发环境 (Development)
- 使用 MemoryCache
- 启用详细日志
- 启用 Swagger
- 本地数据库连接

### 测试环境 (Staging)
- 使用 Redis 缓存
- 适中的日志级别
- 启用 Swagger
- 测试数据库连接

### 生产环境 (Production)
- 使用 Redis 缓存
- 最小日志级别
- 禁用 Swagger（可选）
- 生产数据库连接
- 启用健康检查监控

## 🛠️ 配置最佳实践

### 1. 安全配置
- JWT Secret 使用强密码
- 数据库连接字符串不包含明文密码
- 生产环境禁用敏感信息输出

### 2. 性能配置
- 根据环境选择合适的缓存提供程序
- 合理设置缓存过期时间
- 配置适当的 Hangfire 工作线程数

### 3. 监控配置
- 启用健康检查
- 配置适当的日志级别
- 监控关键性能指标

### 4. 开发配置
- 使用 appsettings.Development.json 进行本地配置
- 不要提交个人配置到版本控制
- 保持配置模板的完整性

## 🔍 配置验证

### 检查端口是否正确启动
```bash
# 检查端口占用
netstat -tulpn | grep :2021  # 开发环境
netstat -tulpn | grep :6061  # 测试环境
netstat -tulpn | grep :8081  # 生产环境

# 测试健康检查
curl http://127.0.0.1:2021/health  # 开发环境
curl http://127.0.0.1:6061/health  # 测试环境
curl http://127.0.0.1:8081/health  # 生产环境
```

### 配置优先级
ASP.NET Core 配置的优先级（从高到低）：
1. 命令行参数
2. 环境变量
3. appsettings.{Environment}.json
4. appsettings.json
5. 默认配置

## ⚠️ 注意事项

1. **生产环境绑定地址**：使用 `0.0.0.0` 而不是 `127.0.0.1`
2. **防火墙配置**：确保对应端口在防火墙中开放
3. **负载均衡器**：如果使用负载均衡器，确保健康检查配置正确
4. **SSL 证书**：生产环境建议配置 HTTPS
5. **端口冲突**：确保端口没有被其他服务占用

## 🔧 故障排除

### 端口被占用
```bash
# 查找占用端口的进程
lsof -i :2021
# 或
ss -tulpn | grep :2021

# 终止进程
kill -9 <PID>
```

### 权限问题
```bash
# Linux/Mac 下绑定 1024 以下端口需要 sudo
sudo dotnet run --urls="http://0.0.0.0:80"
```

---

📚 更多信息请参考 [主项目文档](../../../../README.md)
