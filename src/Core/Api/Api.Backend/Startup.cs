using Api.Backend.Extensions;
using Autofac;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace Api.Backend;

/// <summary>
/// 应用程序启动配置类
/// </summary>
public class Startup
{
    private readonly IWebHostEnvironment _webHostEnvironment;
    private IConfiguration Configuration { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    public Startup(IConfiguration configuration, IWebHostEnvironment webHostEnvironment)
    {
        _webHostEnvironment = webHostEnvironment;
        Configuration = configuration;
    }

    /// <summary>
    /// 配置服务容器
    /// </summary>
    public void ConfigureServices(IServiceCollection services)
    {
        // 使用模块化的服务注册
        services.AddApplicationServices(Configuration, _webHostEnvironment);
    }



    /// <summary>
    /// 配置Autofac容器
    /// </summary>
    public void ConfigureContainer(ContainerBuilder builder)
    {
        // 使用模块化的Autofac配置
        builder.ConfigureAutofacContainer();
    }

    /// <summary>
    /// 配置HTTP请求管道
    /// </summary>
    public void Configure(IApplicationBuilder app)
    {
        // 使用模块化的中间件配置
        app.ConfigureApplicationPipeline(Configuration, _webHostEnvironment);
    }


}
