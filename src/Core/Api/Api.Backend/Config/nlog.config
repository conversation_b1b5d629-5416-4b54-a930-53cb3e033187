<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      autoReload="true" internalLogLevel="Info" internalLogFile="${basedir}/Logs/nlog.txt">

    <!-- enable asp.net core layout renderers -->
    <extensions>
        <add assembly="NLog.Web.AspNetCore"/>
    </extensions>

    <!-- the targets to write to -->
    <targets>
        <!-- File Target for all log messages with basic details -->
        <target xsi:type="File" name="debug" fileName="${basedir}/Logs/debug-${shortdate}.log"
                maxArchiveFiles="100"
                archiveAboveSize="10485760"
                maxArchiveDays="30"
                layout="${newline}---------------------------------------${newline}${longdate}|${event-properties:item=EventId_Id:whenEmpty=0}|${uppercase:${level}}|${logger}${newline}${message} ${exception:format=tostring}"/>

        <!-- File Target for own log messages with extra web details using some ASP.NET core renderers -->
        <target xsi:type="File" name="error" fileName="${basedir}/Logs/error-${shortdate}.log"
                maxArchiveFiles="100"
                archiveAboveSize="10485760"
                maxArchiveDays="30"
                layout="${newline}---------------------------------------${newline}${longdate}|${event-properties:item=EventId_Id:whenEmpty=0}|${uppercase:${level}}|${logger}${newline}${message} ${exception:format=tostring}|url: ${aspnet-request-url}|action: ${aspnet-mvc-action}|${callsite}| body: ${aspnet-request-posted-body}"/>

        <!-- File Target for own log messages with extra web details using some ASP.NET core renderers -->
        <target xsi:type="File" name="info" fileName="${basedir}/Logs/info-${shortdate}.log"
                maxArchiveFiles="100"
                archiveAboveSize="10485760"
                maxArchiveDays="30"
                layout="${newline}---------------------------------------${newline}${longdate}|${event-properties:item=EventId_Id:whenEmpty=0}|${uppercase:${level}}|${logger}${newline}${message} ${exception:format=tostring}|url: ${aspnet-request-url}|action: ${aspnet-mvc-action}|${callsite}| body: ${aspnet-request-posted-body}"/>

        <!--Console Target for hosting lifetime messages to improve Docker / Visual Studio startup detection -->
        <target xsi:type="Console" name="console"
                layout="${level:truncate=4:tolower=true}\: ${logger}[0]${newline}      ${message}${exception:format=tostring}"/>
    </targets>

    <!-- rules to map from logger name to target -->
    <rules>
        <logger name="Hangfire.*" minlevel="Error" writeTo="error" final="true"/>
        <logger name="*" minlevel="Error" writeTo="error"/>
        <logger name="CYSF.*" minlevel="Debug" writeTo="debug"/>
        <!-- <logger name="*" minlevel="Info" writeTo="info"/> -->
    </rules>
</nlog>