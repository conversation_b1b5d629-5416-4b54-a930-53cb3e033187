using System.Net;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Authorization.Policy;
using Microsoft.AspNetCore.Http;

namespace Api.Backend.Jwt;

/// <summary>
/// 简化的权限处理器 - 只验证JWT Token有效性，不验证缓存
/// 支持 [AllowAnonymous] 属性跳过登录验证
/// Token持久化仅用于RefreshToken验证，避免MemoryCache重启导致用户下线
/// </summary>
public class PermissionHandler(IAuthenticationSchemeProvider schemes)
    : IAuthorizationMiddlewareResultHandler
{
    private readonly AuthorizationMiddlewareResultHandler _defaultHandler = new();

    /// <summary>
    /// 验证方案提供对象
    /// </summary>
    private IAuthenticationSchemeProvider Schemes { get; } = schemes;

    public async Task HandleAsync(
        RequestDelegate next,
        HttpContext context,
        AuthorizationPolicy policy,
        PolicyAuthorizationResult policyAuthorizationResult)
    {
        // 检查是否标记了 [AllowAnonymous] 属性
        var endpoint = context.GetEndpoint();
        var allowAnonymous = endpoint?.Metadata?.GetMetadata<AllowAnonymousAttribute>() != null;

        // 如果允许匿名访问，直接跳过认证
        if (allowAnonymous)
        {
            await next.Invoke(context);
            return;
        }

        // 检查认证方案
        var defaultAuthenticate = await Schemes.GetDefaultAuthenticateSchemeAsync();
        if (defaultAuthenticate == null)
        {
            context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
            return;
        }

        // 验证JWT Token（只验证JWT本身的有效性，不验证缓存）
        var result = await context.AuthenticateAsync(defaultAuthenticate.Name);
        if (result?.Principal == null || !result.Succeeded)
        {
            context.Response.StatusCode = (int)HttpStatusCode.Unauthorized;
            return;
        }

        // 认证通过，继续处理请求
        await next.Invoke(context);
    }
}