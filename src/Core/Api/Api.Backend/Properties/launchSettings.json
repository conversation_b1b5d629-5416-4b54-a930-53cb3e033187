{"$schema": "http://json.schemastore.org/launchsettings.json", "profiles": {"Development": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "launchUrl": "swagger/index.html", "applicationUrl": "http://127.0.0.1:2021", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "Staging": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": true, "launchUrl": "swagger/index.html", "applicationUrl": "http://127.0.0.1:6061", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Staging"}}, "Production": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "applicationUrl": "http://127.0.0.1:8081", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Production"}}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "swagger/index.html", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}, "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:2021", "sslPort": 2020}}}