<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Api.Backend</name>
    </assembly>
    <members>
        <member name="T:Api.Backend.Controllers.CommonController">
            <summary>
                公共Api
            </summary>
        </member>
        <member name="M:Api.Backend.Controllers.CommonController.#ctor(CYSF.Application.CommonApp)">
            <summary>
                公共Api
            </summary>
        </member>
        <member name="M:Api.Backend.Controllers.CommonController.GetEnumList(System.String)">
            <summary>
                获取一个或者多个枚举键值对
                多个使用英文逗号分隔
            </summary>
            <param name="enums">枚举类型名称</param>
            <returns></returns>
        </member>
        <member name="T:Api.Backend.Controllers.HealthController">
            <summary>
            健康检查控制器
            </summary>
        </member>
        <member name="M:Api.Backend.Controllers.HealthController.Get">
            <summary>
            获取系统健康状态
            </summary>
            <returns>系统健康状态信息</returns>
        </member>
        <member name="M:Api.Backend.Controllers.HealthController.Ready">
            <summary>
            获取就绪状态检查
            </summary>
            <returns>就绪状态信息</returns>
        </member>
        <member name="M:Api.Backend.Controllers.HealthController.Live">
            <summary>
            获取存活状态检查
            </summary>
            <returns>存活状态信息</returns>
        </member>
        <member name="M:Api.Backend.Controllers.HealthController.Info">
            <summary>
            获取系统信息
            </summary>
            <returns>系统基本信息</returns>
        </member>
        <member name="T:Api.Backend.Controllers.SnowflakeController">
            <summary>
            雪花ID生成器控制器
            </summary>
        </member>
        <member name="M:Api.Backend.Controllers.SnowflakeController.NextId">
            <summary>
            生成单个雪花ID
            </summary>
            <returns>雪花ID</returns>
        </member>
        <member name="M:Api.Backend.Controllers.SnowflakeController.BatchIds(System.Int32)">
            <summary>
            批量生成雪花ID
            </summary>
            <param name="count">生成数量 (1-100)</param>
            <returns>雪花ID数组</returns>
        </member>
        <member name="M:Api.Backend.Controllers.SnowflakeController.ParseId(System.Int64)">
            <summary>
            解析雪花ID
            </summary>
            <param name="id">雪花ID</param>
            <returns>解析结果</returns>
        </member>
        <member name="M:Api.Backend.Controllers.SnowflakeController.ParseStringId(System.String)">
            <summary>
            解析字符串雪花ID
            </summary>
            <param name="stringId">字符串雪花ID</param>
            <returns>解析结果</returns>
        </member>
        <member name="M:Api.Backend.Controllers.SnowflakeController.GetGeneratorInfo">
            <summary>
            获取生成器信息
            </summary>
            <returns>生成器信息</returns>
        </member>
        <member name="M:Api.Backend.Controllers.SnowflakeController.ValidateId(System.Int64)">
            <summary>
            验证雪花ID是否有效
            </summary>
            <param name="id">雪花ID</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:Api.Backend.Controllers.SnowflakeController.PerformanceTest(System.Int32)">
            <summary>
            性能测试 - 生成大量ID
            </summary>
            <param name="count">生成数量 (1-10000)</param>
            <returns>性能统计</returns>
        </member>
        <member name="M:Api.Backend.Controllers.SnowflakeController.NextIdUsingHelper">
            <summary>
            使用静态助手生成ID（演示用法）
            </summary>
            <returns>雪花ID</returns>
        </member>
        <member name="T:Api.Backend.Controllers.TokenController">
            <summary>
                平台/租户用户登录
            </summary>
        </member>
        <member name="M:Api.Backend.Controllers.TokenController.Get(CYSF.Models.Request.Auth.LoginReq)">
            <summary>
                获取Token
            </summary>
            <param name="req">请求实体</param>
            <returns></returns>
        </member>
        <member name="M:Api.Backend.Controllers.TokenController.Refresh(CYSF.Models.Request.Auth.RefreshTokenReq)">
            <summary>
                刷新Token
            </summary>
            <returns></returns>
        </member>
        <member name="M:Api.Backend.Controllers.TokenController.Delete(CYSF.Models.Request.Auth.DelTokenReq)">
            <summary>
                删除Token
            </summary>
            <returns></returns>
        </member>
        <member name="T:Api.Backend.Extensions.ApiServiceExtensions">
            <summary>
            API相关服务注册扩展
            </summary>
        </member>
        <member name="M:Api.Backend.Extensions.ApiServiceExtensions.AddApiServices(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            添加API相关服务
            </summary>
        </member>
        <member name="M:Api.Backend.Extensions.ApiServiceExtensions.AddCorsServices(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            添加CORS服务
            </summary>
        </member>
        <member name="M:Api.Backend.Extensions.ApiServiceExtensions.AddValidationServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            添加验证服务
            </summary>
        </member>
        <member name="T:Api.Backend.Extensions.ApplicationBuilderExtensions">
            <summary>
            应用程序构建器扩展
            </summary>
        </member>
        <member name="M:Api.Backend.Extensions.ApplicationBuilderExtensions.ConfigureApplicationPipeline(Microsoft.AspNetCore.Builder.IApplicationBuilder,Microsoft.Extensions.Configuration.IConfiguration,Microsoft.AspNetCore.Hosting.IWebHostEnvironment)">
            <summary>
            配置应用程序中间件管道
            </summary>
        </member>
        <member name="M:Api.Backend.Extensions.ApplicationBuilderExtensions.InitializeDependencies(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            初始化依赖项
            </summary>
        </member>
        <member name="M:Api.Backend.Extensions.ApplicationBuilderExtensions.ConfigureEnvironmentSpecific(Microsoft.AspNetCore.Builder.IApplicationBuilder,Microsoft.AspNetCore.Hosting.IWebHostEnvironment)">
            <summary>
            配置环境特定设置
            </summary>
        </member>
        <member name="M:Api.Backend.Extensions.ApplicationBuilderExtensions.InitializeApplication(Microsoft.AspNetCore.Builder.IApplicationBuilder,Microsoft.AspNetCore.Hosting.IWebHostEnvironment)">
            <summary>
            初始化应用程序
            </summary>
        </member>
        <member name="M:Api.Backend.Extensions.ApplicationBuilderExtensions.ConfigureProxyHeaders(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            配置代理头（解决 Nginx 代理不能获取IP问题）
            </summary>
        </member>
        <member name="M:Api.Backend.Extensions.ApplicationBuilderExtensions.ConfigureRoutingAndAuth(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            配置路由和认证
            </summary>
        </member>
        <member name="M:Api.Backend.Extensions.ApplicationBuilderExtensions.ConfigureDocumentation(Microsoft.AspNetCore.Builder.IApplicationBuilder,Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            配置文档
            </summary>
        </member>
        <member name="M:Api.Backend.Extensions.ApplicationBuilderExtensions.ConfigureHangfireDashboard(Microsoft.AspNetCore.Builder.IApplicationBuilder,Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            配置Hangfire Dashboard
            </summary>
        </member>
        <member name="M:Api.Backend.Extensions.ApplicationBuilderExtensions.ConfigureEndpoints(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            配置端点
            </summary>
        </member>
        <member name="T:Api.Backend.Extensions.AuthenticationServiceExtensions">
            <summary>
            认证授权服务注册扩展
            </summary>
        </member>
        <member name="M:Api.Backend.Extensions.AuthenticationServiceExtensions.AddAuthenticationServices(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            添加认证授权服务
            </summary>
        </member>
        <member name="T:Api.Backend.Extensions.AutofacExtensions">
            <summary>
            Autofac依赖注入扩展
            </summary>
        </member>
        <member name="M:Api.Backend.Extensions.AutofacExtensions.ConfigureAutofacContainer(Autofac.ContainerBuilder)">
            <summary>
            配置Autofac容器
            </summary>
        </member>
        <member name="M:Api.Backend.Extensions.AutofacExtensions.RegisterApplicationServices(Autofac.ContainerBuilder)">
            <summary>
            注册Application层服务
            </summary>
        </member>
        <member name="M:Api.Backend.Extensions.AutofacExtensions.RegisterBusinessServices(Autofac.ContainerBuilder)">
            <summary>
            注册Service层服务
            </summary>
        </member>
        <member name="M:Api.Backend.Extensions.AutofacExtensions.RegisterRepositoryServices(Autofac.ContainerBuilder)">
            <summary>
            注册Repository层服务
            </summary>
        </member>
        <member name="T:Api.Backend.Extensions.BackgroundServiceExtensions">
            <summary>
            后台任务服务注册扩展
            </summary>
        </member>
        <member name="M:Api.Backend.Extensions.BackgroundServiceExtensions.AddBackgroundServices(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            添加后台任务服务
            </summary>
        </member>
        <member name="T:Api.Backend.Extensions.BaseServiceExtensions">
            <summary>
            基础服务注册扩展
            </summary>
        </member>
        <member name="M:Api.Backend.Extensions.BaseServiceExtensions.AddBaseServices(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            添加基础服务
            </summary>
        </member>
        <member name="T:Api.Backend.Extensions.DatabaseServiceExtensions">
            <summary>
            数据库服务注册扩展
            </summary>
        </member>
        <member name="M:Api.Backend.Extensions.DatabaseServiceExtensions.AddDatabaseServices(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.Extensions.Configuration.IConfiguration,Microsoft.AspNetCore.Hosting.IWebHostEnvironment)">
            <summary>
            添加数据库服务
            </summary>
        </member>
        <member name="T:Api.Backend.Extensions.DocumentationServiceExtensions">
            <summary>
            文档服务注册扩展
            </summary>
        </member>
        <member name="M:Api.Backend.Extensions.DocumentationServiceExtensions.AddDocumentationServices(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            添加文档服务
            </summary>
        </member>
        <member name="T:Api.Backend.Extensions.HealthCheckServiceExtensions">
            <summary>
            健康检查服务注册扩展
            </summary>
        </member>
        <member name="M:Api.Backend.Extensions.HealthCheckServiceExtensions.AddHealthCheckServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            添加健康检查服务
            </summary>
        </member>
        <member name="T:Api.Backend.Extensions.ServiceCollectionExtensions">
            <summary>
            服务注册扩展方法集合
            </summary>
        </member>
        <member name="M:Api.Backend.Extensions.ServiceCollectionExtensions.AddApplicationServices(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.Extensions.Configuration.IConfiguration,Microsoft.AspNetCore.Hosting.IWebHostEnvironment)">
            <summary>
            添加所有应用服务
            </summary>
        </member>
        <member name="T:Api.Backend.Filters.ApiExceptionFilter">
            <summary>
                Api 异常拦截返回标准结果模型
            </summary>
        </member>
        <member name="T:Api.Backend.Filters.HangfireDashboardAuthorizationFilter">
            <summary>
            Hangfire Dashboard 认证过滤器
            </summary>
        </member>
        <member name="T:Api.Backend.Filters.ValidationFilter">
            <summary>
            最终优化的验证过滤器
            完全遵循ApiResult返回格式，支持快速失败和详细错误信息
            </summary>
        </member>
        <member name="M:Api.Backend.Filters.ValidationFilter.ProcessValidationErrors(Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext)">
            <summary>
            处理验证错误
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Api.Backend.Filters.ValidationFilter.LogValidationFailure(Microsoft.AspNetCore.Mvc.Filters.ActionExecutingContext,CYSF.Core.Models.ValidationResult)">
            <summary>
            记录验证失败日志
            </summary>
            <param name="context"></param>
            <param name="validationResult"></param>
        </member>
        <member name="M:Api.Backend.Filters.ValidationFilter.CreateValidationErrorData(CYSF.Core.Models.ValidationResult)">
            <summary>
            创建验证错误数据
            根据配置决定是否返回详细错误信息
            </summary>
            <param name="validationResult"></param>
            <returns></returns>
        </member>
        <member name="T:Api.Backend.HealthChecks.ApplicationHealthCheck">
            <summary>
            应用程序基础健康检查
            </summary>
        </member>
        <member name="T:Api.Backend.HealthChecks.CacheHealthCheck">
            <summary>
            缓存健康检查（支持 Redis 和 MemoryCache）
            </summary>
        </member>
        <member name="T:Api.Backend.HealthChecks.DatabaseHealthCheck">
            <summary>
            数据库健康检查
            </summary>
        </member>
        <member name="T:Api.Backend.Jwt.PermissionHandler">
            <summary>
            简化的权限处理器 - 只验证JWT Token有效性，不验证缓存
            支持 [AllowAnonymous] 属性跳过登录验证
            Token持久化仅用于RefreshToken验证，避免MemoryCache重启导致用户下线
            </summary>
        </member>
        <member name="M:Api.Backend.Jwt.PermissionHandler.#ctor(Microsoft.AspNetCore.Authentication.IAuthenticationSchemeProvider)">
            <summary>
            简化的权限处理器 - 只验证JWT Token有效性，不验证缓存
            支持 [AllowAnonymous] 属性跳过登录验证
            Token持久化仅用于RefreshToken验证，避免MemoryCache重启导致用户下线
            </summary>
        </member>
        <member name="P:Api.Backend.Jwt.PermissionHandler.Schemes">
            <summary>
            验证方案提供对象
            </summary>
        </member>
        <member name="T:Api.Backend.Startup">
            <summary>
            应用程序启动配置类
            </summary>
        </member>
        <member name="M:Api.Backend.Startup.#ctor(Microsoft.Extensions.Configuration.IConfiguration,Microsoft.AspNetCore.Hosting.IWebHostEnvironment)">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:Api.Backend.Startup.ConfigureServices(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            配置服务容器
            </summary>
        </member>
        <member name="M:Api.Backend.Startup.ConfigureContainer(Autofac.ContainerBuilder)">
            <summary>
            配置Autofac容器
            </summary>
        </member>
        <member name="M:Api.Backend.Startup.Configure(Microsoft.AspNetCore.Builder.IApplicationBuilder)">
            <summary>
            配置HTTP请求管道
            </summary>
        </member>
        <member name="T:Api.Backend.Swagger.SwaggerEnumFilter">
            <summary>
                Swagger枚举处理过滤器
            </summary>
        </member>
    </members>
</doc>
