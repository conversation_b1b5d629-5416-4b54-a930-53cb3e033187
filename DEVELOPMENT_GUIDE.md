# 🚀 CYSF 开发入门指南

## 📋 概述

本指南将帮助新开发者快速了解 CYSF 项目的架构、开发流程和最佳实践。

## 🏗️ 项目架构

CYSF 采用 DDD（领域驱动设计）分层架构：
- **Api.Backend** - Web API 接口层
- **Application** - 应用服务层
- **Services** - 业务服务层
- **Repositories** - 数据访问层
- **Models** - 数据模型层
- **Infrastructure** - 核心基础设施层

## 🛠️ 开发环境搭建

### 1. 环境要求
- **.NET 8.0 SDK**
- **Visual Studio 2022** 或 **JetBrains Rider**
- **SQL Server 2019+** 或 **MySQL 8.0+**
- **Redis 6.0+** (可选，可使用 MemoryCache)

### 2. 项目克隆和配置
```bash
# 克隆项目
git clone [repository-url]
cd CYSF

# 复制配置文件
cd src/Core/Api/Api.Backend
copy appsettings.json appsettings.Development.json
```

### 3. 配置数据库
编辑 `appsettings.Development.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=CYSF_Dev;Trusted_Connection=true;TrustServerCertificate=true;"
  },
  "Hangfire": {
    "ConnectionString": "Server=localhost;Database=CYSF_Dev_Hangfire;Trusted_Connection=true;TrustServerCertificate=true;",
    "Enabled": true
  }
}
```

### 4. 配置缓存
```json
{
  "Cache": {
    "Provider": "Memory",  // 开发环境推荐使用 Memory
    "Enabled": true
  }
}
```

### 5. 运行项目
```bash
# 生成数据库表结构
cd src/Core/Infrastructure/CYSF.Generate
dotnet run

# 启动 API 项目
cd ../../../Api/Api.Backend
dotnet run
```

### 6. 访问应用
- **API 文档**: http://127.0.0.1:2021/swagger
- **健康检查**: http://127.0.0.1:2021/health
- **Hangfire 仪表板**: http://127.0.0.1:2021/hangfire
  - 用户名: `admin`
  - 密码: `admin123`

## 🔧 开发流程

### 1. 新增业务功能
1. **定义实体** (CYSF.Models)
2. **生成代码** (CYSF.Generate)
3. **实现业务逻辑** (CYSF.Services)
4. **实现应用服务** (CYSF.Application)
5. **创建控制器** (Api.Backend)

### 2. 代码生成器使用
```bash
cd src/Core/Infrastructure/CYSF.Generate
dotnet run
```

生成器会自动创建：
- 实体类 (Models)
- 仓储类 (Repositories)
- 服务类 (Services)
- 应用服务类 (Application)

### 3. 依赖注入
项目使用 Autofac 进行依赖注入，采用模块化注册：
- **Application 层**: 自动注册以 `App` 结尾的类
- **Service 层**: 自动注册所有服务类
- **Repository 层**: 自动注册仓储类和接口

## 📚 开发要点

### 1. 缓存使用
```csharp
// 使用缓存助手
public async Task<Tenant> GetTenantAsync(int id)
{
    return await _cacheHelper.CacheShellAsync(
        CacheKey.Tenant(id),
        CacheKey.DayExpired,
        () => _tenantService.GetAsync(id)
    );
}
```

### 2. 异常处理
```csharp
// 抛出业务异常
throw new ApiException("租户编号已存在");
```

### 3. 健康检查端点
- `/health` - 完整健康检查
- `/api/health` - RESTful API 健康检查

## 🎯 最佳实践

### 1. 配置管理
- 使用 `appsettings.json` 作为配置模板
- 本地配置使用 `appsettings.Development.json`
- 不要提交个人配置到版本控制

### 2. 缓存使用
- 查询操作使用缓存
- 更新/删除操作清除相关缓存
- 使用合适的缓存过期时间

### 3. 数据库操作
- 使用 SqlSugarScope 确保线程安全
- 复杂操作使用事务
- 避免在循环中执行数据库操作

### 4. API 设计
- 遵循 RESTful 设计原则
- 使用统一的响应格式 (ApiResult)
- 提供清晰的 API 文档

### 5. 日志记录
- 记录关键业务操作
- 记录异常信息
- 避免记录敏感数据

## 🧪 测试

### 1. 单元测试
```bash
dotnet test
```

### 2. API 测试
使用 Swagger UI 进行接口测试：
http://127.0.0.1:2021/swagger

### 3. 健康检查测试
```bash
# 测试健康检查
curl http://127.0.0.1:2021/health

# 测试系统信息
curl http://127.0.0.1:2021/api/health/info
```

## 🔍 常见问题

### 1. 数据库连接问题
- 检查连接字符串配置
- 确认数据库服务是否启动
- 检查防火墙设置

### 2. 缓存问题
- 开发环境推荐使用 MemoryCache
- Redis 连接问题检查连接字符串和服务状态

### 3. JWT 问题
- 检查 JWT 配置中的 Secret
- 确认 Token 是否过期
- 检查权限配置

### 4. 代码生成问题
- 确认数据库连接正常
- 检查表结构是否符合规范
- 查看生成器日志输出

---

🚀 开始你的 CYSF 开发之旅吧！如有问题，请查阅 [配置指南](src/Core/Api/Api.Backend/CONFIGURATION_GUIDE.md) 或相关架构文档。
