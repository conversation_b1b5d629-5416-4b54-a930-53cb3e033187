# Git属性配置文件

# 自动检测文本文件并执行LF规范化
* text=auto

# 明确声明您希望始终进行规范化并在签出时转换为本机行尾的文件
*.cs text diff=csharp
*.csproj text
*.sln text
*.config text
*.json text
*.xml text
*.md text
*.txt text
*.yml text
*.yaml text

# 声明始终具有CRLF行尾的文件
*.bat text eol=crlf
*.cmd text eol=crlf

# 声明始终具有LF行尾的文件
*.sh text eol=lf

# 表示二进制文件（不进行文本处理）
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.pdf binary
*.zip binary
*.7z binary
*.rar binary
*.dll binary
*.exe binary
*.pdb binary

# 字体文件
*.woff binary
*.woff2 binary
*.eot binary
*.ttf binary
*.otf binary

# 音视频文件
*.mp3 binary
*.mp4 binary
*.avi binary
*.mov binary
*.wav binary

# Office文档
*.doc binary
*.docx binary
*.xls binary
*.xlsx binary
*.ppt binary
*.pptx binary

# 归档文件
*.tar binary
*.gz binary
*.bz2 binary

# 数据库文件
*.db binary
*.sqlite binary
*.sqlite3 binary

# 证书文件
*.pfx binary
*.p12 binary
*.cer binary
*.crt binary
*.key binary

# Visual Studio 文件
*.suo binary
*.user binary
*.userosscache binary
*.sln.docstates binary

# 包文件
*.nupkg binary
*.snupkg binary

# 语言特定的Git属性

# C# 项目文件
*.csproj linguist-language=XML
*.vbproj linguist-language=XML
*.fsproj linguist-language=XML
*.dbproj linguist-language=XML

# 解决方案文件
*.sln linguist-language=Text

# 配置文件
*.config linguist-language=XML
web.config linguist-language=XML
app.config linguist-language=XML

# JSON 文件
*.json linguist-language=JSON
appsettings*.json linguist-language=JSON

# XML 文件
*.xml linguist-language=XML
*.xaml linguist-language=XML
*.resx linguist-language=XML

# 脚本文件
*.ps1 linguist-language=PowerShell
*.bat linguist-language=Batchfile
*.cmd linguist-language=Batchfile
*.sh linguist-language=Shell

# 文档文件
*.md linguist-language=Markdown
*.rst linguist-language=reStructuredText

# 忽略生成的文件在语言统计中
bin/ linguist-generated=true
obj/ linguist-generated=true
packages/ linguist-generated=true
*.designer.cs linguist-generated=true
*.Designer.cs linguist-generated=true
*.generated.cs linguist-generated=true
*.g.cs linguist-generated=true
*.g.i.cs linguist-generated=true
AssemblyInfo.cs linguist-generated=true
GlobalAssemblyInfo.cs linguist-generated=true
TemporaryGeneratedFile_*.cs linguist-generated=true

# 第三方库文件
lib/ linguist-vendored=true
libs/ linguist-vendored=true
vendor/ linguist-vendored=true
third-party/ linguist-vendored=true
external/ linguist-vendored=true

# 测试文件
*.Tests/ linguist-documentation=true
*.Test/ linguist-documentation=true
test/ linguist-documentation=true
tests/ linguist-documentation=true

# 文档文件
docs/ linguist-documentation=true
documentation/ linguist-documentation=true
*.md linguist-documentation=true

# 示例和演示文件
examples/ linguist-documentation=true
samples/ linguist-documentation=true
demo/ linguist-documentation=true
demos/ linguist-documentation=true

# 工具和脚本
tools/ linguist-documentation=true
scripts/ linguist-documentation=true
build/ linguist-documentation=true

# 配置文件
*.config linguist-language=XML
*.settings linguist-language=XML

# NuGet 包配置
packages.config linguist-language=XML
*.nuspec linguist-language=XML

# MSBuild 文件
*.targets linguist-language=XML
*.props linguist-language=XML
Directory.Build.props linguist-language=XML
Directory.Build.targets linguist-language=XML

# EditorConfig
.editorconfig linguist-language=INI

# Git 配置
.gitignore linguist-language=Text
.gitattributes linguist-language=Text

# Docker 文件
Dockerfile linguist-language=Dockerfile
*.dockerfile linguist-language=Dockerfile
docker-compose*.yml linguist-language=YAML

# CI/CD 配置
*.yml linguist-language=YAML
*.yaml linguist-language=YAML
.github/ linguist-documentation=true
azure-pipelines.yml linguist-language=YAML

# 许可证文件
LICENSE linguist-language=Text
LICENSE.txt linguist-language=Text
COPYING linguist-language=Text

# 变更日志
CHANGELOG* linguist-documentation=true
HISTORY* linguist-documentation=true
RELEASES* linguist-documentation=true
