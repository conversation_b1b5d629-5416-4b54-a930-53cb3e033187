# 🏢 CYSF 企业级智慧工厂管理系统 - 培训完整指南

##  1. <a name=''></a>📋 培训对象与目标

**培训对象：** 具备基础编程知识但缺乏可能企业级开发经验的程序员

**培训目标：**
- 🎯 理解企业级系统架构设计思想
- 🛠️ 掌握现代化开发工具和技术栈
- 💻 具备独立开发业务功能的能力
- 📚 建立正确的技术选型和最佳实践认知

**培训时长：** 2天 (16小时)
- 第一天：基础知识 + 框架概述 + 环境搭建
- 第二天：深入特性 + 实战开发 + 部署运维

---

##  2. <a name='2'></a>🎓 第一部分：基础概念与框架认知 (2小时)

###  2.1. <a name='-1'></a>1.1 什么是框架？为什么需要框架？ (45分钟)

**框架的定义：**
框架是一套预先设计好的代码结构和规范，提供了应用程序的基础架构，开发者在此基础上填充业务逻辑。

**无框架开发 vs 简易框架 vs 企业级框架对比：**

| 开发方式 | 无框架开发 | 简易框架 | 企业级框架(CYSF) |
|----------|------------|----------|------------------|
| **代码结构** | 随意放置，无规范 | 简单分层 | 严格DDD分层架构 |
| **数据访问** | 原生SQL，手写连接 | 简单ORM | SqlSugar + 仓储模式 |
| **依赖管理** | 手动new对象 | 简单注入 | Autofac容器管理 |
| **异常处理** | try-catch到处都是 | 简单统一处理 | 全局异常中间件 |
| **日志记录** | Console.WriteLine | 简单文件日志 | 结构化日志系统 |
| **缓存策略** | 无缓存或简单缓存 | 基础缓存 | 多级缓存抽象 |
| **API文档** | 手写文档 | 简单注释 | Swagger自动生成 |
| **部署方式** | 手动复制文件 | 简单脚本 | 容器化自动部署 |
| **团队协作** | 各写各的 | 基本规范 | 统一标准和工具 |

**无框架开发的问题：**
```csharp
// 无框架开发示例 - 混乱的代码结构
public class ProductController
{
    public string CreateProduct(string name, decimal price)
    {
        try
        {
            // 数据库连接硬编码
            var connectionString = "Server=localhost;Database=Test;...";
            using var connection = new SqlConnection(connectionString);
            connection.Open();

            // SQL注入风险
            var sql = $"INSERT INTO Products (Name, Price) VALUES ('{name}', {price})";
            var command = new SqlCommand(sql, connection);
            command.ExecuteNonQuery();

            // 返回格式不统一
            return "创建成功";
        }
        catch (Exception ex)
        {
            // 异常处理不统一
            Console.WriteLine(ex.Message);
            return "创建失败";
        }
    }
}

// 问题：
// ❌ 数据库连接硬编码，难以维护
// ❌ SQL注入安全风险
// ❌ 异常处理不统一
// ❌ 返回格式不规范
// ❌ 没有日志记录
// ❌ 无法单元测试
// ❌ 代码重复，难以复用
```

**企业级框架的优势：**
```csharp
// CYSF框架开发示例 - 规范的代码结构
[Route("api/[controller]/[action]")]
[ApiController]
[Authorize]
public class ProductController : ControllerBase
{
    private readonly ProductApp _productApp;

    public ProductController(ProductApp productApp) // 依赖注入
    {
        _productApp = productApp;
    }

    /// <summary>
    /// 创建产品
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> Create([FromBody] CreateProductReq req)
    {
        // FluentValidation自动验证
        // 全局异常处理
        // 统一返回格式
        var result = await _productApp.CreateAsync(req);
        return Ok(result);
    }
}

// 优势：
// ✅ 依赖注入，松耦合
// ✅ 自动参数验证
// ✅ 全局异常处理
// ✅ 统一返回格式
// ✅ 自动生成API文档
// ✅ 结构化日志记录
// ✅ 易于单元测试
// ✅ 代码复用性高
```

**为什么框架要考虑更多的设计和冗余？**

**核心原则：不能因为以前没有发生过，就代表不应该考虑！**

1. **墨菲定律在软件开发中的体现：**
   ```
   "凡是可能出错的事情，就一定会出错"

   常见的错误思维：
   ❌ "我们系统用户不多，不需要考虑并发"
   ❌ "数据库从来没有连接失败过，不需要重试机制"
   ❌ "内存一直够用，不需要考虑内存泄漏"
   ❌ "网络很稳定，不需要超时处理"

   现实情况：
   ✅ 用户量突然增长时系统崩溃
   ✅ 数据库维护时应用无法启动
   ✅ 长时间运行后内存溢出
   ✅ 网络抖动导致请求超时
   ```

2. **预防性设计的重要性：**
   ```csharp
   // 错误示例：没有考虑异常情况
   public class SimpleProductService
   {
       public Product GetProduct(int id)
       {
           var connection = new SqlConnection("...");
           connection.Open(); // 如果数据库不可用怎么办？

           var command = new SqlCommand($"SELECT * FROM Products WHERE Id = {id}", connection);
           var reader = command.ExecuteReader(); // SQL注入风险

           // 如果没有数据怎么办？
           reader.Read();
           return new Product
           {
               Id = reader.GetInt32("Id"),
               Name = reader.GetString("Name") // 如果Name是NULL怎么办？
           };
       }
   }

   // 正确示例：考虑各种异常情况
   public class RobustProductService
   {
       private readonly IDbConnectionFactory _connectionFactory;
       private readonly ILogger<RobustProductService> _logger;
       private readonly ICircuitBreaker _circuitBreaker;

       public async Task<Product> GetProductAsync(int id)
       {
           return await _circuitBreaker.ExecuteAsync(async () =>
           {
               using var connection = await _connectionFactory.CreateConnectionAsync();

               var product = await connection.QueryFirstOrDefaultAsync<Product>(
                   "SELECT Id, Name, Price FROM Products WHERE Id = @Id",
                   new { Id = id });

               if (product == null)
               {
                   _logger.LogWarning("Product not found: {ProductId}", id);
                   throw new NotFoundException($"Product {id} not found");
               }

               return product;
           });
       }
   }
   ```

3. **可扩展性考虑：**
   - **接口抽象**：今天用MySQL，明天可能要支持Oracle
   - **配置化设计**：硬编码的参数将来都可能需要调整
   - **插件化架构**：新的业务需求可能需要扩展功能

4. **稳定性保障：**
   - **熔断机制**：防止雪崩效应
   - **重试策略**：处理临时性故障
   - **资源管理**：防止内存泄漏和连接泄漏
   - **优雅降级**：核心功能保持可用

5. **团队协作需要：**
   - **统一标准**：避免每个人都有自己的写法
   - **错误处理**：统一的异常处理和日志记录
   - **代码审查**：可预测的代码结构

6. **维护性要求：**
   - **可观测性**：完善的日志和监控
   - **可调试性**：清晰的错误信息和堆栈跟踪
   - **可测试性**：依赖注入支持单元测试

**框架设计原则：**
```
1. 单一职责原则 (SRP)
   - 每个类只负责一个功能
   - 职责分离，便于维护

2. 开闭原则 (OCP)
   - 对扩展开放，对修改关闭
   - 通过接口和抽象实现扩展

3. 依赖倒置原则 (DIP)
   - 依赖抽象，不依赖具体实现
   - 通过依赖注入实现解耦

4. 接口隔离原则 (ISP)
   - 接口功能单一，避免臃肿
   - 客户端不依赖不需要的接口

5. 里氏替换原则 (LSP)
   - 子类可以替换父类
   - 保证多态性的正确使用
```

###  2.2. <a name='FluentValidation30'></a>1.2 FluentValidation：自动验证机制 (30分钟)

**传统验证 vs FluentValidation：**

```csharp
// 传统手动验证 - 繁琐易错
public async Task<IActionResult> CreateProduct(CreateProductReq req)
{
    // 手动验证每个字段
    if (string.IsNullOrEmpty(req.Name))
        return BadRequest("产品名称不能为空");

    if (req.Name.Length > 100)
        return BadRequest("产品名称长度不能超过100个字符");

    if (req.Price <= 0)
        return BadRequest("产品价格必须大于0");

    if (string.IsNullOrEmpty(req.Code))
        return BadRequest("产品编码不能为空");

    if (!Regex.IsMatch(req.Code, "^[A-Z0-9]+$"))
        return BadRequest("产品编码只能包含大写字母和数字");

    // 业务逻辑...
    var result = await _productService.CreateAsync(req);
    return Ok(result);
}

// 问题：
// ❌ 验证逻辑与业务逻辑混合
// ❌ 代码重复，每个接口都要写
// ❌ 错误信息不统一
// ❌ 难以维护和扩展
```

**FluentValidation自动验证：**
```csharp
// 1. 定义验证器（只需要定义一次）
public class CreateProductReqValidator : AbstractValidator<CreateProductReq>
{
    public CreateProductReqValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("产品名称不能为空")
            .MaximumLength(100).WithMessage("产品名称长度不能超过100个字符");

        RuleFor(x => x.Code)
            .NotEmpty().WithMessage("产品编码不能为空")
            .Length(3, 50).WithMessage("产品编码长度必须在3-50个字符之间")
            .Matches("^[A-Z0-9]+$").WithMessage("产品编码只能包含大写字母和数字");

        RuleFor(x => x.Price)
            .GreaterThan(0).WithMessage("产品价格必须大于0")
            .LessThanOrEqualTo(999999.99m).WithMessage("产品价格不能超过999999.99");

        RuleFor(x => x.CategoryId)
            .GreaterThan(0).WithMessage("请选择产品分类");
    }
}

// 2. 控制器代码简洁（框架自动验证）
[HttpPost]
public async Task<IActionResult> CreateProduct([FromBody] CreateProductReq req)
{
    // FluentValidation自动验证，验证失败自动返回400错误
    // 无需手动验证代码！

    var result = await _productApp.CreateAsync(req);
    return Ok(result);
}

// 优势：
// ✅ 验证逻辑与业务逻辑分离
// ✅ 验证器可复用
// ✅ 错误信息统一格式
// ✅ 支持复杂验证规则
// ✅ 易于单元测试
```

**FluentValidation高级用法：**
```csharp
public class CreateProductReqValidator : AbstractValidator<CreateProductReq>
{
    private readonly ProductRepository _productRepository;

    public CreateProductReqValidator(ProductRepository productRepository)
    {
        _productRepository = productRepository;

        // 基础验证
        RuleFor(x => x.Code)
            .NotEmpty().WithMessage("产品编码不能为空")
            .MustAsync(BeUniqueCode).WithMessage("产品编码已存在"); // 异步验证

        // 条件验证
        RuleFor(x => x.Description)
            .NotEmpty().WithMessage("高价产品必须填写描述")
            .When(x => x.Price > 1000); // 当价格大于1000时才验证描述

        // 自定义验证
        RuleFor(x => x)
            .Must(HaveValidPriceRange).WithMessage("价格范围不合理")
            .WithName("价格验证");
    }

    // 异步验证方法
    private async Task<bool> BeUniqueCode(string code, CancellationToken cancellationToken)
    {
        return !await _productRepository.ExistsByCodeAsync(code);
    }

    // 自定义验证方法
    private bool HaveValidPriceRange(CreateProductReq req)
    {
        // 自定义业务规则验证
        return req.Price >= req.MinPrice && req.Price <= req.MaxPrice;
    }
}
```

###  2.3. <a name='Mapster30'></a>1.3 Mapster：高性能对象映射 (30分钟)

**手动映射 vs Mapster：**

```csharp
// 手动映射 - 繁琐易错
public ProductDto ConvertToDto(Product product)
{
    return new ProductDto
    {
        Id = product.Id,
        Code = product.Code,
        Name = product.Name,
        Description = product.Description,
        Price = product.Price,
        Stock = product.Stock,
        MinStock = product.MinStock,
        Status = product.Status,
        StatusText = product.Status == ProductStatus.Normal ? "正常" : "停用",
        IsLowStock = product.Stock <= product.MinStock,
        CreateTime = product.CreateTime,
        UpdateTime = product.UpdateTime,
        CategoryName = product.Category?.Name
    };
}

// 问题：
// ❌ 代码冗长，容易出错
// ❌ 字段增加时需要手动更新
// ❌ 性能较差（反射或手动赋值）
// ❌ 难以维护
```

**Mapster自动映射：**
```csharp
// 1. 简单映射（零配置）
public ProductDto ConvertToDto(Product product)
{
    return product.Adapt<ProductDto>(); // 一行代码完成映射
}

// 2. 批量映射
public List<ProductDto> ConvertToDtoList(List<Product> products)
{
    return products.Adapt<List<ProductDto>>(); // 批量映射
}

// 3. 应用服务中的使用
public class ProductApp
{
    public async Task<ProductDto> CreateAsync(CreateProductReq req)
    {
        // 请求映射为实体
        var product = req.Adapt<Product>();

        var created = await _productService.CreateAsync(product);

        // 实体映射为响应
        return created.Adapt<ProductDto>();
    }

    public async Task<PagedResult<ProductDto>> GetPagedListAsync(PageListReq<ProductPageListReq> req)
    {
        var result = await _productService.GetPagedListAsync(req.Data);

        // 分页结果映射
        return new PagedResult<ProductDto>
        {
            Items = result.Items.Adapt<List<ProductDto>>(), // 批量映射
            TotalCount = result.TotalCount,
            PageIndex = result.PageIndex,
            PageSize = result.PageSize
        };
    }
}
```

**Mapster自定义映射配置：**
```csharp
// 1. 全局映射配置
public class MappingConfig : IRegister
{
    public void Register(TypeAdapterConfig config)
    {
        // Product -> ProductDto 映射配置
        config.NewConfig<Product, ProductDto>()
            .Map(dest => dest.CategoryName, src => src.Category.Name) // 导航属性映射
            .Map(dest => dest.StatusText, src => src.Status == ProductStatus.Normal ? "正常" : "停用") // 枚举转换
            .Map(dest => dest.IsLowStock, src => src.Stock <= src.MinStock) // 计算属性
            .Map(dest => dest.CreateTimeText, src => src.CreateTime.ToString("yyyy-MM-dd HH:mm:ss")); // 格式化

        // CreateProductReq -> Product 映射配置
        config.NewConfig<CreateProductReq, Product>()
            .Map(dest => dest.Id, src => SnowflakeHelper.NextId()) // 自动生成ID
            .Map(dest => dest.CreateTime, src => DateTime.Now) // 设置创建时间
            .Map(dest => dest.Status, src => ProductStatus.Normal) // 设置默认状态
            .Ignore(dest => dest.TenantId) // 忽略某些字段，由业务逻辑设置
            .Ignore(dest => dest.CreateBy);
    }
}

// 2. 条件映射
config.NewConfig<Product, ProductDto>()
    .Map(dest => dest.PriceText,
         src => src.Price > 1000 ? $"¥{src.Price:N2}(高价)" : $"¥{src.Price:N2}",
         src => src.Price > 0); // 条件映射

// 3. 嵌套对象映射
config.NewConfig<Order, OrderDto>()
    .Map(dest => dest.Items, src => src.OrderItems.Adapt<List<OrderItemDto>>()) // 嵌套集合映射
    .Map(dest => dest.Customer, src => src.Customer.Adapt<CustomerDto>()); // 嵌套对象映射
```

**Mapster性能优势：**
```csharp
// 性能对比测试
[Benchmark]
public class MappingBenchmark
{
    private List<Product> _products;

    [GlobalSetup]
    public void Setup()
    {
        _products = GenerateProducts(10000); // 生成1万个产品
    }

    [Benchmark]
    public List<ProductDto> AutoMapper()
    {
        return _mapper.Map<List<ProductDto>>(_products); // AutoMapper
    }

    [Benchmark]
    public List<ProductDto> Mapster()
    {
        return _products.Adapt<List<ProductDto>>(); // Mapster
    }

    [Benchmark]
    public List<ProductDto> Manual()
    {
        return _products.Select(p => new ProductDto { ... }).ToList(); // 手动映射
    }
}

// 结果：
// Mapster: 100ms (最快)
// Manual: 120ms
// AutoMapper: 180ms (最慢)
```

###  2.4. <a name='GitSVN20'></a>1.4 版本控制：为什么选择Git而不是SVN (20分钟)

**SVN (集中式版本控制)：**
```
SVN服务器
    ↓
开发者A ← → 开发者B ← → 开发者C

问题：
❌ 必须联网才能提交
❌ 服务器故障全员停工
❌ 分支合并复杂
❌ 历史记录在服务器
```

**Git (分布式版本控制)：**
```
开发者A(完整仓库) ← → 远程仓库 ← → 开发者B(完整仓库)
                        ↕
                   开发者C(完整仓库)

优势：
✅ 离线工作，本地提交
✅ 每个人都有完整历史
✅ 分支操作轻量快速
✅ 强大的合并能力
```

**实际对比示例：**
```bash
# SVN 创建分支（服务器操作）
svn copy trunk branches/feature-login

# Git 创建分支（本地操作，秒级完成）
git checkout -b feature-login

# SVN 合并（复杂，容易冲突）
svn merge branches/feature-login trunk

# Git 合并（智能，冲突处理友好）
git merge feature-login
```

###  2.5. <a name='-1'></a>1.3 为什么工业管理系统需要缓存 (30分钟)

**工业管理系统特点：**
- 📊 **多用户并发访问**：生产管理、质量管理、设备管理等多个部门同时使用
- 🔄 **数据查询频繁**：生产报表、设备状态、库存信息等高频查询
- 📈 **业务数据量大**：生产订单、工艺参数、质量数据、设备档案等海量数据
- ⏰ **响应时间要求**：管理决策需要快速获取数据支持
- 🏭 **24小时运行**：工厂不停机，系统需要持续稳定运行

**不使用缓存的问题：**
```csharp
// 工业管理系统典型场景 - 生产看板数据查询
public async Task<ProductionDashboard> GetProductionDashboard()
{
    // 每次都查数据库 - 慢！
    var productionOrders = await _database.QueryAsync<ProductionOrder>(
        "SELECT * FROM ProductionOrders WHERE Status = 'InProgress'"); // 100ms

    var equipmentStatus = await _database.QueryAsync<Equipment>(
        "SELECT * FROM Equipment WHERE Status = 'Running'"); // 150ms

    var qualityData = await _database.QueryAsync<QualityRecord>(
        "SELECT * FROM QualityRecords WHERE CreateTime >= @today",
        new { today = DateTime.Today }); // 200ms

    var inventoryData = await _database.QueryAsync<Inventory>(
        "SELECT * FROM Inventory WHERE Quantity > 0"); // 120ms

    // 总耗时：570ms，用户等待时间长
    return new ProductionDashboard
    {
        ProductionOrders = productionOrders,
        EquipmentStatus = equipmentStatus,
        QualityData = qualityData,
        InventoryData = inventoryData
    };
}

// 问题：
// 1. 响应时间慢：每次查询570ms，管理人员等待时间长
// 2. 数据库压力大：100个管理人员同时查看 = 每秒数百次复杂查询
// 3. 资源浪费：相同的生产数据被重复查询
// 4. 用户体验差：页面加载慢，影响决策效率
```

**使用缓存的优势：**
```csharp
// 工业管理系统使用缓存 - 快！
public async Task<ProductionDashboard> GetProductionDashboard()
{
    var cacheKey = "production:dashboard";

    // 先查缓存，耗时1-5ms
    var cached = await _cache.GetAsync<ProductionDashboard>(cacheKey);
    if (cached != null)
        return cached; // 缓存命中，快速返回

    // 缓存未命中，查数据库（只有第一次或缓存过期时）
    var dashboard = await BuildDashboardFromDatabase();

    // 存入缓存，5分钟过期（生产数据不需要实时到秒）
    await _cache.SetAsync(cacheKey, dashboard, TimeSpan.FromMinutes(5));

    return dashboard;
}

// 分层缓存策略
public async Task<List<Equipment>> GetEquipmentByWorkshop(int workshopId)
{
    var cacheKey = $"equipment:workshop:{workshopId}";

    // 设备基础信息变化不频繁，缓存1小时
    return await _cache.GetOrSetAsync(cacheKey,
        () => _equipmentRepository.GetByWorkshopAsync(workshopId),
        TimeSpan.FromHours(1));
}

public async Task<ProductionOrder> GetProductionOrder(int orderId)
{
    var cacheKey = $"production:order:{orderId}";

    // 生产订单状态变化较频繁，缓存10分钟
    return await _cache.GetOrSetAsync(cacheKey,
        () => _orderRepository.GetByIdAsync(orderId),
        TimeSpan.FromMinutes(10));
}

// 优势：
// 1. 响应速度：缓存命中5ms vs 数据库查询570ms，提升100倍
// 2. 数据库压力：减少95%的数据库查询，数据库资源释放给核心业务
// 3. 用户体验：管理看板秒开，决策效率大幅提升
// 4. 系统稳定性：数据库负载降低，系统更稳定
// 5. 成本节约：减少数据库服务器资源需求
```

**工业管理系统缓存使用场景对比：**
```
适合缓存的数据：
✅ 设备档案信息（设备型号、规格等基本不变）
✅ 工艺参数配置（标准工艺流程相对稳定）
✅ 用户权限信息（角色权限变化不频繁）
✅ 产品BOM信息（物料清单相对固定）
✅ 车间组织架构（部门结构变化较少）
✅ 生产统计报表（按小时/天汇总的数据）
✅ 库存预警设置（预警阈值配置）

不适合缓存的数据：
❌ 实时生产数据（产量、进度等秒级变化）
❌ 设备实时状态（运行/停机状态实时变化）
❌ 质量检测结果（每次检测都是新数据）
❌ 操作日志记录（审计数据，一次性写入）
❌ 临时计算结果（用完即弃的中间数据）
❌ 紧急报警信息（安全相关，必须实时）

缓存策略建议：
📋 基础数据：缓存2-24小时（如设备档案、工艺参数）
📊 统计数据：缓存10-60分钟（如生产报表、库存汇总）
👥 用户数据：缓存30-120分钟（如权限信息、个人设置）
⚙️ 配置数据：缓存1-6小时（如系统参数、预警设置）
```

###  2.6. <a name='-1'></a>1.4 第三方组件选择的原因 (40分钟)

**为什么不自己写，要用第三方组件？**

**1. SqlSugar ORM vs 原生SQL**
```csharp
// 原生SQL - 繁琐易错
public async Task<List<User>> GetUsersByDepartment(int deptId)
{
    var sql = @"
        SELECT u.Id, u.Name, u.Email, d.DeptName
        FROM Users u
        INNER JOIN Departments d ON u.DeptId = d.Id
        WHERE u.DeptId = @deptId AND u.IsActive = 1";

    using var connection = new SqlConnection(_connectionString);
    var users = new List<User>();

    using var command = new SqlCommand(sql, connection);
    command.Parameters.AddWithValue("@deptId", deptId);

    await connection.OpenAsync();
    using var reader = await command.ExecuteReaderAsync();

    while (await reader.ReadAsync())
    {
        users.Add(new User
        {
            Id = reader.GetInt32("Id"),
            Name = reader.GetString("Name"),
            Email = reader.GetString("Email"),
            DeptName = reader.GetString("DeptName")
        });
    }

    return users;
}

// SqlSugar ORM - 简洁安全
public async Task<List<User>> GetUsersByDepartment(int deptId)
{
    return await _db.Queryable<User>()
        .LeftJoin<Department>((u, d) => u.DeptId == d.Id)
        .Where(u => u.DeptId == deptId && u.IsActive)
        .Select((u, d) => new User
        {
            Id = u.Id,
            Name = u.Name,
            Email = u.Email,
            DeptName = d.DeptName
        })
        .ToListAsync();
}

// SqlSugar优势：
// ✅ 类型安全：编译时检查，避免字段名错误
// ✅ 防SQL注入：自动参数化查询
// ✅ 代码简洁：减少80%代码量
// ✅ 智能提示：IDE自动补全
// ✅ 多数据库：支持MySQL、SQL Server、Oracle等
```

**2. Autofac依赖注入 vs 手动创建对象**
```csharp
// 手动创建 - 耦合度高
public class OrderController
{
    public async Task<IActionResult> CreateOrder(CreateOrderRequest request)
    {
        // 手动创建所有依赖，修改一个影响全部
        var connectionString = ConfigurationManager.ConnectionStrings["Default"];
        var dbContext = new SqlSugarClient(connectionString);
        var orderRepository = new OrderRepository(dbContext);
        var productRepository = new ProductRepository(dbContext);
        var userRepository = new UserRepository(dbContext);
        var emailService = new EmailService("smtp.company.com");
        var orderService = new OrderService(orderRepository, productRepository, emailService);

        return await orderService.CreateAsync(request);
    }
}

// Autofac依赖注入 - 松耦合
public class OrderController
{
    private readonly OrderService _orderService;

    // 构造函数注入，框架自动解析所有依赖
    public OrderController(OrderService orderService)
    {
        _orderService = orderService;
    }

    public async Task<IActionResult> CreateOrder(CreateOrderRequest request)
    {
        return await _orderService.CreateAsync(request);
    }
}

// 依赖注入优势：
// ✅ 松耦合：类之间不直接依赖具体实现
// ✅ 易测试：可以注入Mock对象进行单元测试
// ✅ 易维护：修改实现不影响调用方
// ✅ 生命周期管理：自动管理对象创建和销毁
```

**3. Hangfire后台任务 vs 手动线程**
```csharp
// 手动线程 - 复杂危险
public void SendEmailAsync(string email, string content)
{
    // 问题：线程管理复杂，异常处理困难，无法监控
    Task.Run(async () =>
    {
        try
        {
            await _emailService.SendAsync(email, content);
        }
        catch (Exception ex)
        {
            // 异常被吞掉，无法追踪
            _logger.LogError(ex, "发送邮件失败");
        }
    });
}

// Hangfire - 专业可靠
public void SendEmailAsync(string email, string content)
{
    // 优势：持久化、重试、监控、集群支持
    BackgroundJob.Enqueue(() => _emailService.SendAsync(email, content));
}

// Hangfire优势：
// ✅ 持久化：任务存储在数据库，服务重启不丢失
// ✅ 重试机制：失败自动重试，可配置重试策略
// ✅ 监控界面：可视化监控任务执行状态
// ✅ 集群支持：多个服务器共享任务队列
// ✅ 定时任务：支持Cron表达式定时执行
```

---

##  3. <a name='CYSF3'></a>🏗️ 第二部分：CYSF框架架构深度解析 (3小时)

###  3.1. <a name='DDD45'></a>2.1 DDD分层架构的意义 (45分钟)

**传统三层架构问题：**
```
UI层 → 业务层 → 数据层

问题：
❌ 业务逻辑分散在UI和数据层
❌ 数据库表结构直接暴露给UI
❌ 难以应对复杂业务场景
❌ 测试困难，耦合度高
```

**CYSF的DDD分层架构：**
```
Api.Backend (接口层)
    ↓ 只处理HTTP请求响应
CYSF.Application (应用层)
    ↓ 协调业务流程，管理事务
CYSF.Services (领域服务层)
    ↓ 核心业务逻辑实现
CYSF.Repositories (仓储层)
    ↓ 数据访问抽象
CYSF.Models (模型层)
    ↓ 领域实体定义
CYSF.Core (基础设施层)
    ↓ 通用工具和服务
```

**实际代码示例：**
```csharp
// 1. 模型层 - 领域实体
public class Order
{
    public int Id { get; set; }
    public decimal Amount { get; set; }
    public OrderStatus Status { get; set; }

    // 领域方法 - 业务规则在实体内部
    public void Cancel()
    {
        if (Status == OrderStatus.Shipped)
            throw new BusinessException("已发货订单不能取消");

        Status = OrderStatus.Cancelled;
    }
}

// 2. 仓储层 - 数据访问抽象
public class OrderRepository
{
    public async Task<Order> GetByIdAsync(int id)
    {
        return await _db.Queryable<Order>()
            .FirstAsync(x => x.Id == id);
    }
}

// 3. 领域服务层 - 核心业务逻辑
public class OrderService
{
    public async Task<Order> CancelOrderAsync(int orderId)
    {
        var order = await _orderRepository.GetByIdAsync(orderId);
        if (order == null)
            throw new NotFoundException("订单不存在");

        // 调用领域方法
        order.Cancel();

        // 业务规则：取消订单需要退款
        if (order.Amount > 0)
        {
            await _paymentService.RefundAsync(order.Id, order.Amount);
        }

        await _orderRepository.UpdateAsync(order);
        return order;
    }
}

// 4. 应用层 - 协调业务流程
public class OrderApp
{
    public async Task<OrderDto> CancelOrderAsync(int orderId)
    {
        // 开启事务
        using var transaction = await _db.BeginTransactionAsync();
        try
        {
            var order = await _orderService.CancelOrderAsync(orderId);

            // 清除缓存
            await _cacheHelper.RemoveAsync(CacheKey.Order(orderId));

            // 发送通知
            await _notificationService.SendOrderCancelledAsync(order);

            await transaction.CommitAsync();
            return order.Adapt<OrderDto>();
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }
}

// 5. 接口层 - HTTP请求处理
[ApiController]
public class OrderController
{
    [HttpPost("cancel/{id}")]
    public async Task<IActionResult> CancelOrder(int id)
    {
        var result = await _orderApp.CancelOrderAsync(id);
        return Ok(result);
    }
}
```

**分层的好处：**
- 🎯 **职责清晰**：每层只关注自己的职责
- 🧪 **易于测试**：可以单独测试每一层
- 🔄 **易于替换**：可以替换某一层的实现
- 📈 **易于扩展**：新增功能不影响其他层

###  3.2. <a name='NET45'></a>2.2 .NET依赖注入生命周期详解 (45分钟)

**什么是线程安全？**

线程安全是指在多线程环境下，多个线程同时访问同一个对象时，不会导致数据不一致或程序崩溃的特性。

```csharp
// 线程不安全的示例
public class UnsafeCounter
{
    private int _count = 0;

    public void Increment()
    {
        _count++; // 这个操作不是原子的！
        // 实际上包含三个步骤：
        // 1. 读取 _count 的值
        // 2. 将值加1
        // 3. 将结果写回 _count
        // 在多线程环境下，这三个步骤可能被其他线程打断
    }

    public int GetCount() => _count;
}

// 问题演示：
var counter = new UnsafeCounter();
var tasks = new List<Task>();

// 启动10个线程，每个线程执行1000次递增
for (int i = 0; i < 10; i++)
{
    tasks.Add(Task.Run(() =>
    {
        for (int j = 0; j < 1000; j++)
        {
            counter.Increment();
        }
    }));
}

await Task.WhenAll(tasks);
Console.WriteLine(counter.GetCount()); // 期望：10000，实际：可能是9876、9234等

// 线程安全的示例
public class SafeCounter
{
    private int _count = 0;
    private readonly object _lock = new object();

    public void Increment()
    {
        lock (_lock) // 确保同一时间只有一个线程能执行
        {
            _count++;
        }
    }

    public int GetCount()
    {
        lock (_lock)
        {
            return _count;
        }
    }
}

// 或者使用原子操作
public class AtomicCounter
{
    private int _count = 0;

    public void Increment()
    {
        Interlocked.Increment(ref _count); // 原子操作，线程安全
    }

    public int GetCount() => _count;
}
```

**为什么需要不同的生命周期？**

在企业级应用中，不同的服务有不同的使用特点和资源需求，需要合理的生命周期管理。特别是在多线程环境下，生命周期的选择直接影响线程安全性。

**三种主要生命周期：**

**1. Singleton (单例) - 全局唯一**
```csharp
// 适用场景：配置服务、缓存服务、连接池、雪花ID生成器
services.AddSingleton<ISnowflakeIdGenerator, SnowflakeIdGenerator>();
services.AddSingleton<ICacheService, RedisCacheService>();

// 特点：
// ✅ 整个应用生命周期只创建一次
// ✅ 所有请求共享同一个实例
// ✅ 内存占用少，性能好
// ❌ 必须线程安全
// ❌ 不能依赖Scoped服务

// 雪花ID生成器 - 典型的Singleton场景
public class SnowflakeIdGenerator : ISnowflakeIdGenerator
{
    private readonly object _lock = new object();
    private long _lastTimestamp = -1L;
    private long _sequence = 0L;
    private readonly long _workerId;

    public SnowflakeIdGenerator(IConfiguration configuration)
    {
        _workerId = configuration.GetValue<long>("Snowflake:WorkerId");
    }

    // 必须线程安全！多个请求可能同时调用
    public long NextId()
    {
        lock (_lock) // 使用锁保证线程安全
        {
            var timestamp = GetCurrentTimestamp();

            if (timestamp < _lastTimestamp)
                throw new InvalidOperationException("时钟回拨检测");

            if (timestamp == _lastTimestamp)
            {
                _sequence = (_sequence + 1) & 4095; // 序列号递增
                if (_sequence == 0)
                {
                    timestamp = WaitNextMillis(_lastTimestamp);
                }
            }
            else
            {
                _sequence = 0;
            }

            _lastTimestamp = timestamp;

            return ((timestamp - 1609459200000L) << 22) | (_workerId << 12) | _sequence;
        }
    }
}

// 缓存服务 - 另一个典型的Singleton场景
public class RedisCacheService : ICacheService
{
    private readonly IConnectionMultiplexer _redis;
    private readonly ConcurrentDictionary<string, SemaphoreSlim> _semaphores;

    public RedisCacheService(IConnectionMultiplexer redis)
    {
        _redis = redis;
        _semaphores = new ConcurrentDictionary<string, SemaphoreSlim>();
    }

    // 线程安全的缓存操作
    public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan expiry)
    {
        var db = _redis.GetDatabase();
        var cached = await db.StringGetAsync(key);

        if (cached.HasValue)
            return JsonSerializer.Deserialize<T>(cached);

        // 防止缓存击穿：同一个key只允许一个线程去加载数据
        var semaphore = _semaphores.GetOrAdd(key, _ => new SemaphoreSlim(1, 1));

        await semaphore.WaitAsync();
        try
        {
            // 双重检查
            cached = await db.StringGetAsync(key);
            if (cached.HasValue)
                return JsonSerializer.Deserialize<T>(cached);

            // 加载数据
            var data = await factory();
            await db.StringSetAsync(key, JsonSerializer.Serialize(data), expiry);
            return data;
        }
        finally
        {
            semaphore.Release();
        }
    }
}
```

**2. Scoped (作用域) - 请求级别**
```csharp
// 适用场景：数据库上下文、业务服务、ContextUser、工作单元
services.AddScoped<SqlSugarScope>();
services.AddScoped<ContextUser>();
services.AddScoped<IUnitOfWork, UnitOfWork>();

// 特点：
// ✅ 每个HTTP请求创建一次
// ✅ 同一请求内共享实例
// ✅ 请求结束自动释放
// ✅ 可以依赖Singleton和Transient
// ❌ 不能被Singleton依赖

// ContextUser - 典型的Scoped场景
public class ContextUser
{
    public int UserId { get; set; }
    public string UserName { get; set; }
    public int TenantId { get; set; }
    public List<string> Permissions { get; set; } = new();

    // 请求级别的用户上下文，不需要考虑线程安全
    // 因为每个请求都有独立的实例
    public bool HasPermission(string permission) => Permissions.Contains(permission);
}

// 工作单元模式 - 管理请求级别的事务
public class UnitOfWork : IUnitOfWork, IDisposable
{
    private readonly SqlSugarScope _db;
    private readonly ContextUser _contextUser;
    private ITenant _transaction;
    private bool _disposed = false;

    public UnitOfWork(SqlSugarScope db, ContextUser contextUser)
    {
        _db = db;
        _contextUser = contextUser;
    }

    public async Task BeginTransactionAsync()
    {
        _transaction = await _db.BeginTranAsync();
    }

    public async Task CommitAsync()
    {
        if (_transaction != null)
        {
            await _transaction.CommitTranAsync();
            _transaction = null;
        }
    }

    public async Task RollbackAsync()
    {
        if (_transaction != null)
        {
            await _transaction.RollbackTranAsync();
            _transaction = null;
        }
    }

    // 请求结束时自动释放资源
    public void Dispose()
    {
        if (!_disposed)
        {
            _transaction?.Dispose();
            _disposed = true;
        }
    }
}

// 业务服务 - 使用Scoped的数据库上下文和用户上下文
public class ProductService : IProductService
{
    private readonly SqlSugarScope _db;
    private readonly ContextUser _contextUser;
    private readonly IUnitOfWork _unitOfWork;

    public ProductService(SqlSugarScope db, ContextUser contextUser, IUnitOfWork unitOfWork)
    {
        _db = db;           // 请求级别的数据库上下文
        _contextUser = contextUser; // 请求级别的用户上下文
        _unitOfWork = unitOfWork;   // 请求级别的工作单元
    }

    public async Task<Product> CreateAsync(Product product)
    {
        // 同一个请求内，所有操作使用相同的数据库连接和用户上下文
        product.TenantId = _contextUser.TenantId; // 自动设置租户ID
        product.CreateBy = _contextUser.UserId;   // 自动设置创建人

        await _unitOfWork.BeginTransactionAsync();
        try
        {
            await _db.Insertable(product).ExecuteCommandAsync();
            await _unitOfWork.CommitAsync();
            return product;
        }
        catch
        {
            await _unitOfWork.RollbackAsync();
            throw;
        }
    }
}
```

**3. Transient (瞬时) - 每次创建**
```csharp
// 适用场景：轻量级服务、无状态服务、工具类、验证器
services.AddTransient<IEmailService, EmailService>();
services.AddTransient<IExcelExportService, ExcelExportService>();
services.AddTransient<IQRCodeGenerator, QRCodeGenerator>();

// 特点：
// ✅ 每次注入都创建新实例
// ✅ 无状态，天然线程安全
// ✅ 可以依赖任何生命周期的服务
// ❌ 内存开销大
// ❌ 创建成本高

// Excel导出服务 - 典型的Transient场景
public class ExcelExportService : IExcelExportService
{
    private readonly ILogger<ExcelExportService> _logger;

    public ExcelExportService(ILogger<ExcelExportService> logger)
    {
        _logger = logger;
    }

    // 无状态方法，每次调用都是独立的
    public async Task<byte[]> ExportProductsAsync<T>(List<T> data, string sheetName)
    {
        using var workbook = new XLWorkbook();
        var worksheet = workbook.Worksheets.Add(sheetName);

        // 每次导出都是新的实例，不会有状态冲突
        var properties = typeof(T).GetProperties();

        // 写入表头
        for (int i = 0; i < properties.Length; i++)
        {
            worksheet.Cell(1, i + 1).Value = properties[i].Name;
        }

        // 写入数据
        for (int row = 0; row < data.Count; row++)
        {
            for (int col = 0; col < properties.Length; col++)
            {
                var value = properties[col].GetValue(data[row]);
                worksheet.Cell(row + 2, col + 1).Value = value?.ToString() ?? "";
            }
        }

        using var stream = new MemoryStream();
        workbook.SaveAs(stream);
        return stream.ToArray();
    }
}

// 二维码生成器 - 无状态工具类
public class QRCodeGenerator : IQRCodeGenerator
{
    public byte[] GenerateQRCode(string content, int size = 200)
    {
        // 每次生成都是独立的，无状态
        var qrGenerator = new QRCodeGenerator();
        var qrCodeData = qrGenerator.CreateQrCode(content, QRCodeGenerator.ECCLevel.Q);
        var qrCode = new PngByteQRCode(qrCodeData);
        return qrCode.GetGraphic(size / 25); // 每次都创建新的图片
    }
}

// 邮件服务 - 每次发送都是独立的操作
public class EmailService : IEmailService
{
    private readonly ILogger<EmailService> _logger;
    private readonly IConfiguration _configuration;

    public EmailService(ILogger<EmailService> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
    }

    public async Task SendAsync(string to, string subject, string body)
    {
        // 每次发送邮件都创建新的SMTP客户端
        using var client = new SmtpClient(_configuration["Email:Host"])
        {
            Port = _configuration.GetValue<int>("Email:Port"),
            Credentials = new NetworkCredential(
                _configuration["Email:Username"],
                _configuration["Email:Password"]),
            EnableSsl = true
        };

        var message = new MailMessage
        {
            From = new MailAddress(_configuration["Email:From"]),
            Subject = subject,
            Body = body,
            IsBodyHtml = true
        };
        message.To.Add(to);

        await client.SendMailAsync(message);
        _logger.LogInformation("邮件发送成功: {To} - {Subject}", to, subject);

        // 使用完毕自动释放，不会影响其他实例
    }
}
```

**实际使用对比：**
```csharp
public class OrderController : ControllerBase
{
    private readonly IOrderService _orderService;     // Scoped - 请求级别
    private readonly ICacheService _cacheService;     // Singleton - 全局共享
    private readonly IEmailService _emailService;     // Transient - 每次新建

    public OrderController(
        IOrderService orderService,
        ICacheService cacheService,
        IEmailService emailService)
    {
        _orderService = orderService;
        _cacheService = cacheService;
        _emailService = emailService;
    }

    [HttpPost]
    public async Task<IActionResult> CreateOrder(CreateOrderRequest request)
    {
        // 1. _orderService: 这个请求期间一直是同一个实例
        var order = await _orderService.CreateAsync(request);

        // 2. _cacheService: 全局共享，所有请求都用同一个实例
        await _cacheService.SetAsync($"order:{order.Id}", order);

        // 3. _emailService: 每次调用都是新实例
        await _emailService.SendAsync(request.Email, "订单确认", "您的订单已创建");

        return Ok(order);
    }
}
```

**生命周期选择原则：**
```
Singleton 适用于：
✅ 配置服务 (IConfiguration)
✅ 缓存服务 (ICacheService)
✅ 日志服务 (ILogger)
✅ 连接池 (IConnectionMultiplexer)

Scoped 适用于：
✅ 数据库上下文 (SqlSugarScope)
✅ 业务服务 (IOrderService)
✅ 仓储服务 (IOrderRepository)
✅ 应用服务 (OrderApp)

Transient 适用于：
✅ 工具类 (IEmailService)
✅ 轻量级服务 (IValidationService)
✅ 无状态服务 (ICalculationService)
```

###  3.3. <a name='IDvsGUIDID30'></a>2.3 雪花ID vs GUID：为什么选择雪花ID (30分钟)

**GUID的问题：**
```csharp
// GUID示例
var guid = Guid.NewGuid(); // "f47ac10b-58cc-4372-a567-0e02b2c3d479"

// 问题：
// ❌ 36个字符，存储空间大
// ❌ 无序性，数据库索引性能差
// ❌ 不包含时间信息，难以排序
// ❌ 可读性差，调试困难
```

**雪花ID的优势：**
```csharp
// 雪花ID示例
var snowflakeId = SnowflakeHelper.NextId(); // 1234567890123456789

// 优势：
// ✅ 64位长整型，存储空间小
// ✅ 趋势递增，数据库索引友好
// ✅ 包含时间戳，天然排序
// ✅ 高性能，单机400万QPS
// ✅ 分布式唯一，支持集群
```

**性能对比测试：**
```csharp
// 数据库插入性能测试
public class IdPerformanceTest
{
    [Test]
    public async Task InsertPerformanceTest()
    {
        // GUID作为主键
        var guidStopwatch = Stopwatch.StartNew();
        for (int i = 0; i < 10000; i++)
        {
            var entity = new EntityWithGuid
            {
                Id = Guid.NewGuid(),
                Name = $"Test{i}"
            };
            await _db.Insertable(entity).ExecuteCommandAsync();
        }
        guidStopwatch.Stop();

        // 雪花ID作为主键
        var snowflakeStopwatch = Stopwatch.StartNew();
        for (int i = 0; i < 10000; i++)
        {
            var entity = new EntityWithSnowflake
            {
                Id = SnowflakeHelper.NextId(),
                Name = $"Test{i}"
            };
            await _db.Insertable(entity).ExecuteCommandAsync();
        }
        snowflakeStopwatch.Stop();

        // 结果：雪花ID比GUID快30-50%
        Console.WriteLine($"GUID插入耗时: {guidStopwatch.ElapsedMilliseconds}ms");
        Console.WriteLine($"雪花ID插入耗时: {snowflakeStopwatch.ElapsedMilliseconds}ms");
    }
}
```

**雪花ID结构解析：**
```
雪花ID: 1234567890123456789
解析结果:
- 生成时间: 2024-01-15 14:30:25
- 数据中心ID: 1
- 工作机器ID: 1
- 序列号: 123

// 代码示例
var idInfo = SnowflakeHelper.ParseId(1234567890123456789L);
Console.WriteLine($"生成时间: {idInfo.GeneratedAt}");
Console.WriteLine($"工作机器: {idInfo.WorkerId}");
Console.WriteLine($"数据中心: {idInfo.DatacenterId}");
```

**实际应用场景：**
```csharp
// 订单号生成
public class OrderService
{
    public async Task<Order> CreateOrderAsync(CreateOrderRequest request)
    {
        var order = new Order
        {
            Id = SnowflakeHelper.NextId(), // 雪花ID作为主键
            OrderNo = $"ORD{SnowflakeHelper.NextId()}", // 订单号
            UserId = request.UserId,
            Amount = request.Amount,
            CreateTime = DateTime.Now
        };

        await _orderRepository.CreateAsync(order);
        return order;
    }
}

// 优势：
// 1. 订单ID天然按时间排序
// 2. 可以从ID推算订单创建时间
// 3. 分布式环境下绝对唯一
// 4. 数据库查询性能优异
```

###  3.4. <a name='ContextUser30'></a>2.4 ContextUser：上下文用户信息 (30分钟)

**什么是ContextUser？**

ContextUser是当前请求上下文中的用户信息，包含当前登录用户的基本信息和权限数据。

**为什么需要ContextUser？**
```csharp
// 不使用ContextUser - 每次都要查询
public class OrderService
{
    public async Task<List<Order>> GetMyOrdersAsync(int userId)
    {
        // 每次都要验证用户是否存在
        var user = await _userRepository.GetByIdAsync(userId);
        if (user == null)
            throw new UnauthorizedException("用户不存在");

        // 每次都要查询用户权限
        var permissions = await _permissionService.GetUserPermissionsAsync(userId);
        if (!permissions.Contains("ORDER_VIEW"))
            throw new ForbiddenException("无权限查看订单");

        return await _orderRepository.GetByUserIdAsync(userId);
    }
}

// 使用ContextUser - 一次解析，全局使用
public class OrderService
{
    private readonly ContextUser _contextUser;

    public OrderService(ContextUser contextUser)
    {
        _contextUser = contextUser; // 请求开始时已解析好
    }

    public async Task<List<Order>> GetMyOrdersAsync()
    {
        // 直接使用，无需重复查询
        if (!_contextUser.HasPermission("ORDER_VIEW"))
            throw new ForbiddenException("无权限查看订单");

        return await _orderRepository.GetByUserIdAsync(_contextUser.UserId);
    }
}
```

**ContextUser的实现：**
```csharp
// ContextUser定义
public class ContextUser
{
    public int UserId { get; set; }
    public string UserName { get; set; }
    public string Email { get; set; }
    public int TenantId { get; set; }
    public string TenantCode { get; set; }
    public List<string> Roles { get; set; } = new();
    public List<string> Permissions { get; set; } = new();

    // 便捷方法
    public bool HasRole(string role) => Roles.Contains(role);
    public bool HasPermission(string permission) => Permissions.Contains(permission);
    public bool IsAdmin => HasRole("Admin");
}

// JWT中间件解析ContextUser
public class JwtMiddleware
{
    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        var token = context.Request.Headers["Authorization"]
            .FirstOrDefault()?.Split(" ").Last();

        if (token != null)
        {
            var contextUser = await ParseTokenToContextUser(token);

            // 注册到DI容器，作用域为当前请求
            context.RequestServices.GetService<ContextUser>().CopyFrom(contextUser);
        }

        await next(context);
    }

    private async Task<ContextUser> ParseTokenToContextUser(string token)
    {
        // 解析JWT Token
        var tokenHandler = new JwtSecurityTokenHandler();
        var jsonToken = tokenHandler.ReadJwtToken(token);

        var userId = int.Parse(jsonToken.Claims.First(x => x.Type == "userId").Value);
        var tenantId = int.Parse(jsonToken.Claims.First(x => x.Type == "tenantId").Value);

        // 从缓存获取用户详细信息
        var userInfo = await _cacheHelper.GetOrSetAsync(
            CacheKey.UserInfo(userId, tenantId),
            () => _userService.GetUserInfoAsync(userId, tenantId),
            TimeSpan.FromMinutes(30)
        );

        return new ContextUser
        {
            UserId = userInfo.UserId,
            UserName = userInfo.UserName,
            Email = userInfo.Email,
            TenantId = userInfo.TenantId,
            TenantCode = userInfo.TenantCode,
            Roles = userInfo.Roles,
            Permissions = userInfo.Permissions
        };
    }
}
```

**ContextUser的使用场景：**
```csharp
// 1. 数据权限控制
public class OrderRepository
{
    private readonly ContextUser _contextUser;

    public async Task<List<Order>> GetOrdersAsync()
    {
        var query = _db.Queryable<Order>();

        // 根据用户角色过滤数据
        if (!_contextUser.IsAdmin)
        {
            // 普通用户只能看自己的订单
            query = query.Where(x => x.UserId == _contextUser.UserId);
        }

        // 多租户数据隔离
        query = query.Where(x => x.TenantId == _contextUser.TenantId);

        return await query.ToListAsync();
    }
}

// 2. 操作日志记录
public class AuditService
{
    public async Task LogOperationAsync(string operation, object data)
    {
        var log = new OperationLog
        {
            UserId = _contextUser.UserId,
            UserName = _contextUser.UserName,
            TenantId = _contextUser.TenantId,
            Operation = operation,
            Data = JsonSerializer.Serialize(data),
            CreateTime = DateTime.Now
        };

        await _logRepository.CreateAsync(log);
    }
}

// 3. 业务规则验证
public class ProductService
{
    public async Task<Product> CreateProductAsync(CreateProductRequest request)
    {
        // 权限检查
        if (!_contextUser.HasPermission("PRODUCT_CREATE"))
            throw new ForbiddenException("无权限创建产品");

        var product = new Product
        {
            Name = request.Name,
            Price = request.Price,
            TenantId = _contextUser.TenantId, // 自动设置租户ID
            CreatedBy = _contextUser.UserId,   // 自动设置创建人
            CreatedAt = DateTime.Now
        };

        return await _productRepository.CreateAsync(product);
    }
}
```

**ContextUser的优势：**
- 🚀 **性能优化**：避免重复查询用户信息
- 🔒 **安全保障**：统一的权限验证机制
- 🎯 **数据隔离**：多租户数据自动隔离
- 📝 **审计追踪**：操作日志自动记录用户信息
- 💻 **开发便利**：业务代码无需关心用户信息获取

---

##  4. <a name='2-1'></a>🛠️ 第三部分：环境搭建与配置实战 (2小时)

###  4.1. <a name='-1'></a>3.1 开发环境搭建详解 (45分钟)

**环境要求检查清单：**
```bash
# 1. 检查.NET版本
dotnet --version
# 应该显示: 8.0.x

# 2. 检查SQL Server
sqlcmd -S localhost -E -Q "SELECT @@VERSION"
# 应该能连接并显示版本信息

# 3. 检查Redis（可选）
redis-cli ping
# 应该返回: PONG

# 4. 检查Git
git --version
# 应该显示Git版本
```

**项目克隆和配置：**
```bash
# 1. 克隆项目
git clone [repository-url]
cd CYSF

# 2. 查看项目结构
tree src/Core -L 2
# 应该看到完整的分层结构

# 3. 复制配置文件
cd src/Core/Api/Api.Backend
copy appsettings.json appsettings.Development.json

# 4. 检查项目依赖
dotnet restore
# 应该成功还原所有NuGet包
```

**配置文件详解：**
```json
// appsettings.Development.json - 开发环境配置
{
  // 数据库连接配置
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=CYSF_Dev;Trusted_Connection=true;TrustServerCertificate=true;"
  },

  // Hangfire后台任务配置
  "Hangfire": {
    "ConnectionString": "Server=localhost;Database=CYSF_Dev_Hangfire;Trusted_Connection=true;TrustServerCertificate=true;",
    "Enabled": true,
    "DashboardEnabled": true,
    "DashboardPath": "/hangfire",
    "WorkerCount": 5,
    "Queues": ["default", "critical", "background"],
    "Dashboard": {
      "Username": "admin",
      "Password": "admin123",
      "RequireAuthentication": true
    }
  },

  // 缓存配置
  "Cache": {
    "Provider": "Memory",  // 开发环境推荐Memory，生产环境用Redis
    "RedisConnectionString": "localhost:6379,password=your_password,defaultDatabase=2",
    "DefaultExpirationMinutes": 60,
    "KeyPrefix": "CYSF:Dev:",
    "Enabled": true,
    "MemoryCache": {
      "SizeLimit": 100,
      "CompactionPercentage": 0.25,
      "ExpirationScanFrequency": 60
    }
  },

  // JWT认证配置
  "Jwt": {
    "Secret": "A1B2C3D4E5F6G7H8I9J0K1L2M3N4O5P6Q7R8S9T0U1V2W3X4Y5Z6A7B8C9D0E1F2G3H4I5J6K7L8M9N0",
    "Issuer": "CYSF-API",
    "Audience": "CYSF-Client",
    "TokenExpires": "14400",    // 4小时
    "RefreshTokenExpires": "18000" // 5小时
  },

  // 雪花ID配置
  "Snowflake": {
    "WorkerId": 1,                    // 工作机器ID，每个实例必须唯一
    "DatacenterId": 1,                // 数据中心ID
    "Epoch": "2020-01-01T00:00:00Z",  // 起始时间戳
    "EnableClockBackwardsCheck": true, // 启用时钟回拨检测
    "MaxClockBackwardsMs": 5          // 最大容忍回拨时间
  },

  // CORS跨域配置
  "Cors": {
    "AllowedOrigins": ["http://localhost:3000", "http://localhost:8080"],
    "AllowedMethods": ["GET", "POST", "PUT", "DELETE"],
    "AllowedHeaders": ["*"]
  },

  // 日志配置
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "System": "Warning"
    }
  }
}
```

###  4.2. <a name='-1'></a>3.2 数据库初始化和代码生成 (30分钟)

**重要概念：CYSF采用Database First开发模式**

```
正确的开发流程：
1. 数据库设计工具设计表结构 (PowerDesigner/Navicat等)
2. 执行DDL脚本创建数据库表
3. 运行代码生成器读取已有表结构
4. 自动生成对应的代码文件
```

**第一步：创建数据库和表结构**
```sql
-- 1. 创建数据库
CREATE DATABASE CYSF_Dev;
USE CYSF_Dev;

-- 2. 创建基础表结构（这些表必须先存在）
-- 租户表
CREATE TABLE [Tenant] (
    [Id] int IDENTITY(1,1) PRIMARY KEY,
    [Code] nvarchar(50) NOT NULL COMMENT '租户编码',
    [Name] nvarchar(100) NOT NULL COMMENT '租户名称',
    [Level] int NOT NULL DEFAULT 1 COMMENT '租户级别：1-标准，2-高级',
    [Status] int NOT NULL DEFAULT 1 COMMENT '状态：1-正常，2-停用',
    [CreateTime] datetime NOT NULL DEFAULT GETDATE() COMMENT '创建时间',
    [UpdateTime] datetime NULL COMMENT '更新时间'
);

-- 用户表
CREATE TABLE [TenantUser] (
    [Id] int IDENTITY(1,1) PRIMARY KEY,
    [TenantId] int NOT NULL COMMENT '租户ID',
    [UserName] nvarchar(50) NOT NULL COMMENT '用户名',
    [Password] nvarchar(100) NOT NULL COMMENT '密码',
    [Email] nvarchar(100) NULL COMMENT '邮箱',
    [Mobile] nvarchar(20) NULL COMMENT '手机号',
    [Status] int NOT NULL DEFAULT 1 COMMENT '状态：1-正常，2-停用',
    [CreateTime] datetime NOT NULL DEFAULT GETDATE() COMMENT '创建时间',
    [UpdateTime] datetime NULL COMMENT '更新时间'
);

-- 角色表
CREATE TABLE [Role] (
    [Id] int IDENTITY(1,1) PRIMARY KEY,
    [TenantId] int NOT NULL COMMENT '租户ID',
    [Name] nvarchar(50) NOT NULL COMMENT '角色名称',
    [Description] nvarchar(200) NULL COMMENT '角色描述',
    [CreateTime] datetime NOT NULL DEFAULT GETDATE() COMMENT '创建时间'
);

-- 用户角色关联表
CREATE TABLE [UserRole] (
    [Id] int IDENTITY(1,1) PRIMARY KEY,
    [UserId] int NOT NULL COMMENT '用户ID',
    [RoleId] int NOT NULL COMMENT '角色ID',
    [CreateTime] datetime NOT NULL DEFAULT GETDATE() COMMENT '创建时间'
);
```

**第二步：验证数据库表结构**
```sql
-- 检查表是否创建成功
SELECT
    TABLE_NAME as '表名',
    TABLE_COMMENT as '表注释'
FROM INFORMATION_SCHEMA.TABLES
WHERE TABLE_SCHEMA = 'CYSF_Dev'
  AND TABLE_TYPE = 'BASE TABLE'
ORDER BY TABLE_NAME;

-- 检查字段结构和注释
SELECT
    TABLE_NAME as '表名',
    COLUMN_NAME as '字段名',
    DATA_TYPE as '数据类型',
    IS_NULLABLE as '允许空值',
    COLUMN_DEFAULT as '默认值',
    COLUMN_COMMENT as '字段注释'
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = 'CYSF_Dev'
ORDER BY TABLE_NAME, ORDINAL_POSITION;
```

**第三步：配置代码生成器连接**
```json
// src/Core/Infrastructure/CYSF.Generate/appsettings.json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=CYSF_Dev;Trusted_Connection=true;TrustServerCertificate=true;"
  }
}
```

**第四步：运行代码生成器读取表结构**
```bash
# 1. 进入代码生成器目录
cd src/Core/Infrastructure/CYSF.Generate

# 2. 运行代码生成器（读取已有表结构）
dotnet run

# 输出示例：
# === CYSF 代码生成器 ===
# 开始时间: 2024-01-15 14:30:25
# 连接数据库: CYSF_Dev
# 🔍 正在读取数据库表结构...
# 📋 发现表: Tenant (租户表)
# 📋 发现表: TenantUser (用户表)
# 📋 发现表: Role (角色表)
# 📋 发现表: UserRole (用户角色关联表)
# 🔄 正在生成代码文件...
# ✅ 生成 Models/Entities/Tenant.cs
# ✅ 生成 Models/Entities/TenantUser.cs
# ✅ 生成 Models/Entities/Role.cs
# ✅ 生成 Models/Entities/UserRole.cs
# ✅ 生成 Repositories/TenantRepository.cs
# ✅ 生成 Services/TenantService.cs
# ✅ 代码生成完成!
```

**代码生成器的工作原理：**
```csharp
// 代码生成器核心逻辑
public class DatabaseFirstGenerator
{
    public async Task GenerateAsync()
    {
        // 1. 连接数据库
        using var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();

        // 2. 读取所有表信息
        var tables = await GetTablesAsync(connection);

        foreach (var table in tables)
        {
            // 3. 读取表的列信息
            var columns = await GetColumnsAsync(connection, table.TableName);

            // 4. 根据表结构生成实体类
            await GenerateEntityAsync(table, columns);

            // 5. 生成仓储类
            await GenerateRepositoryAsync(table, columns);

            // 6. 生成服务类
            await GenerateServiceAsync(table, columns);

            // 7. 生成枚举（如果字段有枚举值）
            await GenerateEnumsAsync(table, columns);
        }
    }

    private async Task<List<TableInfo>> GetTablesAsync(SqlConnection connection)
    {
        var sql = @"
            SELECT TABLE_NAME, TABLE_COMMENT
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_TYPE = 'BASE TABLE'";

        return await connection.QueryAsync<TableInfo>(sql);
    }

    private async Task<List<ColumnInfo>> GetColumnsAsync(SqlConnection connection, string tableName)
    {
        var sql = @"
            SELECT
                COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT,
                COLUMN_COMMENT
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = @tableName
            ORDER BY ORDINAL_POSITION";

        return await connection.QueryAsync<ColumnInfo>(sql, new { tableName });
    }
}
```

**重要提醒：**
```
❌ 错误理解：代码生成器创建数据库表
✅ 正确理解：代码生成器读取已有表结构，生成对应代码

Database First 流程：
数据库表 → 代码生成器 → C# 代码文件

Code First 流程：
C# 实体类 → ORM迁移 → 数据库表
```

###  4.3. <a name='-1'></a>3.3 项目启动和验证 (45分钟)

**启动API项目：**
```bash
# 1. 进入API项目目录
cd src/Core/Api/Api.Backend

# 2. 启动项目
dotnet run

# 输出示例：
# info: Microsoft.Hosting.Lifetime[14]
#       Now listening on: http://127.0.0.1:2021
# info: Microsoft.Hosting.Lifetime[0]
#       Application started. Press Ctrl+C to shut down.
```

**验证各个功能模块：**

**1. API文档验证：**
```
访问: http://127.0.0.1:2021/swagger

应该看到：
✅ Swagger UI界面
✅ 所有API接口列表
✅ 可以展开查看接口详情
✅ 可以直接测试接口
```

**2. 健康检查验证：**
```bash
# 完整健康检查
curl http://127.0.0.1:2021/health

# 预期响应：
{
  "status": "Healthy",
  "duration": 45.2,
  "timestamp": "2024-01-15T14:30:25Z",
  "checks": [
    {
      "name": "application",
      "status": "Healthy",
      "duration": 1.2,
      "description": "应用程序运行正常"
    },
    {
      "name": "database",
      "status": "Healthy",
      "duration": 15.8,
      "description": "数据库连接正常"
    }
  ]
}
```

**3. Hangfire Dashboard验证：**
```
访问: http://127.0.0.1:2021/hangfire
用户名: admin
密码: admin123

应该看到：
✅ Hangfire Dashboard界面
✅ 任务统计信息
✅ 服务器状态
✅ 队列信息
```

**4. 雪花ID生成验证：**
```bash
# 测试雪花ID生成
curl http://127.0.0.1:2021/api/snowflake/next

# 预期响应：
{
  "code": 200,
  "message": "",
  "data": {
    "id": 1234567890123456789,
    "stringId": "1234567890123456789"
  }
}
```

**常见启动问题解决：**

**问题1：端口被占用**
```bash
# 检查端口占用
netstat -ano | findstr :2021

# 解决方案：
# 1. 杀掉占用进程
taskkill /PID [进程ID] /F

# 2. 或修改端口配置
# 在appsettings.Development.json中修改urls配置
```

**问题2：数据库连接失败**
```bash
# 检查SQL Server服务
net start MSSQLSERVER

# 测试连接
sqlcmd -S localhost -E -Q "SELECT 1"

# 检查连接字符串格式
```

**问题3：缓存服务异常**
```json
// 如果Redis连接失败，临时切换到Memory缓存
{
  "Cache": {
    "Provider": "Memory",
    "Enabled": true
  }
}
```

---

##  5. <a name='4'></a>💻 第四部分：业务开发实战演练 (4小时)

###  5.1. <a name='-1'></a>4.1 完整业务模块开发 - 产品管理 (2小时)

**需求分析：**
- 产品基本信息管理（增删改查）
- 产品分类管理
- 产品库存管理
- 产品价格历史记录

**第一步：数据库物理模型设计和表创建**

**为什么要使用NOT NULL和DEFAULT值？**

```sql
-- 错误示例：没有约束的表设计
CREATE TABLE [Product_Bad] (
    [Id] bigint,                       -- 没有NOT NULL，可能插入NULL值导致查询异常
    [TenantId] int,                    -- 没有NOT NULL，多租户数据可能混乱
    [Code] nvarchar(50),               -- 没有NOT NULL，业务逻辑可能出错
    [Stock] int,                       -- 没有DEFAULT，插入时必须指定值
    [Status] int                       -- 没有DEFAULT，状态不明确
);

-- 正确示例：完整约束的表设计
CREATE TABLE [Product] (
    [Id] bigint NOT NULL PRIMARY KEY,                    -- 主键必须NOT NULL
    [TenantId] int NOT NULL,                            -- 租户ID必须有值，保证数据隔离
    [CategoryId] int NOT NULL,                          -- 分类ID必须有值，保证数据完整性
    [Code] nvarchar(50) NOT NULL,                       -- 产品编码必须有值，业务唯一标识
    [Name] nvarchar(100) NOT NULL,                      -- 产品名称必须有值，显示必需
    [Description] nvarchar(500) NULL,                   -- 描述可以为空，非必填信息
    [Price] decimal(18,2) NOT NULL DEFAULT 0.00,        -- 价格必须有值，默认0避免计算错误
    [Stock] int NOT NULL DEFAULT 0,                     -- 库存必须有值，默认0表示无库存
    [MinStock] int NOT NULL DEFAULT 0,                  -- 最小库存必须有值，默认0
    [Status] int NOT NULL DEFAULT 1,                    -- 状态必须有值，默认1表示正常
    [CreateBy] int NOT NULL,                            -- 创建人必须有值，审计需要
    [CreateTime] datetime NOT NULL DEFAULT GETDATE(),   -- 创建时间必须有值，默认当前时间
    [UpdateBy] int NULL,                                -- 更新人可以为空，创建时还没有更新
    [UpdateTime] datetime NULL                          -- 更新时间可以为空，创建时还没有更新
);
```

**NOT NULL vs NULL 的区别：**

| 方面 | NOT NULL | NULL |
|------|----------|------|
| **数据完整性** | 保证字段必须有值 | 允许空值，可能导致业务逻辑错误 |
| **查询性能** | 索引效率高，查询快 | 需要处理NULL值，查询复杂 |
| **业务逻辑** | 简化代码，无需判空 | 需要大量NULL值检查 |
| **计算操作** | 可以直接计算 | NULL参与计算结果为NULL |
| **存储空间** | 固定空间 | 需要额外标记位 |

**DEFAULT值的重要性：**
```csharp
// 没有DEFAULT值的问题
public async Task CreateProductAsync(Product product)
{
    // 必须手动设置每个字段，容易遗漏
    product.Stock = product.Stock ?? 0;        // 手动处理默认值
    product.MinStock = product.MinStock ?? 0;  // 手动处理默认值
    product.Status = product.Status ?? 1;      // 手动处理默认值
    product.CreateTime = DateTime.Now;         // 手动设置时间

    await _repository.CreateAsync(product);
}

// 有DEFAULT值的优势
public async Task CreateProductAsync(Product product)
{
    // 数据库自动处理默认值，代码更简洁
    // 只需要设置必要的业务字段
    product.Id = SnowflakeHelper.NextId();
    product.CreateBy = _contextUser.UserId;

    await _repository.CreateAsync(product); // 其他字段数据库自动设置默认值
}
```

**字段注释的重要性：**

**为什么每个字段都必须添加注释？**

1. **代码生成器依赖**：
   - 代码生成器会读取字段注释生成XML文档注释
   - 自动生成的实体类、DTO类都会包含注释
   - API文档（Swagger）会显示字段说明

2. **团队协作需要**：
   - 新人快速理解字段含义
   - 减少沟通成本和理解偏差
   - 便于代码审查和维护

3. **业务文档化**：
   - 数据库即文档，字段注释就是业务说明
   - 便于后期维护和功能扩展
   - 符合企业级开发规范

**字段注释规范：**
```sql
-- 错误示例：没有注释或注释不清晰
CREATE TABLE [Product] (
    [Id] bigint NOT NULL,              -- 主键
    [Code] nvarchar(50) NOT NULL,      -- 编码
    [Status] int NOT NULL DEFAULT 1    -- 状态
);

-- 正确示例：详细清晰的注释
CREATE TABLE [Product] (
    [Id] bigint NOT NULL,                            -- 产品ID，雪花ID算法生成，全局唯一
    [Code] nvarchar(50) NOT NULL,                    -- 产品编码，业务唯一标识，格式：PROD001，用于显示和查询
    [Status] int NOT NULL DEFAULT 1                 -- 产品状态：1-正常（可销售），2-停用（不可销售），默认正常状态
);
```

**完整的表结构设计（必须添加字段说明）：**
```sql
-- 产品表
CREATE TABLE [Product] (
    [Id] bigint NOT NULL PRIMARY KEY,                    -- 产品ID，雪花ID算法生成
    [TenantId] int NOT NULL,                            -- 租户ID，多租户数据隔离
    [CategoryId] int NOT NULL,                          -- 产品分类ID，关联ProductCategory表
    [Code] nvarchar(50) NOT NULL,                       -- 产品编码，业务唯一标识，如：PROD001
    [Name] nvarchar(100) NOT NULL,                      -- 产品名称，显示用
    [Description] nvarchar(500) NULL,                   -- 产品描述，详细说明信息
    [Price] decimal(18,2) NOT NULL DEFAULT 0.00,        -- 当前销售价格，单位：元
    [Stock] int NOT NULL DEFAULT 0,                     -- 当前库存数量
    [MinStock] int NOT NULL DEFAULT 0,                  -- 最小库存预警值
    [Status] int NOT NULL DEFAULT 1,                    -- 产品状态：1-正常，2-停用
    [CreateBy] int NOT NULL,                            -- 创建人用户ID
    [CreateTime] datetime NOT NULL DEFAULT GETDATE(),   -- 创建时间
    [UpdateBy] int NULL,                                -- 最后更新人用户ID
    [UpdateTime] datetime NULL,                         -- 最后更新时间

    -- 索引设计
    INDEX IX_Product_TenantId_Code (TenantId, Code),     -- 租户+编码唯一索引
    INDEX IX_Product_TenantId_CategoryId (TenantId, CategoryId), -- 分类查询索引
    INDEX IX_Product_TenantId_Status (TenantId, Status)  -- 状态查询索引
);

-- 产品分类表
CREATE TABLE [ProductCategory] (
    [Id] int IDENTITY(1,1) PRIMARY KEY,                 -- 分类ID，自增主键
    [TenantId] int NOT NULL,                            -- 租户ID，多租户数据隔离
    [ParentId] int NULL,                                -- 父分类ID，支持树形结构，顶级分类为NULL
    [Name] nvarchar(50) NOT NULL,                       -- 分类名称
    [Sort] int NOT NULL DEFAULT 0,                      -- 排序号，数字越小越靠前
    [Status] int NOT NULL DEFAULT 1,                    -- 分类状态：1-启用，2-禁用
    [CreateTime] datetime NOT NULL DEFAULT GETDATE(),   -- 创建时间

    -- 索引设计
    INDEX IX_ProductCategory_TenantId_ParentId (TenantId, ParentId), -- 树形查询索引
    INDEX IX_ProductCategory_TenantId_Sort (TenantId, Sort)          -- 排序查询索引
);

-- 产品价格历史表
CREATE TABLE [ProductPriceHistory] (
    [Id] bigint NOT NULL PRIMARY KEY,                   -- 历史记录ID，雪花ID算法生成
    [ProductId] bigint NOT NULL,                        -- 产品ID，关联Product表
    [OldPrice] decimal(18,2) NULL,                      -- 原价格，创建产品时为NULL
    [NewPrice] decimal(18,2) NOT NULL,                  -- 新价格
    [ChangeReason] nvarchar(200) NULL,                  -- 价格变更原因
    [CreateBy] int NOT NULL,                            -- 操作人用户ID
    [CreateTime] datetime NOT NULL DEFAULT GETDATE(),   -- 变更时间

    -- 索引设计
    INDEX IX_ProductPriceHistory_ProductId_CreateTime (ProductId, CreateTime) -- 产品价格历史查询索引
);
```

**第二步：执行DDL脚本创建表结构**

```sql
-- 在数据库中执行以下DDL脚本
USE CYSF_Dev;

-- 产品分类表（先创建，因为Product表要引用）
CREATE TABLE [ProductCategory] (
    [Id] int IDENTITY(1,1) PRIMARY KEY,                 -- 分类ID，自增主键
    [TenantId] int NOT NULL,                            -- 租户ID，多租户数据隔离
    [ParentId] int NULL,                                -- 父分类ID，支持树形结构，顶级分类为NULL
    [Name] nvarchar(50) NOT NULL,                       -- 分类名称
    [Sort] int NOT NULL DEFAULT 0,                      -- 排序号，数字越小越靠前
    [Status] int NOT NULL DEFAULT 1,                    -- 分类状态：1-启用，2-禁用
    [CreateTime] datetime NOT NULL DEFAULT GETDATE(),   -- 创建时间

    -- 索引设计
    INDEX IX_ProductCategory_TenantId_ParentId (TenantId, ParentId), -- 树形查询索引
    INDEX IX_ProductCategory_TenantId_Sort (TenantId, Sort)          -- 排序查询索引
);

-- 产品表
CREATE TABLE [Product] (
    [Id] bigint NOT NULL PRIMARY KEY,                    -- 产品ID，雪花ID算法生成
    [TenantId] int NOT NULL,                            -- 租户ID，多租户数据隔离
    [CategoryId] int NOT NULL,                          -- 产品分类ID，关联ProductCategory表
    [Code] nvarchar(50) NOT NULL,                       -- 产品编码，业务唯一标识，如：PROD001
    [Name] nvarchar(100) NOT NULL,                      -- 产品名称，显示用
    [Description] nvarchar(500) NULL,                   -- 产品描述，详细说明信息
    [Price] decimal(18,2) NOT NULL DEFAULT 0.00,        -- 当前销售价格，单位：元
    [Stock] int NOT NULL DEFAULT 0,                     -- 当前库存数量
    [MinStock] int NOT NULL DEFAULT 0,                  -- 最小库存预警值
    [Status] int NOT NULL DEFAULT 1,                    -- 产品状态：1-正常，2-停用
    [CreateBy] int NOT NULL,                            -- 创建人用户ID
    [CreateTime] datetime NOT NULL DEFAULT GETDATE(),   -- 创建时间
    [UpdateBy] int NULL,                                -- 最后更新人用户ID
    [UpdateTime] datetime NULL,                         -- 最后更新时间

    -- 索引设计
    INDEX IX_Product_TenantId_Code (TenantId, Code),     -- 租户+编码唯一索引
    INDEX IX_Product_TenantId_CategoryId (TenantId, CategoryId), -- 分类查询索引
    INDEX IX_Product_TenantId_Status (TenantId, Status)  -- 状态查询索引
);

-- 产品价格历史表
CREATE TABLE [ProductPriceHistory] (
    [Id] bigint NOT NULL PRIMARY KEY,                   -- 历史记录ID，雪花ID算法生成
    [ProductId] bigint NOT NULL,                        -- 产品ID，关联Product表
    [OldPrice] decimal(18,2) NULL,                      -- 原价格，创建产品时为NULL
    [NewPrice] decimal(18,2) NOT NULL,                  -- 新价格
    [ChangeReason] nvarchar(200) NULL,                  -- 价格变更原因
    [CreateBy] int NOT NULL,                            -- 操作人用户ID
    [CreateTime] datetime NOT NULL DEFAULT GETDATE(),   -- 变更时间

    -- 索引设计
    INDEX IX_ProductPriceHistory_ProductId_CreateTime (ProductId, CreateTime) -- 产品价格历史查询索引
);
```

**第三步：验证表结构创建**
```sql
-- 验证表是否创建成功
SELECT
    TABLE_NAME as '表名',
    TABLE_ROWS as '行数'
FROM INFORMATION_SCHEMA.TABLES
WHERE TABLE_SCHEMA = 'CYSF_Dev'
  AND TABLE_NAME IN ('Product', 'ProductCategory', 'ProductPriceHistory')
ORDER BY TABLE_NAME;

-- 验证字段结构
SELECT
    TABLE_NAME as '表名',
    COLUMN_NAME as '字段名',
    DATA_TYPE as '数据类型',
    IS_NULLABLE as '允许空值',
    COLUMN_DEFAULT as '默认值',
    COLUMN_COMMENT as '字段注释'
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = 'CYSF_Dev'
  AND TABLE_NAME IN ('Product', 'ProductCategory', 'ProductPriceHistory')
ORDER BY TABLE_NAME, ORDINAL_POSITION;
```

**第四步：运行代码生成器读取表结构**
```bash
# 1. 确保数据库表已创建
# 2. 进入代码生成器目录
cd src/Core/Infrastructure/CYSF.Generate

# 3. 运行代码生成器（读取已有表结构）
dotnet run

# 输出示例：
# === CYSF 代码生成器 ===
# 🔍 正在读取数据库表结构...
# 📋 发现表: Product (产品表)
# 📋 发现表: ProductCategory (产品分类表)
# 📋 发现表: ProductPriceHistory (产品价格历史表)
# 🔄 正在生成代码文件...
# ✅ 生成完成!
```

**第五步：查看生成的代码文件**

**生成的文件结构：**
```
代码生成器读取表结构后生成的文件：
├── Models/Entities/
│   ├── Product.cs                    -- 产品实体类（根据Product表生成）
│   ├── ProductCategory.cs            -- 产品分类实体类（根据ProductCategory表生成）
│   └── ProductPriceHistory.cs        -- 价格历史实体类（根据ProductPriceHistory表生成）
├── Models/Enums/
│   └── ProductStatus.cs              -- 产品状态枚举（根据Status字段生成）
├── Models/Request/Product/
│   ├── CreateProductReq.cs           -- 创建产品请求模型
│   ├── UpdateProductReq.cs           -- 更新产品请求模型
│   └── ProductPageListReq.cs         -- 产品分页查询请求模型
├── Models/Response/Product/
│   └── ProductDto.cs                 -- 产品响应模型
├── Repositories/
│   ├── ProductRepository.cs          -- 产品仓储类（包含基础CRUD）
│   ├── ProductCategoryRepository.cs  -- 分类仓储类
│   └── ProductPriceHistoryRepository.cs -- 价格历史仓储类
├── Services/
│   ├── ProductService.cs             -- 产品业务服务类
│   ├── ProductCategoryService.cs     -- 分类业务服务类
│   └── ProductPriceHistoryService.cs -- 价格历史服务类
└── Application/
    ├── ProductApp.cs                 -- 产品应用服务类
    ├── ProductCategoryApp.cs         -- 分类应用服务类
    └── ProductPriceHistoryApp.cs     -- 价格历史应用服务类
```

**Database First 的优势总结：**
```
✅ 数据库设计专业化：DBA可以设计复杂的约束、索引、触发器
✅ 性能优化：数据库层面的优化更直接有效
✅ 代码标准化：生成的代码结构统一，减少人为错误
✅ 开发效率：表结构确定后，快速生成所有基础代码
✅ 团队协作：数据库设计和代码开发可以并行进行
✅ 维护便利：表结构变更时重新生成代码即可

Database First vs Code First：
Database First: 表结构 → 生成代码 → 业务开发
Code First: 实体类 → 生成表结构 → 业务开发

CYSF选择Database First的原因：
1. 企业级项目需要DBA参与数据库设计
2. 复杂的业务逻辑需要数据库层面的约束
3. 性能要求高，需要专业的索引设计
4. 团队分工明确，提高开发效率
```

**第六步：查看和完善生成的代码**

**代码生成器自动生成的实体类：**
```csharp
// Models/Entities/Product.cs (代码生成器自动生成)
[SugarTable("Product")]
public class Product
{
    /// <summary>
    /// 产品ID，雪花ID算法生成
    /// </summary>
    [SugarColumn(IsPrimaryKey = true)]
    public long Id { get; set; }

    /// <summary>
    /// 租户ID，多租户数据隔离
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public int TenantId { get; set; }

    /// <summary>
    /// 产品分类ID，关联ProductCategory表
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public int CategoryId { get; set; }

    /// <summary>
    /// 产品编码，业务唯一标识，如：PROD001
    /// </summary>
    [SugarColumn(Length = 50, IsNullable = false)]
    public string Code { get; set; }

    /// <summary>
    /// 产品名称，显示用
    /// </summary>
    [SugarColumn(Length = 100, IsNullable = false)]
    public string Name { get; set; }

    /// <summary>
    /// 产品描述，详细说明信息
    /// </summary>
    [SugarColumn(Length = 500, IsNullable = true)]
    public string Description { get; set; }

    /// <summary>
    /// 当前销售价格，单位：元
    /// </summary>
    [SugarColumn(ColumnDataType = "decimal(18,2)", IsNullable = false)]
    public decimal Price { get; set; }

    /// <summary>
    /// 当前库存数量
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public int Stock { get; set; }

    /// <summary>
    /// 最小库存预警值
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public int MinStock { get; set; }

    /// <summary>
    /// 产品状态：1-正常，2-停用
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public ProductStatus Status { get; set; }

    /// <summary>
    /// 创建人用户ID
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public int CreateBy { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [SugarColumn(IsNullable = false)]
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 最后更新人用户ID
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public int? UpdateBy { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    [SugarColumn(IsNullable = true)]
    public DateTime? UpdateTime { get; set; }

    // 导航属性（不映射到数据库）
    [SugarColumn(IsIgnore = true)]
    public ProductCategory Category { get; set; }

    // 计算属性
    [SugarColumn(IsIgnore = true)]
    public bool IsLowStock => Stock <= MinStock;
}

// Models/Enums/ProductStatus.cs (代码生成器自动生成)
/// <summary>
/// 产品状态枚举
/// </summary>
public enum ProductStatus
{
    /// <summary>
    /// 正常
    /// </summary>
    Normal = 1,

    /// <summary>
    /// 停用
    /// </summary>
    Disabled = 2
}
```

**手动扩展业务方法（在生成的实体类基础上扩展）：**
```csharp
// Models/Entities/Product.cs (手动扩展部分)
public partial class Product
{
    /// <summary>
    /// 更新产品价格
    /// </summary>
    /// <param name="newPrice">新价格</param>
    /// <param name="reason">变更原因</param>
    /// <param name="operatorId">操作人ID</param>
    /// <returns>价格历史记录</returns>
    public ProductPriceHistory UpdatePrice(decimal newPrice, string reason, int operatorId)
    {
        if (newPrice <= 0)
            throw new BusinessException("价格必须大于0");

        if (newPrice == Price)
            return null; // 价格未变化，不需要记录历史

        // 创建价格变更历史记录
        var history = new ProductPriceHistory
        {
            Id = SnowflakeHelper.NextId(),
            ProductId = Id,
            OldPrice = Price,
            NewPrice = newPrice,
            ChangeReason = reason,
            CreateBy = operatorId,
            CreateTime = DateTime.Now
        };

        // 更新产品价格
        Price = newPrice;
        UpdateBy = operatorId;
        UpdateTime = DateTime.Now;

        return history;
    }

    /// <summary>
    /// 更新库存
    /// </summary>
    /// <param name="quantity">数量</param>
    /// <param name="operation">操作类型：IN-入库，OUT-出库</param>
    public void UpdateStock(int quantity, string operation)
    {
        if (quantity <= 0)
            throw new BusinessException("数量必须大于0");

        switch (operation.ToUpper())
        {
            case "IN":  // 入库
                Stock += quantity;
                break;
            case "OUT": // 出库
                if (Stock < quantity)
                    throw new BusinessException($"库存不足，当前库存：{Stock}，需要出库：{quantity}");
                Stock -= quantity;
                break;
            default:
                throw new ArgumentException($"无效的库存操作类型：{operation}，只支持IN或OUT");
        }
    }

    /// <summary>
    /// 验证产品数据完整性
    /// </summary>
    public void Validate()
    {
        if (string.IsNullOrWhiteSpace(Code))
            throw new BusinessException("产品编码不能为空");

        if (string.IsNullOrWhiteSpace(Name))
            throw new BusinessException("产品名称不能为空");

        if (Price < 0)
            throw new BusinessException("产品价格不能为负数");

        if (Stock < 0)
            throw new BusinessException("库存数量不能为负数");

        if (MinStock < 0)
            throw new BusinessException("最小库存不能为负数");
    }
}
```

**Database First vs Code First 对比：**

| 方面 | Database First (CYSF采用) | Code First |
|------|---------------------------|------------|
| **设计起点** | 数据库表结构 | 实体类代码 |
| **适用场景** | 企业级项目、DBA主导 | 快速原型、开发主导 |
| **数据库设计** | 专业工具设计，约束完整 | 代码生成，约束有限 |
| **团队协作** | DBA和开发分工明确 | 开发人员主导 |
| **版本控制** | DDL脚本版本控制 | 迁移文件版本控制 |
| **性能优化** | 索引设计更专业 | 依赖ORM自动生成 |

**为什么CYSF选择Database First？**

1. **企业级项目需求**：
   - 数据库设计需要DBA专业参与
   - 复杂的约束和索引设计
   - 严格的数据完整性要求

2. **团队协作优势**：
   - DBA负责数据库设计和优化
   - 开发人员专注业务逻辑实现
   - 职责分工明确，效率更高

3. **代码质量保证**：
   - 代码生成器保证代码规范统一
   - 减少手写代码的错误
   - 自动生成完整的CRUD操作

4. **维护便利性**：
   - 数据库结构变更时重新生成代码
   - 保持数据库和代码的一致性
   - 减少手动同步的工作量

**第七步：扩展仓储层（在生成的基础上）**
```csharp
// Repositories/ProductRepository.cs
public class ProductRepository
{
    private readonly SqlSugarScope _db;

    public ProductRepository(SqlSugarScope db)
    {
        _db = db;
    }

    // 基础CRUD方法（代码生成器已生成）

    // 自定义查询方法
    public async Task<bool> ExistsByCodeAsync(string code, int tenantId, long? excludeId = null)
    {
        var query = _db.Queryable<Product>()
            .Where(x => x.Code == code && x.TenantId == tenantId);

        if (excludeId.HasValue)
            query = query.Where(x => x.Id != excludeId.Value);

        return await query.AnyAsync();
    }

    public async Task<List<Product>> GetLowStockProductsAsync(int tenantId)
    {
        return await _db.Queryable<Product>()
            .LeftJoin<ProductCategory>((p, c) => p.CategoryId == c.Id)
            .Where(p => p.TenantId == tenantId && p.Stock <= p.MinStock)
            .Select((p, c) => new Product
            {
                Id = p.Id,
                Code = p.Code,
                Name = p.Name,
                Stock = p.Stock,
                MinStock = p.MinStock,
                Category = c
            })
            .ToListAsync();
    }

    public async Task<PagedResult<Product>> GetPagedListAsync(ProductPageListReq req, int tenantId)
    {
        var query = _db.Queryable<Product>()
            .LeftJoin<ProductCategory>((p, c) => p.CategoryId == c.Id)
            .Where(p => p.TenantId == tenantId);

        // 条件过滤
        if (!string.IsNullOrEmpty(req.Keyword))
        {
            query = query.Where(p => p.Code.Contains(req.Keyword) || p.Name.Contains(req.Keyword));
        }

        if (req.CategoryId.HasValue)
        {
            query = query.Where(p => p.CategoryId == req.CategoryId.Value);
        }

        if (req.Status.HasValue)
        {
            query = query.Where(p => p.Status == req.Status.Value);
        }

        if (req.LowStock == true)
        {
            query = query.Where(p => p.Stock <= p.MinStock);
        }

        // 排序
        if (!string.IsNullOrEmpty(req.SortField))
        {
            var orderBy = req.SortOrder?.ToUpper() == "DESC" ? OrderByType.Desc : OrderByType.Asc;
            query = query.OrderBy(req.SortField, orderBy);
        }
        else
        {
            query = query.OrderBy(p => p.CreateTime, OrderByType.Desc);
        }

        // 分页查询
        RefAsync<int> totalCount = 0;
        var items = await query
            .Select((p, c) => new Product
            {
                Id = p.Id,
                Code = p.Code,
                Name = p.Name,
                Description = p.Description,
                Price = p.Price,
                Stock = p.Stock,
                MinStock = p.MinStock,
                Status = p.Status,
                CreateTime = p.CreateTime,
                Category = c
            })
            .ToPageListAsync(req.PageIndex, req.PageSize, totalCount);

        return new PagedResult<Product>
        {
            Items = items,
            TotalCount = totalCount.Value,
            PageIndex = req.PageIndex,
            PageSize = req.PageSize
        };
    }
}
```

**第八步：实现业务服务层（在生成的基础上扩展）**
```csharp
// Services/ProductService.cs
public class ProductService
{
    private readonly ProductRepository _productRepository;
    private readonly ProductCategoryRepository _categoryRepository;
    private readonly ProductPriceHistoryRepository _priceHistoryRepository;
    private readonly ContextUser _contextUser;
    private readonly ILogger<ProductService> _logger;

    public ProductService(
        ProductRepository productRepository,
        ProductCategoryRepository categoryRepository,
        ProductPriceHistoryRepository priceHistoryRepository,
        ContextUser contextUser,
        ILogger<ProductService> logger)
    {
        _productRepository = productRepository;
        _categoryRepository = categoryRepository;
        _priceHistoryRepository = priceHistoryRepository;
        _contextUser = contextUser;
        _logger = logger;
    }

    public async Task<Product> CreateAsync(Product product)
    {
        // 注意：不需要手动调用验证方法！
        // FluentValidation会在API层自动验证请求参数
        // 这里只需要处理业务逻辑验证

        // 设置基础信息
        product.Id = SnowflakeHelper.NextId();
        product.TenantId = _contextUser.TenantId;
        product.CreateBy = _contextUser.UserId;
        product.CreateTime = DateTime.Now;
        product.Status = ProductStatus.Normal;

        // 业务逻辑验证（不是数据格式验证）
        await ValidateBusinessRulesAsync(product);

        // 使用事务确保数据一致性
        using var transaction = await _db.BeginTransactionAsync();
        try
        {
            // 保存产品
            await _productRepository.CreateAsync(product);

            // 记录价格历史
            var priceHistory = new ProductPriceHistory
            {
                Id = SnowflakeHelper.NextId(),
                ProductId = product.Id,
                OldPrice = null,
                NewPrice = product.Price,
                ChangeReason = "产品创建",
                CreateBy = _contextUser.UserId,
                CreateTime = DateTime.Now
            };
            await _priceHistoryRepository.CreateAsync(priceHistory);

            // 提交事务
            await transaction.CommitAsync();

            _logger.LogInformation("产品创建成功: {ProductCode} - {ProductName}", product.Code, product.Name);

            return product;
        }
        catch
        {
            // 异常时自动回滚
            await transaction.RollbackAsync();
            throw;
        }
    }

    /// <summary>
    /// 业务规则验证（区别于FluentValidation的数据格式验证）
    /// </summary>
    private async Task ValidateBusinessRulesAsync(Product product, long? excludeId = null)
    {
        // 验证产品编码唯一性（业务规则）
        if (await _productRepository.ExistsByCodeAsync(product.Code, _contextUser.TenantId, excludeId))
            throw new BusinessException("产品编码已存在");

        // 验证分类是否存在且属于当前租户（业务规则）
        var category = await _categoryRepository.GetByIdAsync(product.CategoryId);
        if (category == null || category.TenantId != _contextUser.TenantId)
            throw new BusinessException("产品分类不存在或无权限访问");

        // 其他业务规则验证...
    }

    public async Task<Product> UpdateAsync(Product product)
    {
        // 获取原产品信息
        var existingProduct = await _productRepository.GetByIdAsync(product.Id);
        if (existingProduct == null || existingProduct.TenantId != _contextUser.TenantId)
            throw new NotFoundException("产品不存在");

        // 业务规则验证（FluentValidation已在API层完成数据格式验证）
        await ValidateBusinessRulesAsync(product, product.Id);

        // 更新产品信息
        product.TenantId = existingProduct.TenantId; // 保持租户ID不变
        product.CreateBy = existingProduct.CreateBy; // 保持创建人不变
        product.CreateTime = existingProduct.CreateTime; // 保持创建时间不变
        product.UpdateBy = _contextUser.UserId;
        product.UpdateTime = DateTime.Now;

        // 使用事务确保产品更新和价格历史记录的一致性
        using var transaction = await _db.BeginTransactionAsync();
        try
        {
            // 更新产品
            await _productRepository.UpdateAsync(product);

            // 如果价格发生变更，记录价格历史
            if (existingProduct.Price != product.Price)
            {
                var priceHistory = new ProductPriceHistory
                {
                    Id = SnowflakeHelper.NextId(),
                    ProductId = product.Id,
                    OldPrice = existingProduct.Price,
                    NewPrice = product.Price,
                    ChangeReason = "产品更新",
                    CreateBy = _contextUser.UserId,
                    CreateTime = DateTime.Now
                };
                await _priceHistoryRepository.CreateAsync(priceHistory);
            }

            await transaction.CommitAsync();

            _logger.LogInformation("产品更新成功: {ProductCode} - {ProductName}", product.Code, product.Name);

            return product;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    public async Task DeleteAsync(long id)
    {
        var product = await _productRepository.GetByIdAsync(id);
        if (product == null || product.TenantId != _contextUser.TenantId)
            throw new NotFoundException("产品不存在");

        // 检查是否可以删除（例如：是否有关联的订单）
        // var hasOrders = await _orderRepository.ExistsByProductIdAsync(id);
        // if (hasOrders)
        //     throw new BusinessException("该产品已有关联订单，无法删除");

        await _productRepository.DeleteAsync(id);

        _logger.LogInformation("产品删除成功: {ProductCode} - {ProductName}", product.Code, product.Name);
    }

    public async Task<Product> GetByIdAsync(long id)
    {
        var product = await _productRepository.GetByIdAsync(id);
        if (product == null || product.TenantId != _contextUser.TenantId)
            throw new NotFoundException("产品不存在");

        return product;
    }

    public async Task<PagedResult<Product>> GetPagedListAsync(ProductPageListReq req)
    {
        return await _productRepository.GetPagedListAsync(req, _contextUser.TenantId);
    }

    public async Task<List<Product>> GetLowStockProductsAsync()
    {
        return await _productRepository.GetLowStockProductsAsync(_contextUser.TenantId);
    }

    public async Task UpdateStockAsync(long productId, int quantity, string operation, string reason)
    {
        var product = await GetByIdAsync(productId);

        var oldStock = product.Stock;
        product.UpdateStock(quantity, operation);
        product.UpdateBy = _contextUser.UserId;
        product.UpdateTime = DateTime.Now;

        await _productRepository.UpdateAsync(product);

        _logger.LogInformation("库存更新: {ProductCode} {Operation} {Quantity}, 原库存: {OldStock}, 新库存: {NewStock}, 原因: {Reason}",
            product.Code, operation, quantity, oldStock, product.Stock, reason);
    }

    /// <summary>
    /// FluentValidation vs 业务规则验证的区别说明
    /// </summary>
    /// <remarks>
    /// FluentValidation负责：
    /// - 数据格式验证（非空、长度、正则表达式等）
    /// - 数据类型验证（数字范围、日期格式等）
    /// - 基础业务规则验证（如价格必须大于0）
    ///
    /// 业务规则验证负责：
    /// - 需要查询数据库的验证（如编码唯一性）
    /// - 复杂的业务逻辑验证（如库存充足性）
    /// - 权限相关的验证（如数据归属验证）
    /// </remarks>
}
```

**第九步：实现应用服务层（在生成的基础上扩展）**
```csharp
// Application/ProductApp.cs
public class ProductApp
{
    private readonly ProductService _productService;
    private readonly CacheHelper _cacheHelper;
    private readonly ILogger<ProductApp> _logger;

    public ProductApp(
        ProductService productService,
        CacheHelper cacheHelper,
        ILogger<ProductApp> logger)
    {
        _productService = productService;
        _cacheHelper = cacheHelper;
        _logger = logger;
    }

    public async Task<ProductDto> CreateAsync(CreateProductReq req)
    {
        // 注意：FluentValidation已在API层自动完成参数验证
        // 这里直接进行业务逻辑处理

        // 使用Mapster进行对象映射（请求 -> 实体）
        var product = req.Adapt<Product>();

        // 调用业务服务
        var created = await _productService.CreateAsync(product);

        // 清除相关缓存
        await ClearProductCacheAsync();

        // 使用Mapster映射返回结果（实体 -> DTO）
        return created.Adapt<ProductDto>();
    }

    public async Task<ProductDto> UpdateAsync(UpdateProductReq req)
    {
        // FluentValidation自动验证完成，无需手动验证

        // 使用Mapster进行对象映射
        var product = req.Adapt<Product>();

        var updated = await _productService.UpdateAsync(product);

        // 清除相关缓存
        await ClearProductCacheAsync(req.Id);

        // 映射返回结果，Mapster会自动处理复杂映射
        return updated.Adapt<ProductDto>();
    }

    public async Task DeleteAsync(long id)
    {
        await _productService.DeleteAsync(id);

        // 清除相关缓存
        await ClearProductCacheAsync(id);
    }

    public async Task<ProductDto> GetByIdAsync(long id)
    {
        var product = await GetProductFromCacheAsync(id);
        return product.Adapt<ProductDto>();
    }

    public async Task<PagedResult<ProductDto>> GetPagedListAsync(PageListReq<ProductPageListReq> req)
    {
        var result = await _productService.GetPagedListAsync(req.Data);

        return new PagedResult<ProductDto>
        {
            Items = result.Items.Adapt<List<ProductDto>>(),
            TotalCount = result.TotalCount,
            PageIndex = result.PageIndex,
            PageSize = result.PageSize
        };
    }

    public async Task<List<ProductDto>> GetLowStockProductsAsync()
    {
        var cacheKey = CacheKey.LowStockProducts(_contextUser.TenantId);

        var products = await _cacheHelper.GetOrSetAsync(
            cacheKey,
            () => _productService.GetLowStockProductsAsync(),
            TimeSpan.FromMinutes(10) // 低库存产品缓存10分钟
        );

        return products.Adapt<List<ProductDto>>();
    }

    public async Task UpdateStockAsync(UpdateStockReq req)
    {
        await _productService.UpdateStockAsync(req.ProductId, req.Quantity, req.Operation, req.Reason);

        // 清除相关缓存
        await ClearProductCacheAsync(req.ProductId);
    }

    // 缓存管理
    private async Task<Product> GetProductFromCacheAsync(long id)
    {
        var cacheKey = CacheKey.Product(id);

        return await _cacheHelper.GetOrSetAsync(
            cacheKey,
            () => _productService.GetByIdAsync(id),
            TimeSpan.FromHours(1) // 产品信息缓存1小时
        );
    }

    private async Task ClearProductCacheAsync(long? productId = null)
    {
        var tasks = new List<Task>();

        if (productId.HasValue)
        {
            // 清除特定产品缓存
            tasks.Add(_cacheHelper.RemoveAsync(CacheKey.Product(productId.Value)));
        }

        // 清除低库存产品缓存
        tasks.Add(_cacheHelper.RemoveAsync(CacheKey.LowStockProducts(_contextUser.TenantId)));

        // 清除产品列表相关缓存（可以使用模式匹配）
        tasks.Add(_cacheHelper.RemoveByPatternAsync($"product:list:tenant:{_contextUser.TenantId}:*"));

        await Task.WhenAll(tasks);
    }
}
```

###  5.2. <a name='-1'></a>4.2 事务管理最佳实践 (30分钟)

**什么时候需要事务？**

在以下情况下必须使用事务：
- 多个数据库操作需要保证原子性
- 数据一致性要求高的业务场景
- 涉及多个表的关联操作

**事务管理的正确方式：**

```csharp
// 推荐方式1：在Service层使用 UseTranAsync
public async Task<Product> CreateProductWithHistoryAsync(Product product)
{
    var result = await _productRepo.Context.Ado.UseTranAsync(async () =>
    {
        // 1. 保存产品
        await _productRepository.CreateAsync(product);

        // 2. 记录价格历史
        var priceHistory = new ProductPriceHistory { ... };
        await _priceHistoryRepository.CreateAsync(priceHistory);

        return product;
    });

    return result.Data;
}

// 推荐方式2：App层调用Service层的组合方法
public async Task<ProductDto> CreateProductAsync(CreateProductReq req)
{
    // App层不直接操作数据库，调用Service层的组合方法
    var product = await _productService.CreateProductWithHistoryAsync(req.Adapt<Product>());
    return product.Adapt<ProductDto>();
}

// 复杂操作 - 使用 UnitOfWork（如果项目中有实现）
public async Task<Order> CreateOrderWithItemsAsync(Order order, List<OrderItem> items)
{
    await _unitOfWork.BeginTransactionAsync();
    try
    {
        // 1. 创建订单
        await _orderService.CreateAsync(order);

        // 2. 创建订单项
        foreach (var item in items)
        {
            await _orderItemService.CreateAsync(item);
        }

        // 3. 更新库存
        await _inventoryService.UpdateStockAsync(items);

        await _unitOfWork.CommitAsync();
        return order;
    }
    catch
    {
        await _unitOfWork.RollbackAsync();
        throw;
    }
}
```

**事务使用原则：**

1. **分层职责**：App层不直接操作数据库，事务管理在Service层
2. **最小事务范围**：只在必要的操作上使用事务
3. **避免长事务**：事务时间过长会影响并发性能
4. **异常处理**：确保异常时事务能正确回滚
5. **组合方法**：Service层提供组合方法，内部管理事务

**常见事务场景：**

```csharp
// 场景1：主从表操作
public async Task CreateOrderAsync(CreateOrderReq req)
{
    using var transaction = await _db.BeginTransactionAsync();
    try
    {
        // 创建订单主表
        var order = await _orderRepository.CreateAsync(orderEntity);

        // 创建订单明细
        foreach (var item in req.Items)
        {
            item.OrderId = order.Id;
            await _orderItemRepository.CreateAsync(item);
        }

        await transaction.CommitAsync();
    }
    catch
    {
        await transaction.RollbackAsync();
        throw;
    }
}

// 场景2：状态变更 + 日志记录
public async Task UpdateOrderStatusAsync(long orderId, OrderStatus newStatus)
{
    using var transaction = await _db.BeginTransactionAsync();
    try
    {
        // 更新订单状态
        await _orderRepository.UpdateStatusAsync(orderId, newStatus);

        // 记录状态变更日志
        var log = new OrderStatusLog
        {
            OrderId = orderId,
            OldStatus = oldStatus,
            NewStatus = newStatus,
            ChangeTime = DateTime.Now,
            ChangeBy = _contextUser.UserId
        };
        await _orderStatusLogRepository.CreateAsync(log);

        await transaction.CommitAsync();
    }
    catch
    {
        await transaction.RollbackAsync();
        throw;
    }
}

// 场景3：库存扣减 + 订单创建
public async Task ProcessOrderAsync(CreateOrderReq req)
{
    using var transaction = await _db.BeginTransactionAsync();
    try
    {
        // 1. 检查并扣减库存
        foreach (var item in req.Items)
        {
            var product = await _productRepository.GetByIdAsync(item.ProductId);
            if (product.Stock < item.Quantity)
                throw new BusinessException($"产品 {product.Name} 库存不足");

            product.Stock -= item.Quantity;
            await _productRepository.UpdateAsync(product);
        }

        // 2. 创建订单
        var order = req.Adapt<Order>();
        order.Id = SnowflakeHelper.NextId();
        await _orderRepository.CreateAsync(order);

        await transaction.CommitAsync();
    }
    catch
    {
        await transaction.RollbackAsync();
        throw;
    }
}
```

**事务性能优化建议：**

1. **批量操作**：使用批量插入/更新减少事务次数
2. **读写分离**：只读操作不需要事务
3. **异步操作**：使用异步方法避免阻塞
4. **连接池**：合理配置数据库连接池

###  5.3. <a name='API1'></a>4.3 API控制器实现 (1小时)

**创建产品控制器：**
```csharp
// Controllers/ProductController.cs
[Route("api/[controller]/[action]")]
[ApiController]
[Authorize]
public class ProductController : ControllerBase
{
    private readonly ProductApp _productApp;
    private readonly ILogger<ProductController> _logger;

    public ProductController(ProductApp productApp, ILogger<ProductController> logger)
    {
        _productApp = productApp;
        _logger = logger;
    }

    /// <summary>
    /// 创建产品
    /// </summary>
    /// <param name="req">创建产品请求</param>
    /// <returns>产品信息</returns>
    [HttpPost]
    public async Task<IActionResult> Create([FromBody] CreateProductReq req)
    {
        // 重要：FluentValidation会在这里自动验证req参数
        // 如果验证失败，会自动返回400 Bad Request，包含详细错误信息
        // 验证通过才会执行下面的代码

        var result = await _productApp.CreateAsync(req);
        return Ok(result);
    }

    /// <summary>
    /// 更新产品
    /// </summary>
    /// <param name="req">更新产品请求</param>
    /// <returns>产品信息</returns>
    [HttpPost]
    public async Task<IActionResult> Update([FromBody] UpdateProductReq req)
    {
        var result = await _productApp.UpdateAsync(req);
        return Ok(result);
    }

    /// <summary>
    /// 删除产品
    /// </summary>
    /// <param name="id">产品ID</param>
    /// <returns>操作结果</returns>
    [HttpPost]
    public async Task<IActionResult> Delete([FromBody] DeleteReq req)
    {
        await _productApp.DeleteAsync(req.Id);
        return Ok();
    }

    /// <summary>
    /// 根据ID获取产品
    /// </summary>
    /// <param name="id">产品ID</param>
    /// <returns>产品信息</returns>
    [HttpGet]
    public async Task<IActionResult> GetById(long id)
    {
        var result = await _productApp.GetByIdAsync(id);
        return Ok(result);
    }

    /// <summary>
    /// 获取产品分页列表
    /// </summary>
    /// <param name="req">分页查询请求</param>
    /// <returns>产品分页列表</returns>
    [HttpPost]
    public async Task<IActionResult> GetPageList([FromBody] PageListReq<ProductPageListReq> req)
    {
        var result = await _productApp.GetPagedListAsync(req);
        return Ok(result);
    }

    /// <summary>
    /// 获取低库存产品列表
    /// </summary>
    /// <returns>低库存产品列表</returns>
    [HttpGet]
    public async Task<IActionResult> GetLowStockProducts()
    {
        var result = await _productApp.GetLowStockProductsAsync();
        return Ok(result);
    }

    /// <summary>
    /// 更新产品库存
    /// </summary>
    /// <param name="req">库存更新请求</param>
    /// <returns>操作结果</returns>
    [HttpPost]
    public async Task<IActionResult> UpdateStock([FromBody] UpdateStockReq req)
    {
        await _productApp.UpdateStockAsync(req);
        return Ok();
    }

    /// <summary>
    /// 检查产品编码是否可用
    /// </summary>
    /// <param name="code">产品编码</param>
    /// <param name="excludeId">排除的产品ID（用于更新时检查）</param>
    /// <returns>是否可用</returns>
    [HttpGet]
    public async Task<IActionResult> CheckCodeAvailable(string code, long? excludeId = null)
    {
        // 这里可以调用服务层的方法检查编码可用性
        // 为了演示，简化处理
        var isAvailable = !string.IsNullOrEmpty(code) && code.Length >= 3;
        return Ok(new { available = isAvailable });
    }
}
```

###  5.4. <a name='-1'></a>4.3 请求响应模型和验证 (1小时)

**请求模型定义：**
```csharp
// Models/Request/Product/CreateProductReq.cs
public class CreateProductReq
{
    /// <summary>
    /// 分类ID
    /// </summary>
    public int CategoryId { get; set; }

    /// <summary>
    /// 产品编码
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    /// 产品名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 产品描述
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    /// 价格
    /// </summary>
    public decimal Price { get; set; }

    /// <summary>
    /// 初始库存
    /// </summary>
    public int Stock { get; set; }

    /// <summary>
    /// 最小库存
    /// </summary>
    public int MinStock { get; set; }
}

// FluentValidation验证器 - 自动验证，无需手动调用
public class CreateProductReqValidator : AbstractValidator<CreateProductReq>
{
    public CreateProductReqValidator()
    {
        // 基础数据验证 - FluentValidation负责
        RuleFor(x => x.CategoryId)
            .GreaterThan(0).WithMessage("请选择产品分类");

        RuleFor(x => x.Code)
            .NotEmpty().WithMessage("产品编码不能为空")
            .Length(3, 50).WithMessage("产品编码长度必须在3-50个字符之间")
            .Matches("^[A-Z0-9]+$").WithMessage("产品编码只能包含大写字母和数字");

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("产品名称不能为空")
            .MaximumLength(100).WithMessage("产品名称长度不能超过100个字符");

        RuleFor(x => x.Description)
            .MaximumLength(500).WithMessage("产品描述长度不能超过500个字符");

        RuleFor(x => x.Price)
            .GreaterThan(0).WithMessage("产品价格必须大于0")
            .LessThanOrEqualTo(999999.99m).WithMessage("产品价格不能超过999999.99");

        RuleFor(x => x.Stock)
            .GreaterThanOrEqualTo(0).WithMessage("库存数量不能为负数");

        RuleFor(x => x.MinStock)
            .GreaterThanOrEqualTo(0).WithMessage("最小库存不能为负数");

        // 复合验证规则
        RuleFor(x => x)
            .Must(x => x.Stock >= x.MinStock)
            .WithMessage("初始库存不能小于最小库存")
            .WithName("库存验证");
    }
}

// FluentValidation自动验证流程说明：
/*
1. 客户端发送请求到API
2. ASP.NET Core模型绑定将JSON转换为CreateProductReq对象
3. FluentValidation自动执行CreateProductReqValidator验证
4. 验证失败：自动返回400 Bad Request + 详细错误信息
5. 验证成功：继续执行Controller方法
6. Controller无需任何验证代码！

验证失败时的自动响应格式：
{
  "type": "https://tools.ietf.org/html/rfc7231#section-6.5.1",
  "title": "One or more validation errors occurred.",
  "status": 400,
  "errors": {
    "Code": ["产品编码不能为空"],
    "Price": ["产品价格必须大于0"],
    "库存验证": ["初始库存不能小于最小库存"]
  }
}
*/

// 更新请求
public class UpdateProductReq : CreateProductReq
{
    /// <summary>
    /// 产品ID
    /// </summary>
    public long Id { get; set; }
}

// 分页查询请求
public class ProductPageListReq
{
    /// <summary>
    /// 关键词搜索（产品编码或名称）
    /// </summary>
    public string Keyword { get; set; }

    /// <summary>
    /// 分类ID
    /// </summary>
    public int? CategoryId { get; set; }

    /// <summary>
    /// 产品状态
    /// </summary>
    public ProductStatus? Status { get; set; }

    /// <summary>
    /// 是否只显示低库存产品
    /// </summary>
    public bool? LowStock { get; set; }

    /// <summary>
    /// 价格范围 - 最小值
    /// </summary>
    public decimal? MinPrice { get; set; }

    /// <summary>
    /// 价格范围 - 最大值
    /// </summary>
    public decimal? MaxPrice { get; set; }

    /// <summary>
    /// 排序字段
    /// </summary>
    public string SortField { get; set; }

    /// <summary>
    /// 排序方向 ASC/DESC
    /// </summary>
    public string SortOrder { get; set; }
}

// 库存更新请求
public class UpdateStockReq
{
    /// <summary>
    /// 产品ID
    /// </summary>
    public long ProductId { get; set; }

    /// <summary>
    /// 数量
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    /// 操作类型：IN-入库，OUT-出库
    /// </summary>
    public string Operation { get; set; }

    /// <summary>
    /// 操作原因
    /// </summary>
    public string Reason { get; set; }
}

// 库存更新验证器
public class UpdateStockReqValidator : AbstractValidator<UpdateStockReq>
{
    public UpdateStockReqValidator()
    {
        RuleFor(x => x.ProductId)
            .GreaterThan(0).WithMessage("产品ID无效");

        RuleFor(x => x.Quantity)
            .GreaterThan(0).WithMessage("数量必须大于0");

        RuleFor(x => x.Operation)
            .NotEmpty().WithMessage("操作类型不能为空")
            .Must(x => x?.ToUpper() == "IN" || x?.ToUpper() == "OUT")
            .WithMessage("操作类型只能是IN或OUT");

        RuleFor(x => x.Reason)
            .NotEmpty().WithMessage("操作原因不能为空")
            .MaximumLength(200).WithMessage("操作原因长度不能超过200个字符");
    }
}
```

**响应模型定义：**
```csharp
// Models/Response/ProductDto.cs
public class ProductDto
{
    /// <summary>
    /// 产品ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 分类ID
    /// </summary>
    public int CategoryId { get; set; }

    /// <summary>
    /// 分类名称
    /// </summary>
    public string CategoryName { get; set; }

    /// <summary>
    /// 产品编码
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    /// 产品名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 产品描述
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    /// 价格
    /// </summary>
    public decimal Price { get; set; }

    /// <summary>
    /// 格式化价格显示
    /// </summary>
    public string PriceText { get; set; }

    /// <summary>
    /// 库存数量
    /// </summary>
    public int Stock { get; set; }

    /// <summary>
    /// 最小库存
    /// </summary>
    public int MinStock { get; set; }

    /// <summary>
    /// 是否低库存
    /// </summary>
    public bool IsLowStock { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public ProductStatus Status { get; set; }

    /// <summary>
    /// 状态描述
    /// </summary>
    public string StatusText { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 格式化创建时间
    /// </summary>
    public string CreateTimeText { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdateTime { get; set; }
}
```

**Mapster映射配置（自定义映射规则）：**
```csharp
// Infrastructure/Mapster/ProductMappingConfig.cs
public class ProductMappingConfig : IRegister
{
    public void Register(TypeAdapterConfig config)
    {
        // Product -> ProductDto 映射配置
        config.NewConfig<Product, ProductDto>()
            // 导航属性映射
            .Map(dest => dest.CategoryName, src => src.Category != null ? src.Category.Name : "未分类")

            // 枚举转文本映射
            .Map(dest => dest.StatusText, src => src.Status == ProductStatus.Normal ? "正常" : "停用")

            // 计算属性映射
            .Map(dest => dest.IsLowStock, src => src.Stock <= src.MinStock)

            // 格式化映射
            .Map(dest => dest.PriceText, src => $"¥{src.Price:N2}")
            .Map(dest => dest.CreateTimeText, src => src.CreateTime.ToString("yyyy-MM-dd HH:mm:ss"))

            // 条件映射
            .Map(dest => dest.PriceText,
                 src => src.Price > 1000 ? $"¥{src.Price:N2}(高价)" : $"¥{src.Price:N2}");

        // CreateProductReq -> Product 映射配置
        config.NewConfig<CreateProductReq, Product>()
            // 忽略自动设置的字段
            .Ignore(dest => dest.Id)           // 业务层设置雪花ID
            .Ignore(dest => dest.TenantId)     // 业务层设置租户ID
            .Ignore(dest => dest.CreateBy)     // 业务层设置创建人
            .Ignore(dest => dest.CreateTime)   // 业务层设置创建时间
            .Ignore(dest => dest.UpdateBy)     // 创建时不需要
            .Ignore(dest => dest.UpdateTime)   // 创建时不需要

            // 设置默认值
            .Map(dest => dest.Status, src => ProductStatus.Normal);

        // UpdateProductReq -> Product 映射配置
        config.NewConfig<UpdateProductReq, Product>()
            // 保持原有字段不变
            .Ignore(dest => dest.TenantId)     // 不允许修改租户
            .Ignore(dest => dest.CreateBy)     // 不允许修改创建人
            .Ignore(dest => dest.CreateTime)   // 不允许修改创建时间
            .Ignore(dest => dest.UpdateBy)     // 业务层设置
            .Ignore(dest => dest.UpdateTime);  // 业务层设置
    }
}

// 在Startup.cs中注册映射配置
public void ConfigureServices(IServiceCollection services)
{
    // 注册Mapster
    var config = TypeAdapterConfig.GlobalSettings;
    config.Scan(Assembly.GetExecutingAssembly()); // 自动扫描所有映射配置

    services.AddSingleton(config);
    services.AddScoped<IMapper, ServiceMapper>();
}
```

**Mapster即时自定义映射的实际应用场景：**

```csharp
// 1. 根据用户权限动态映射
public async Task<ProductDto> GetProductForUser(long productId)
{
    var product = await _productService.GetByIdAsync(productId);

    // 根据当前用户权限决定映射规则
    if (_contextUser.HasPermission("PRODUCT_VIEW_COST"))
    {
        // 有权限用户：显示成本信息
        return product.Adapt<ProductDto>(config => config
            .Map(dest => dest.CostPrice, src => src.CostPrice)
            .Map(dest => dest.ProfitMargin, src => (src.Price - src.CostPrice) / src.CostPrice * 100)
            .Map(dest => dest.PriceText, src => $"售价：¥{src.Price:N2} | 成本：¥{src.CostPrice:N2}")
        );
    }
    else
    {
        // 无权限用户：隐藏成本信息
        return product.Adapt<ProductDto>(config => config
            .Map(dest => dest.CostPrice, src => 0m)
            .Map(dest => dest.ProfitMargin, src => 0m)
            .Map(dest => dest.PriceText, src => $"售价：¥{src.Price:N2}")
        );
    }
}

// 2. 根据业务场景动态映射
public async Task<List<ProductDto>> GetProductList(ProductListType listType)
{
    var products = await _productService.GetAllAsync();

    switch (listType)
    {
        case ProductListType.Sales:
            // 销售场景：突出价格和库存
            return products.Adapt<List<ProductDto>>(config => config
                .Map(dest => dest.DisplayName, src => $"{src.Name} - ¥{src.Price}")
                .Map(dest => dest.StockStatus, src => src.Stock > src.MinStock ? "充足" : "紧张")
                .Map(dest => dest.SalesPoint, src => src.Price < 100 ? "经济实惠" : "高端产品")
            );

        case ProductListType.Production:
            // 生产场景：突出工艺和物料
            return products.Adapt<List<ProductDto>>(config => config
                .Map(dest => dest.DisplayName, src => $"{src.Code} - {src.Name}")
                .Map(dest => dest.ProductionInfo, src => $"工艺：{src.ProcessType} | 周期：{src.ProductionCycle}天")
                .Map(dest => dest.MaterialStatus, src => src.Materials.All(m => m.Stock > 0) ? "物料齐全" : "物料不足")
            );

        case ProductListType.Quality:
            // 质量场景：突出质量标准
            return products.Adapt<List<ProductDto>>(config => config
                .Map(dest => dest.DisplayName, src => $"{src.Name} ({src.QualityLevel})")
                .Map(dest => dest.QualityInfo, src => $"标准：{src.QualityStandard} | 检测项：{src.QualityCheckItems.Count}")
                .Map(dest => dest.QualityStatus, src => src.QualityScore >= 95 ? "优秀" : src.QualityScore >= 85 ? "良好" : "需改进")
            );

        default:
            return products.Adapt<List<ProductDto>>();
    }
}

// 3. 根据客户端类型动态映射
public async Task<object> GetProductForClient(long productId, ClientType clientType)
{
    var product = await _productService.GetByIdAsync(productId);

    switch (clientType)
    {
        case ClientType.Mobile:
            // 移动端：简化数据，减少流量
            return product.Adapt<ProductMobileDto>(config => config
                .Map(dest => dest.Title, src => src.Name)
                .Map(dest => dest.Price, src => src.Price)
                .Map(dest => dest.Image, src => src.Images.FirstOrDefault()?.Url ?? "")
                .Map(dest => dest.InStock, src => src.Stock > 0)
                .Ignore(dest => dest.Description) // 移动端不需要详细描述
            );

        case ClientType.Desktop:
            // 桌面端：完整数据
            return product.Adapt<ProductDto>();

        case ClientType.Api:
            // API调用：只返回必要字段
            return product.Adapt<ProductApiDto>(config => config
                .Map(dest => dest.Id, src => src.Id)
                .Map(dest => dest.Code, src => src.Code)
                .Map(dest => dest.Name, src => src.Name)
                .Map(dest => dest.Price, src => src.Price)
                .Map(dest => dest.Available, src => src.Stock > 0 && src.Status == ProductStatus.Normal)
            );

        default:
            return product.Adapt<ProductDto>();
    }
}

// 4. 根据数据状态动态映射
public async Task<ProductDto> GetProductWithStatus(long productId)
{
    var product = await _productService.GetByIdAsync(productId);
    var now = DateTime.Now;

    return product.Adapt<ProductDto>(config => config
        // 根据时间动态设置状态文本
        .Map(dest => dest.StatusText, src =>
            src.Status == ProductStatus.Normal
                ? (src.Stock <= src.MinStock ? "库存不足" : "正常")
                : "已停用")

        // 根据创建时间动态设置标签
        .Map(dest => dest.ProductTag, src =>
            (now - src.CreateTime).TotalDays <= 30 ? "新品" :
            src.Sales > 1000 ? "热销" :
            src.Stock == 0 ? "缺货" : "")

        // 根据价格动态设置折扣信息
        .Map(dest => dest.DiscountInfo, src =>
            src.OriginalPrice > src.Price
                ? $"原价¥{src.OriginalPrice:N2}，现价¥{src.Price:N2}，节省¥{(src.OriginalPrice - src.Price):N2}"
                : "")
    );
}

// 5. 条件映射的复杂场景
public async Task<List<OrderDto>> GetOrdersForDashboard(DateTime startDate, DateTime endDate)
{
    var orders = await _orderService.GetOrdersByDateRangeAsync(startDate, endDate);

    return orders.Adapt<List<OrderDto>>(config => config
        // 根据订单金额设置不同的显示样式
        .Map(dest => dest.AmountDisplay, src =>
            src.TotalAmount >= 10000 ? $"¥{src.TotalAmount:N2} (大单)" :
            src.TotalAmount >= 1000 ? $"¥{src.TotalAmount:N2}" :
            $"¥{src.TotalAmount:N2} (小单)")

        // 根据订单状态和时间计算紧急程度
        .Map(dest => dest.UrgencyLevel, src =>
            src.Status == OrderStatus.Pending && (DateTime.Now - src.CreateTime).TotalHours > 24 ? "紧急" :
            src.Status == OrderStatus.Processing && (DateTime.Now - src.StartTime).TotalDays > 7 ? "延期" :
            "正常")

        // 根据客户类型设置不同的客户标识
        .Map(dest => dest.CustomerDisplay, src =>
            src.Customer.Type == CustomerType.VIP ? $"⭐ {src.Customer.Name}" :
            src.Customer.Type == CustomerType.Enterprise ? $"🏢 {src.Customer.Name}" :
            src.Customer.Name)
    );
}

// 6. 嵌套对象的条件映射
public async Task<OrderDetailDto> GetOrderDetail(long orderId, bool includeInternal = false)
{
    var order = await _orderService.GetByIdAsync(orderId);

    return order.Adapt<OrderDetailDto>(config => config
        // 根据参数决定是否包含内部信息
        .Map(dest => dest.InternalNotes, src => includeInternal ? src.InternalNotes : "")
        .Map(dest => dest.CostInfo, src => includeInternal ? src.CostBreakdown : null)

        // 订单项的条件映射
        .Map(dest => dest.Items, src => src.OrderItems.Select(item =>
            item.Adapt<OrderItemDto>(itemConfig => itemConfig
                .Map(itemDest => itemDest.PriceDisplay, itemSrc =>
                    includeInternal
                        ? $"售价：¥{itemSrc.UnitPrice:N2} | 成本：¥{itemSrc.CostPrice:N2}"
                        : $"¥{itemSrc.UnitPrice:N2}")
            )).ToList())
    );
}
```

**即时映射 vs 全局映射的使用场景：**

| 场景 | 使用方式 | 示例 |
|------|----------|------|
| **固定规则** | 全局映射配置 | 实体到DTO的标准转换 |
| **权限相关** | 即时映射 | 根据用户权限显示不同字段 |
| **业务场景** | 即时映射 | 销售/生产/质量等不同场景 |
| **客户端适配** | 即时映射 | 移动端/桌面端/API的不同需求 |
| **动态计算** | 即时映射 | 根据实时数据计算显示内容 |
| **条件显示** | 即时映射 | 根据状态/时间等条件显示 |

---

##  6. <a name='3'></a>🚀 第五部分：高级特性与最佳实践 (3小时)

###  6.1. <a name='-1'></a>5.1 缓存策略深度应用 (1小时)

**缓存键设计规范：**
```csharp
// Models/Const/CacheKey.cs - 扩展产品相关缓存键
public static class CacheKey
{
    // 基础缓存键
    public static string Product(long id) => $"product:{id}";
    public static string ProductByCode(string code, int tenantId) => $"product:code:{code}:tenant:{tenantId}";

    // 列表缓存键
    public static string ProductList(int tenantId, string hash) => $"product:list:tenant:{tenantId}:hash:{hash}";
    public static string LowStockProducts(int tenantId) => $"product:lowstock:tenant:{tenantId}";
    public static string ProductsByCategory(int categoryId) => $"product:category:{categoryId}";

    // 统计缓存键
    public static string ProductCount(int tenantId) => $"product:count:tenant:{tenantId}";
    public static string ProductStockValue(int tenantId) => $"product:stockvalue:tenant:{tenantId}";

    // 过期时间常量
    public static readonly TimeSpan ProductExpired = TimeSpan.FromHours(1);
    public static readonly TimeSpan ProductListExpired = TimeSpan.FromMinutes(30);
    public static readonly TimeSpan ProductStatsExpired = TimeSpan.FromMinutes(15);
}
```

**多级缓存策略：**
```csharp
public class ProductCacheService
{
    private readonly ICacheService _distributedCache; // Redis
    private readonly IMemoryCache _localCache;        // 本地缓存
    private readonly ProductRepository _repository;

    public async Task<Product> GetProductAsync(long id)
    {
        var cacheKey = CacheKey.Product(id);

        // 1. 先查本地缓存（最快）
        if (_localCache.TryGetValue(cacheKey, out Product localProduct))
        {
            return localProduct;
        }

        // 2. 查分布式缓存（较快）
        var distributedProduct = await _distributedCache.GetAsync<Product>(cacheKey);
        if (distributedProduct != null)
        {
            // 写入本地缓存
            _localCache.Set(cacheKey, distributedProduct, TimeSpan.FromMinutes(5));
            return distributedProduct;
        }

        // 3. 查数据库（最慢）
        var dbProduct = await _repository.GetByIdAsync(id);
        if (dbProduct != null)
        {
            // 写入分布式缓存
            await _distributedCache.SetAsync(cacheKey, dbProduct, CacheKey.ProductExpired);

            // 写入本地缓存
            _localCache.Set(cacheKey, dbProduct, TimeSpan.FromMinutes(5));
        }

        return dbProduct;
    }

    public async Task InvalidateProductAsync(long id)
    {
        var cacheKey = CacheKey.Product(id);

        // 清除本地缓存
        _localCache.Remove(cacheKey);

        // 清除分布式缓存
        await _distributedCache.RemoveAsync(cacheKey);

        // 清除相关的列表缓存
        await _distributedCache.RemoveByPatternAsync("product:list:*");
        await _distributedCache.RemoveByPatternAsync("product:lowstock:*");
    }
}
```

**缓存预热策略：**
```csharp
public class ProductCacheWarmupService : IHostedService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<ProductCacheWarmupService> _logger;
    private Timer _timer;

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        // 应用启动时预热缓存
        await WarmupCacheAsync();

        // 定时预热（每小时一次）
        _timer = new Timer(async _ => await WarmupCacheAsync(), null,
            TimeSpan.Zero, TimeSpan.FromHours(1));
    }

    private async Task WarmupCacheAsync()
    {
        using var scope = _serviceProvider.CreateScope();
        var productService = scope.ServiceProvider.GetRequiredService<ProductService>();
        var cacheService = scope.ServiceProvider.GetRequiredService<ICacheService>();

        try
        {
            // 预热热门产品
            var hotProducts = await productService.GetHotProductsAsync();
            foreach (var product in hotProducts)
            {
                var cacheKey = CacheKey.Product(product.Id);
                await cacheService.SetAsync(cacheKey, product, CacheKey.ProductExpired);
            }

            _logger.LogInformation("缓存预热完成，预热产品数量: {Count}", hotProducts.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "缓存预热失败");
        }
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        _timer?.Dispose();
        return Task.CompletedTask;
    }
}
```

###  6.2. <a name='-1'></a>5.2 性能优化实战技巧 (1小时)

**数据库查询优化：**
```csharp
// 错误示例：N+1查询问题
public async Task<List<ProductDto>> GetProductsWithCategoryBad()
{
    var products = await _db.Queryable<Product>().ToListAsync();
    var result = new List<ProductDto>();

    foreach (var product in products) // N+1问题：每个产品都查询一次分类
    {
        var category = await _db.Queryable<ProductCategory>()
            .FirstAsync(x => x.Id == product.CategoryId);

        result.Add(new ProductDto
        {
            Id = product.Id,
            Name = product.Name,
            CategoryName = category.Name // 每次都查询数据库
        });
    }

    return result;
}

// 正确示例：使用JOIN一次查询
public async Task<List<ProductDto>> GetProductsWithCategoryGood()
{
    return await _db.Queryable<Product>()
        .LeftJoin<ProductCategory>((p, c) => p.CategoryId == c.Id)
        .Select((p, c) => new ProductDto
        {
            Id = p.Id,
            Name = p.Name,
            CategoryName = c.Name // 一次查询获取所有数据
        })
        .ToListAsync();
}

// 批量查询优化
public async Task<List<ProductDto>> GetProductsWithCategoryBatch(List<long> productIds)
{
    // 1. 批量查询产品
    var products = await _db.Queryable<Product>()
        .Where(x => productIds.Contains(x.Id))
        .ToListAsync();

    // 2. 获取所有分类ID
    var categoryIds = products.Select(x => x.CategoryId).Distinct().ToList();

    // 3. 批量查询分类
    var categories = await _db.Queryable<ProductCategory>()
        .Where(x => categoryIds.Contains(x.Id))
        .ToListAsync();

    // 4. 内存中组装数据
    var categoryDict = categories.ToDictionary(x => x.Id, x => x.Name);

    return products.Select(p => new ProductDto
    {
        Id = p.Id,
        Name = p.Name,
        CategoryName = categoryDict.GetValueOrDefault(p.CategoryId, "未知分类")
    }).ToList();
}
```

**异步编程最佳实践：**
```csharp
// 错误示例：同步等待异步方法
public ProductDto GetProductSync(long id)
{
    // 错误：会导致死锁
    var product = _productService.GetByIdAsync(id).Result;
    return product.Adapt<ProductDto>();
}

// 正确示例：全链路异步
public async Task<ProductDto> GetProductAsync(long id)
{
    var product = await _productService.GetByIdAsync(id);
    return product.Adapt<ProductDto>();
}

// 并行处理优化
public async Task<List<ProductDto>> GetProductsParallel(List<long> productIds)
{
    // 错误：串行处理
    var products = new List<Product>();
    foreach (var id in productIds)
    {
        var product = await _productService.GetByIdAsync(id); // 串行，慢
        products.Add(product);
    }

    // 正确：并行处理
    var tasks = productIds.Select(id => _productService.GetByIdAsync(id));
    var parallelProducts = await Task.WhenAll(tasks); // 并行，快

    return parallelProducts.Adapt<List<ProductDto>>();
}
```

###  6.3. <a name='-1'></a>5.3 错误处理和日志记录 (1小时)

**统一异常处理中间件：**
```csharp
public class GlobalExceptionMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<GlobalExceptionMiddleware> _logger;

    public GlobalExceptionMiddleware(RequestDelegate next, ILogger<GlobalExceptionMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            await HandleExceptionAsync(context, ex);
        }
    }

    private async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        var response = context.Response;
        response.ContentType = "application/json";

        var apiResult = new ApiResult();

        switch (exception)
        {
            case BusinessException businessEx:
                // 业务异常：返回具体错误信息
                apiResult.code = ApiStatusCode.LogicException;
                apiResult.message = businessEx.Message;
                response.StatusCode = 200; // 业务异常仍返回200
                _logger.LogWarning("业务异常: {Message} | 请求路径: {Path}",
                    businessEx.Message, context.Request.Path);
                break;

            case NotFoundException notFoundEx:
                apiResult.code = ApiStatusCode.Notfound;
                apiResult.message = notFoundEx.Message;
                response.StatusCode = 404;
                break;

            case UnauthorizedException unauthorizedEx:
                apiResult.code = ApiStatusCode.Unauthorized;
                apiResult.message = "用户未登录或登录已过期";
                response.StatusCode = 401;
                break;

            case ForbiddenException forbiddenEx:
                apiResult.code = ApiStatusCode.NonPermission;
                apiResult.message = "权限不足";
                response.StatusCode = 403;
                break;

            case ValidationException validationEx:
                // FluentValidation验证异常
                apiResult.code = ApiStatusCode.LogicException;
                apiResult.message = string.Join("; ", validationEx.Errors.Select(e => e.ErrorMessage));
                response.StatusCode = 200;
                break;

            default:
                // 系统异常：记录详细日志，返回通用错误信息
                apiResult.code = ApiStatusCode.LogicException;
                apiResult.message = "系统内部错误，请稍后重试";
                response.StatusCode = 500;

                _logger.LogError(exception, "系统异常: {Message} | 请求路径: {Path} | 用户: {User}",
                    exception.Message, context.Request.Path, context.User?.Identity?.Name);
                break;
        }

        var jsonResult = JsonSerializer.Serialize(apiResult, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        await response.WriteAsync(jsonResult);
    }
}
```

**结构化日志记录：**
```csharp
public class ProductService
{
    private readonly ILogger<ProductService> _logger;

    public async Task<Product> CreateAsync(Product product)
    {
        // 使用结构化日志，便于查询和分析
        using var scope = _logger.BeginScope(new Dictionary<string, object>
        {
            ["ProductCode"] = product.Code,
            ["TenantId"] = product.TenantId,
            ["UserId"] = _contextUser.UserId
        });

        _logger.LogInformation("开始创建产品: {ProductCode}", product.Code);

        try
        {
            await ValidateProductAsync(product);

            product.Id = SnowflakeHelper.NextId();
            product.CreateTime = DateTime.Now;

            await _productRepository.CreateAsync(product);

            // 记录成功日志
            _logger.LogInformation("产品创建成功: {ProductCode} | 产品ID: {ProductId} | 耗时: {ElapsedMs}ms",
                product.Code, product.Id, stopwatch.ElapsedMilliseconds);

            return product;
        }
        catch (BusinessException ex)
        {
            // 记录业务异常
            _logger.LogWarning("产品创建失败 - 业务异常: {ErrorMessage} | 产品编码: {ProductCode}",
                ex.Message, product.Code);
            throw;
        }
        catch (Exception ex)
        {
            // 记录系统异常
            _logger.LogError(ex, "产品创建失败 - 系统异常: {ErrorMessage} | 产品编码: {ProductCode}",
                ex.Message, product.Code);
            throw;
        }
    }
}
```

---


**🎉 恭喜完成CYSF框架培训！**

现在你已经具备了使用CYSF框架进行企业级应用开发的基础能力。记住：

- 💻 **多动手实践**：理论结合实际，在项目中学习
- 🤔 **多思考总结**：遇到问题要深入思考原因和解决方案
- 🤝 **多交流讨论**：与团队成员分享经验，共同进步
- 📚 **持续学习**：技术在不断发展，保持学习的热情

**祝愿你在软件开发的道路上越走越远，成为优秀的企业级应用开发工程师！** 🚀
```
```